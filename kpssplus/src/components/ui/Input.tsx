import React, {useState} from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, layout, spacing} from '../../theme';

export interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: string;
  rightIcon?: string;
  onRightIconPress?: () => void;
  containerStyle?: ViewStyle;
  inputStyle?: ViewStyle;
  variant?: 'default' | 'outlined' | 'filled';
  size?: 'small' | 'medium' | 'large';
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  leftIcon,
  rightIcon,
  onRightIconPress,
  containerStyle,
  inputStyle,
  variant = 'outlined',
  size = 'medium',
  secureTextEntry,
  ...props
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [isSecure, setIsSecure] = useState(secureTextEntry);

  const containerStyles = [styles.container, containerStyle];

  const inputContainerStyles = [
    styles.inputContainer,
    styles[variant],
    styles[size],
    isFocused && styles.focused,
    error && styles.error,
  ].filter(Boolean);

  const inputStyles = [
    styles.input,
    styles[`${size}Input`],
    leftIcon && styles.inputWithLeftIcon,
    (rightIcon || secureTextEntry) && styles.inputWithRightIcon,
    inputStyle,
  ].filter(Boolean);

  const handleRightIconPress = () => {
    if (secureTextEntry) {
      setIsSecure(!isSecure);
    } else if (onRightIconPress) {
      onRightIconPress();
    }
  };

  return (
    <View style={containerStyles}>
      {label && <Text style={styles.label}>{label}</Text>}

      <View style={StyleSheet.flatten(inputContainerStyles) as ViewStyle}>
        {leftIcon && (
          <Icon
            name={leftIcon}
            size={layout.iconMedium}
            color={colors.text.tertiary}
            style={styles.leftIcon}
          />
        )}

        <TextInput
          style={StyleSheet.flatten(inputStyles) as TextStyle}
          placeholderTextColor={colors.input.placeholder}
          secureTextEntry={isSecure}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />

        {(rightIcon || secureTextEntry) && (
          <TouchableOpacity
            onPress={handleRightIconPress}
            style={styles.rightIcon}>
            <Icon
              name={
                secureTextEntry
                  ? isSecure
                    ? 'eye-off-outline'
                    : 'eye-outline'
                  : rightIcon || 'close-outline'
              }
              size={layout.iconMedium}
              color={colors.text.tertiary}
            />
          </TouchableOpacity>
        )}
      </View>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: spacing[4],
  },
  label: {
    ...typography.inputLabel,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: layout.inputRadius,
  },
  input: {
    flex: 1,
    ...typography.input,
  },

  // Variants
  default: {
    backgroundColor: colors.input.background,
    borderBottomWidth: 1,
    borderBottomColor: colors.input.border,
  },
  outlined: {
    backgroundColor: colors.input.background,
    borderWidth: 1,
    borderColor: colors.input.border,
  },
  filled: {
    backgroundColor: colors.gray[100],
    borderWidth: 0,
    borderRadius: 12,
  },

  // Sizes
  small: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    minHeight: 36,
  },
  medium: {
    paddingHorizontal: layout.inputPadding,
    paddingVertical: spacing[3],
    minHeight: 48,
  },
  large: {
    paddingHorizontal: layout.inputPadding,
    paddingVertical: spacing[4],
    minHeight: 56,
  },

  // Input sizes
  smallInput: {
    fontSize: 14,
  },
  mediumInput: {
    fontSize: 16,
  },
  largeInput: {
    fontSize: 18,
  },

  // States
  focused: {
    borderColor: colors.input.borderFocus,
    borderWidth: 2,
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  error: {
    borderColor: colors.error,
  },

  // Icons
  leftIcon: {
    marginRight: spacing[2],
  },
  rightIcon: {
    marginLeft: spacing[2],
    padding: spacing[1],
  },
  inputWithLeftIcon: {
    paddingLeft: 0,
  },
  inputWithRightIcon: {
    paddingRight: 0,
  },

  // Error text
  errorText: {
    ...typography.inputError,
  },
});
