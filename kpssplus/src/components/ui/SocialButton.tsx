import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, spacing} from '../../theme';

export interface SocialButtonProps {
  provider: 'google' | 'apple' | 'facebook' | 'twitter';
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
  fullWidth?: boolean;
}

const providerConfig = {
  google: {
    icon: 'logo-google',
    title: 'Google',
    backgroundColor: colors.white,
    borderColor: colors.gray[300],
    textColor: colors.text.primary,
  },
  apple: {
    icon: 'logo-apple',
    title: 'Apple',
    backgroundColor: colors.black,
    borderColor: colors.black,
    textColor: colors.white,
  },
  facebook: {
    icon: 'logo-facebook',
    title: 'Facebook',
    backgroundColor: '#1877F2',
    borderColor: '#1877F2',
    textColor: colors.white,
  },
  twitter: {
    icon: 'logo-twitter',
    title: 'Twitter',
    backgroundColor: '#1DA1F2',
    borderColor: '#1DA1F2',
    textColor: colors.white,
  },
};

export const SocialButton: React.FC<SocialButtonProps> = ({
  provider,
  onPress,
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}) => {
  const config = providerConfig[provider];

  const buttonStyle = [
    styles.base,
    {
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
    },
    fullWidth && styles.fullWidth,
    disabled && styles.disabled,
    style,
  ];

  const textStyleCombined = [
    styles.text,
    {color: config.textColor},
    disabled && styles.disabledText,
    textStyle,
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}>
      {loading ? (
        <ActivityIndicator color={config.textColor} size="small" />
      ) : (
        <>
          <Icon
            name={config.icon}
            size={24}
            color={config.textColor}
            style={styles.icon}
          />
          <Text style={textStyleCombined}>{config.title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  base: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    borderWidth: 1,
    paddingVertical: spacing[3],
    paddingHorizontal: spacing[4],
    minHeight: 48,
  },
  fullWidth: {
    width: '100%',
  },
  icon: {
    marginRight: spacing[2],
  },
  text: {
    ...typography.button,
    fontWeight: '600',
  },
  disabled: {
    opacity: 0.5,
  },
  disabledText: {
    opacity: 0.5,
  },
});
