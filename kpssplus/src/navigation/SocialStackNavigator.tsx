import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {SocialStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {SocialHomeScreen} from '../screens/social/SocialHomeScreen';
import {TimelineScreen} from '../screens/social/TimelineScreen';
import {ProfileScreen} from '../screens/menu/ProfileScreen';
import {FriendsScreen} from '../screens/social/FriendsScreen';
import {FriendRequestsScreen} from '../screens/social/FriendRequestsScreen';
import {UserSearchScreen} from '../screens/social/UserSearchScreen';
import {PostDetailScreen} from '../screens/social/PostDetailScreen';
import {CreatePostScreen} from '../screens/social/CreatePostScreen';
import {LeaderboardScreen} from '../screens/social/LeaderboardScreen';

const Stack = createStackNavigator<SocialStackParamList>();

export const SocialStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="SocialHome"
        component={SocialHomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Timeline"
        component={TimelineScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Friends"
        component={FriendsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="FriendRequests"
        component={FriendRequestsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="UserSearch"
        component={UserSearchScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="PostDetail"
        component={PostDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="CreatePost"
        component={CreatePostScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Leaderboard"
        component={LeaderboardScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
