import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {MenuStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {MenuHomeScreen} from '../screens/menu/MenuHomeScreen';
import {ProfileScreen} from '../screens/menu/ProfileScreen';
import {SettingsScreen} from '../screens/menu/SettingsScreen';
import {BadgesScreen} from '../screens/menu/BadgesScreen';
import {AnalyticsScreen} from '../screens/menu/AnalyticsScreen';
import {ProgressScreen} from '../screens/menu/ProgressScreen';
import {GoalsScreen} from '../screens/menu/GoalsScreen';
import {NotificationsScreen} from '../screens/menu/NotificationsScreen';
import {HelpScreen} from '../screens/menu/HelpScreen';
import {AboutScreen} from '../screens/menu/AboutScreen';

const Stack = createStackNavigator<MenuStackParamList>();

export const MenuStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="MenuHome"
        component={MenuHomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Settings"
        component={SettingsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Badges"
        component={BadgesScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Analytics"
        component={AnalyticsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Progress"
        component={ProgressScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={NotificationsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Help"
        component={HelpScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="About"
        component={AboutScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
