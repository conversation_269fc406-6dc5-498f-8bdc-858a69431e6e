import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {ModalStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {LoginModalScreen} from '../screens/modals/LoginModalScreen';
import {GuestLimitModalScreen} from '../screens/modals/GuestLimitModalScreen';
import {ShareModalScreen} from '../screens/modals/ShareModalScreen';
import {FilterModalScreen} from '../screens/modals/FilterModalScreen';
import {SearchModalScreen} from '../screens/modals/SearchModalScreen';

// Wrapper components for navigation compatibility
const LoginModalWrapper = () => (
  <LoginModalScreen
    visible={true}
    onClose={() => {}}
    onLogin={() => {}}
    onForgotPassword={() => {}}
    onRegister={() => {}}
  />
);

const GuestLimitModalWrapper = () => (
  <GuestLimitModalScreen
    visible={true}
    onClose={() => {}}
    onLogin={() => {}}
    onRegister={() => {}}
  />
);

const ShareModalWrapper = () => (
  <ShareModalScreen
    visible={true}
    onClose={() => {}}
    content={{
      title: 'Paylaş',
      description: 'İçeriği paylaş',
    }}
  />
);

const FilterModalWrapper = () => (
  <FilterModalScreen visible={true} onClose={() => {}} onApply={() => {}} />
);

const SearchModalWrapper = () => (
  <SearchModalScreen visible={true} onClose={() => {}} onSearch={() => {}} />
);

const Stack = createStackNavigator<ModalStackParamList>();

export const ModalStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
        presentation: 'modal',
      }}>
      <Stack.Screen
        name="LoginModal"
        component={LoginModalWrapper}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="GuestLimitModal"
        component={GuestLimitModalWrapper}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ShareModal"
        component={ShareModalWrapper}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="FilterModal"
        component={FilterModalWrapper}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="SearchModal"
        component={SearchModalWrapper}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
