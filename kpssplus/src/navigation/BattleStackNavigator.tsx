import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {BattleStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens
import {BattleHomeScreen} from '../screens/battle/BattleHomeScreen';
import {GroupBattleScreen} from '../screens/battle/GroupBattleScreen';
import {FriendChallengeScreen} from '../screens/battle/FriendChallengeScreen';

const Stack = createStackNavigator<BattleStackParamList>();

export const BattleStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="BattleHome"
        component={BattleHomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="GroupBattle"
        component={GroupBattleScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="FriendChallenge"
        component={FriendChallengeScreen}
        options={{
          headerShown: false,
        }}
      />
      {/* Placeholder screens - these would be implemented later */}
      <Stack.Screen
        name="GroupBattleDetail"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="CreateGroupBattle"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="JoinBattleByCode"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ChallengeDetail"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="SendChallenge"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="BattleStats"
        component={BattleHomeScreen} // Placeholder
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
