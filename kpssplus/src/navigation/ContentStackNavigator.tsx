import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {ContentStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {ContentHomeScreen} from '../screens/content/ContentHomeScreen';
import {ContentDetailScreen} from '../screens/content/ContentDetailScreen';
import {ContentPlayerScreen} from '../screens/content/ContentPlayerScreen';
import {ContentSearchScreen} from '../screens/content/ContentSearchScreen';
import {ContentLibraryScreen} from '../screens/content/ContentLibraryScreen';
import {TopicDetailScreen} from '../screens/content/TopicDetailScreen';
import {TopicListScreen} from '../screens/content/TopicListScreen';
import {StudyTopicsScreen} from '../screens/content/StudyTopicsScreen';

const Stack = createStackNavigator<ContentStackParamList>();

export const ContentStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="ContentHome"
        component={ContentHomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="StudyTopics"
        component={StudyTopicsScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ContentDetail"
        component={ContentDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ContentPlayer"
        component={ContentPlayerScreen}
        options={{
          headerShown: false, // Full screen player
        }}
      />
      <Stack.Screen
        name="ContentSearch"
        component={ContentSearchScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ContentLibrary"
        component={ContentLibraryScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="TopicDetail"
        component={TopicDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="TopicList"
        component={TopicListScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
