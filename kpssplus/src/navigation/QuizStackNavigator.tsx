import React from 'react';
import {createStackNavigator} from '@react-navigation/stack';
import {QuizStackParamList} from './types';
import {colors} from '../theme/colors';

// Import screens (will be created later)
import {QuizHomeScreen} from '../screens/quiz/QuizHomeScreen';
import {QuizDetailScreen} from '../screens/quiz/QuizDetailScreen';
import {QuizPlayScreen} from '../screens/quiz/QuizPlayScreen';
import {QuizResultScreen} from '../screens/quiz/QuizResultScreen';
import {QuizSearchScreen} from '../screens/quiz/QuizSearchScreen';
import {QuizLeaderboardScreen} from '../screens/quiz/QuizLeaderboardScreen';

const Stack = createStackNavigator<QuizStackParamList>();

export const QuizStackNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: {
          backgroundColor: colors.background,
        },
      }}>
      <Stack.Screen
        name="QuizHome"
        component={QuizHomeScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="QuizDetail"
        component={QuizDetailScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="QuizPlay"
        component={QuizPlayScreen}
        options={{
          headerShown: false, // Full screen quiz
        }}
      />
      <Stack.Screen
        name="QuizResult"
        component={QuizResultScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="QuizSearch"
        component={QuizSearchScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="QuizLeaderboard"
        component={QuizLeaderboardScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};
