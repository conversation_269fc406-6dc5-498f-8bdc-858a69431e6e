// KPSS Plus Mobile Design Colors - Exact match from mobile-design
export const colors = {
  // Primary Colors - Mobile design'dan <PERSON><PERSON><PERSON> renkler
  primary: '#1e13ec', // Ana mavi renk (badges, progress screens)
  primaryLight: '#4A47A3', // Dashboard'daki primary
  primaryDark: '#1610d4',
  primaryMuted: '#e8e7f3', // Primary background açık versiyonu

  // Secondary Colors
  secondary: '#9CA3AF', // Secondary text color

  // Accent Colors - Dashboard'dan
  accent: {
    orange: '#FF914D', // Accent orange
    mint: '#2DD4BF', // Mint
    pink: '#F472B6', // Pink
  },

  // Neutral Colors
  white: '#FFFFFF',
  black: '#000000',
  background: '#F9FAFB', // Ana background (dashboard)
  backgroundAlt: '#fcfcff', // Progress screen background
  surface: '#FFFFFF',

  // Gray Scale - Mobile design uyumlu
  gray: {
    50: '#F9FAFB',
    100: '#F3F4F6',
    200: '#E5E7EB',
    300: '#D1D5DB',
    400: '#9CA3AF',
    500: '#6B7280',
    600: '#4B5563',
    700: '#374151',
    800: '#1F2937',
    900: '#111827',
  },

  // Status Colors
  success: '#10B981',
  successLight: '#34D399',
  successDark: '#059669',
  successMuted: '#D1FAE5',

  warning: '#F59E0B',
  warningLight: '#FBBF24',
  warningDark: '#D97706',
  warningMuted: '#FEF3C7',

  error: '#EF4444',
  errorLight: '#F87171',
  errorDark: '#DC2626',
  errorMuted: '#FEE2E2',

  info: '#3B82F6',
  infoLight: '#60A5FA',
  infoDark: '#2563EB',
  infoMuted: '#DBEAFE',

  // Quiz Colors
  quiz: {
    correct: '#10B981',
    incorrect: '#EF4444',
    unanswered: '#9CA3AF',
    selected: '#FF6B35',
  },

  // Progress Colors
  progress: {
    beginner: '#10B981',
    intermediate: '#F59E0B',
    advanced: '#EF4444',
  },

  // Social Colors
  social: {
    like: '#EF4444',
    comment: '#3B82F6',
    share: '#10B981',
    follow: '#FF6B35',
  },

  // Badge Colors
  badge: {
    common: '#9CA3AF',
    rare: '#3B82F6',
    epic: '#8B5CF6',
    legendary: '#F59E0B',
  },

  // Overlay Colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',

  // Border Colors
  border: '#E5E7EB',
  borderLight: '#F3F4F6',
  borderDark: '#D1D5DB',

  // Text Colors - Mobile design'dan alınan
  text: {
    primary: '#0e0d1b', // Ana text rengi (progress screen)
    secondary: '#374151', // Body text (dashboard)
    tertiary: '#9CA3AF', // Secondary text
    inverse: '#FFFFFF',
    headings: '#1F2937', // Headings (dashboard)
    link: '#1e13ec',
    success: '#059669',
    warning: '#D97706',
    error: '#DC2626',
  },

  // Card Colors
  card: {
    background: '#FFFFFF',
    border: '#E5E7EB',
    shadow: 'rgba(0, 0, 0, 0.1)',
  },

  // Input Colors
  input: {
    background: '#FFFFFF',
    border: '#D1D5DB',
    borderFocus: '#FF6B35',
    placeholder: '#9CA3AF',
    text: '#111827',
  },

  // Button Colors
  button: {
    primary: '#FF6B35',
    primaryHover: '#E55A2B',
    primaryDisabled: '#FFE5DC',
    secondary: '#F3F4F6',
    secondaryHover: '#E5E7EB',
    ghost: 'transparent',
    ghostHover: '#F3F4F6',
  },
} as const;

// Color utility functions
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // Convert hex to rgba
  const hex = color.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const getDifficultyColor = (
  difficulty:
    | 'easy'
    | 'medium'
    | 'hard'
    | 'beginner'
    | 'intermediate'
    | 'advanced',
): string => {
  switch (difficulty) {
    case 'easy':
    case 'beginner':
      return colors.progress.beginner;
    case 'medium':
    case 'intermediate':
      return colors.progress.intermediate;
    case 'hard':
    case 'advanced':
      return colors.progress.advanced;
    default:
      return colors.gray[500];
  }
};

export const getBadgeColor = (
  rarity: 'common' | 'rare' | 'epic' | 'legendary',
): string => {
  return colors.badge[rarity];
};
