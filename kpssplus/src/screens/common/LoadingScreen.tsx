import React, {useEffect, useRef} from 'react';
import {View, StyleSheet, Text, Animated} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography} from '../../theme';

export const LoadingScreen: React.FC = () => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Pulse animation for the icon
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 0.5,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]),
    );

    // Progress bar animation
    const progressAnimation = Animated.loop(
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1500,
        useNativeDriver: false,
      }),
    );

    pulseAnimation.start();
    progressAnimation.start();

    return () => {
      pulseAnimation.stop();
      progressAnimation.stop();
    };
  }, [pulseAnim, progressAnim]);

  const progressTranslateX = progressAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [-200, 0, 200],
  });

  const progressScaleX = progressAnim.interpolate({
    inputRange: [0, 0.5, 1],
    outputRange: [0.5, 0.75, 0.5],
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Animated.View style={[styles.iconContainer, {opacity: pulseAnim}]}>
          <Icon name="checkmark-circle" size={96} color={colors.primary} />
        </Animated.View>

        <Text style={styles.title}>KPSS Prep</Text>
        <Text style={styles.subtitle}>Hazırlanıyor...</Text>

        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <Animated.View
              style={[
                styles.progressFill,
                {
                  transform: [
                    {translateX: progressTranslateX},
                    {scaleX: progressScaleX},
                  ],
                },
              ]}
            />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8fc', // Light background from design
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  iconContainer: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0e0d1b', // Text main from design
    marginBottom: 8,
    letterSpacing: -0.5,
    ...typography.h1,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: '500',
    color: '#504c9a', // Text secondary from design
    marginBottom: 32,
  },
  progressContainer: {
    width: '100%',
    maxWidth: 300,
    paddingHorizontal: 16,
  },
  progressBar: {
    width: '100%',
    height: 10,
    backgroundColor: '#d0cfe7', // Progress bg from design
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 5,
    width: '100%',
  },
});
