import React, {useMemo, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useAuth} from '../../hooks/useAuth';

export const DashboardScreen: React.FC = React.memo(() => {
  const {user, isAuthenticated} = useAuth();

  // Memoize user display name
  const displayName = useMemo(() => {
    return isAuthenticated ? user?.firstName || 'User' : 'Sophia';
  }, [isAuthenticated, user?.firstName]);

  // Memoize avatar URI
  const avatarUri = useMemo(() => {
    return (
      user?.avatar ||
      'https://lh3.googleusercontent.com/aida-public/AB6AXuC8N8lYOBgQ70sRNkbs2LhmuNmLjK4ik5KQX5bpor-zVcMH6Z5aCgBLuTuOSO7odbOEklv6Bmu7foDbhvkDkKCtIN_edoXtUD68kbAgXyHDSt_nKe7ifg26PHh5JnkhcfL6CThylxAhVLrbTZdHaBu96CiLAFgT-wjfph1mWUQ2r-xJnxPK1h7sDEyvbblnXTXunSknEI6zrs40F1Og_RgB_kik6I6uILTFXTagr-rK1ShNZ7E2BEDB6R3J0-aU8GneRzNF7D08ZgY'
    );
  }, [user?.avatar]);

  // Memoize handlers
  const handleNotificationPress = useCallback(() => {
    console.log('Open notifications');
  }, []);

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Header - Mobile design'dan birebir */}
        <View style={styles.header}>
          <View style={styles.userSection}>
            <View style={styles.avatar}>
              <Image source={{uri: avatarUri}} style={styles.avatarImage} />
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.welcomeText}>Welcome back,</Text>
              <Text style={styles.userName}>{displayName}!</Text>
            </View>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity
              style={styles.notificationButton}
              onPress={handleNotificationPress}>
              <Icon name="notifications" size={24} color="#1F2937" />
            </TouchableOpacity>
            <View style={styles.streakBadge}>
              <Icon name="local-fire-department" size={18} color="#FF914D" />
              <Text style={styles.streakText}>3</Text>
            </View>
          </View>
        </View>

        {/* Stats Card - Mobile design'dan birebir */}
        <View style={styles.statsCard}>
          <View style={styles.statsHeader}>
            <View style={styles.rankSection}>
              <Text style={styles.rankLabel}>Your Rank</Text>
              <Text style={styles.rankValue}>12</Text>
            </View>
            <View style={styles.pointsSection}>
              <View style={styles.pointsRow}>
                <Icon name="monetization-on" size={20} color="#FF914D" />
                <Text style={styles.pointsText}>1200 Coins</Text>
              </View>
              <View style={styles.pointsRow}>
                <Icon name="star" size={20} color="#2DD4BF" />
                <Text style={styles.pointsText}>2500 Points</Text>
              </View>
            </View>
          </View>
          <View style={styles.progressSection}>
            <Text style={styles.progressLabel}>Weekly Progress</Text>
            <View style={styles.progressBar}>
              <View style={[styles.progressFill, styles.progressWidth]} />
            </View>
          </View>
        </View>

        {/* Daily Questions Card - Mobile design'dan birebir */}
        <View style={styles.dailyCard}>
          <View style={styles.dailyContent}>
            <View style={styles.dailyInfo}>
              <Text style={styles.dailyTitle}>Daily Questions</Text>
              <Text style={styles.dailySubtitle}>
                Complete 5 questions to earn 50 coins!
              </Text>
            </View>
            <View style={styles.dailyIcon}>
              <Icon name="lightbulb" size={32} color="#FFFFFF" />
            </View>
          </View>
          <TouchableOpacity style={styles.startButton}>
            <Text style={styles.startButtonText}>Start Now</Text>
            <Icon name="arrow-forward" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Engage & Compete Section - Mobile design'dan birebir */}
        <View style={styles.engageSection}>
          <Text style={styles.sectionTitle}>Engage & Compete</Text>
          <View style={styles.battleGrid}>
            <View style={styles.battleCard}>
              <View style={[styles.battleIcon, styles.mintBackground]}>
                <Icon name="groups" size={32} color="#FFFFFF" />
              </View>
              <Text style={styles.battleTitle}>Group Battle</Text>
              <Text style={styles.battleSubtitle}>Join a group</Text>
              <TouchableOpacity
                style={[styles.battleButton, styles.mintBackground]}>
                <Text style={styles.battleButtonText}>Join</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.battleCard}>
              <View style={[styles.battleIcon, styles.pinkBackground]}>
                <Icon name="sports-martial-arts" size={32} color="#FFFFFF" />
              </View>
              <Text style={styles.battleTitle}>1v1 Battle</Text>
              <Text style={styles.battleSubtitle}>Challenge a friend</Text>
              <TouchableOpacity
                style={[styles.battleButton, styles.pinkBackground]}>
                <Text style={styles.battleButtonText}>Challenge</Text>
              </TouchableOpacity>
            </View>
          </View>
          <View style={styles.quizBotCard}>
            <View style={styles.quizBotInfo}>
              <Text style={styles.quizBotTitle}>Quiz Bot</Text>
              <Text style={styles.quizBotSubtitle}>Test your knowledge</Text>
            </View>
            <TouchableOpacity style={styles.quizBotButton}>
              <Text style={styles.quizBotButtonText}>Start</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB', // --background
  },
  scrollView: {
    flex: 1,
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)', // bg-white/80
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    overflow: 'hidden',
  },
  avatarImage: {
    width: '100%',
    height: '100%',
    borderRadius: 20,
  },
  userInfo: {
    gap: 2,
  },
  welcomeText: {
    fontSize: 14,
    color: '#9CA3AF', // --secondary
    fontFamily: 'Lexend-Regular',
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937', // --headings
    fontFamily: 'Lexend-Bold',
    lineHeight: 22,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  streakBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: 'rgba(255, 145, 77, 0.1)', // bg-orange-100
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  streakText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#FF914D', // --accent-orange
    fontFamily: 'Lexend-Bold',
  },
  // Stats Card - Mobile design'dan birebir
  statsCard: {
    backgroundColor: '#4A47A3', // --primary
    marginHorizontal: 16,
    marginBottom: 24,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  statsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  rankSection: {
    gap: 4,
  },
  rankLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'Lexend-Regular',
  },
  rankValue: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend-Bold',
  },
  pointsSection: {
    alignItems: 'flex-end',
    gap: 8,
  },
  pointsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  pointsText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Lexend-SemiBold',
  },
  progressSection: {
    marginTop: 16,
    gap: 4,
  },
  progressLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontFamily: 'Lexend-Regular',
  },
  progressBar: {
    width: '100%',
    height: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#FF914D', // --accent-orange
    borderRadius: 5,
  },
  progressWidth: {
    width: '75%',
  },
  mintBackground: {
    backgroundColor: '#2DD4BF',
  },
  pinkBackground: {
    backgroundColor: '#F472B6',
  },
  // Daily Questions Card - Mobile design'dan birebir
  dailyCard: {
    backgroundColor: '#FFFFFF', // --surface
    marginHorizontal: 16,
    marginBottom: 24,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB', // --border
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  dailyContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  dailyInfo: {
    flex: 1,
    gap: 4,
  },
  dailyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937', // --headings
    fontFamily: 'Lexend-Bold',
  },
  dailySubtitle: {
    fontSize: 14,
    color: '#374151', // --body
    fontFamily: 'Lexend-Regular',
  },
  dailyIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#FF914D', // --accent-orange
    alignItems: 'center',
    justifyContent: 'center',
  },
  startButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: '#FF914D', // --accent-orange
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  startButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend-Bold',
  },
  // Engage & Compete Section - Mobile design'dan birebir
  engageSection: {
    paddingHorizontal: 16,
    gap: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937', // --headings
    fontFamily: 'Lexend-Bold',
    paddingBottom: 12,
  },
  battleGrid: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  battleCard: {
    flex: 1,
    backgroundColor: '#FFFFFF', // --surface
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB', // --border
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  battleIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  battleTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937', // --headings
    fontFamily: 'Lexend-Bold',
    marginBottom: 4,
  },
  battleSubtitle: {
    fontSize: 14,
    color: '#9CA3AF', // --secondary
    fontFamily: 'Lexend-Regular',
    marginBottom: 12,
  },
  battleButton: {
    width: '100%',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  battleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    fontFamily: 'Lexend-Medium',
  },
  quizBotCard: {
    backgroundColor: '#FFFFFF', // --surface
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#E5E7EB', // --border
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  quizBotInfo: {
    gap: 4,
  },
  quizBotTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1F2937', // --headings
    fontFamily: 'Lexend-Bold',
  },
  quizBotSubtitle: {
    fontSize: 14,
    color: '#9CA3AF', // --secondary
    fontFamily: 'Lexend-Regular',
  },
  quizBotButton: {
    backgroundColor: '#4A47A3', // --primary
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  quizBotButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    fontFamily: 'Lexend-Medium',
  },
});
