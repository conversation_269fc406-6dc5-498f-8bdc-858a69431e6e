import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON><PERSON>
const mockNotificationData = {
  all: [
    {
      id: 1,
      type: 'group',
      title: 'History Masters study group was created.',
      time: '1h ago',
      isUnread: true,
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuC8N8lYOBgQ70sRNkbs2LhmuNmLjK4ik5KQX5bpor-zVcMH6Z5aCgBLuTuOSO7odbOEklv6Bmu7foDbhvkDkKCtIN_edoXtUD68kbAgXyHDSt_nKe7ifg26PHh5JnkhcfL6CThylxAhVLrbTZdHaBu96CiLAFgT-wjfph1mWUQ2r-xJnxPK1h7sDEyvbblnXTXunSknEI6zrs40F1Og_RgB_kik6I6uILTFXTagr-rK1ShNZ7E2BEDB6R3J0-aU8GneRzNF7D08ZgY',
    },
    {
      id: 2,
      type: 'friend',
      title: 'Your friend, Alex, joined the History Masters study group.',
      time: '2h ago',
      isUnread: true,
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuCxlQBcH8h2lb5C3rW5Ayb40Px-diCX7mDMnKjl3CmPiCPw7nMKYZ9t7B9Zay7CO-WaRYXb52Wzho-HWmNRwgen5HnP2ABjsdStqRmQIWUdJg0paDGicvxGh_rDCwBh8aD4J33DDEMYjCGME1GbrOtAB8ujGE1hxQdJXBAuyYgItRyazmO1NIZUxQKRDq-fwXpP3ggX2izBBXjvjr0rIdq38GaZj54HgmK_iXFn4DzPodG19iz2J4tp56trvmowdU2-galWj5Nrrp8',
    },
    {
      id: 3,
      type: 'challenge',
      title: 'Daily challenge reminder: Modern History is waiting for you!',
      time: '3h ago',
      isUnread: false,
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAcvDNPtpeT9pAaESlLCnPBQA_C1ciROjAZAioadP-7oas-lwNIlNiQQ2xq4HheLx-ko9cWBEa5MYIgtuAnInAfUOQxcq55QPxf088Mm3n1MHxESsn5RPEo9LY1lZwTh837x76tGqZyDpJvUuJwlZrJ8TrFDvWQG6SLCpzKDfj1OMWBDL6J6_oB51gt9SG5_Tl2jm7aaGEigF6ubUp8pfzC3wlNTeVV0Jf745wVKSUAXNycJrQ_RdMDUCd1ip6xJoRUwWFKDFZmi8s',
    },
    {
      id: 4,
      type: 'achievement',
      title: 'Congratulations! You earned the Quiz Master badge.',
      time: '1d ago',
      isUnread: false,
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuC8N8lYOBgQ70sRNkbs2LhmuNmLjK4ik5KQX5bpor-zVcMH6Z5aCgBLuTuOSO7odbOEklv6Bmu7foDbhvkDkKCtIN_edoXtUD68kbAgXyHDSt_nKe7ifg26PHh5JnkhcfL6CThylxAhVLrbTZdHaBu96CiLAFgT-wjfph1mWUQ2r-xJnxPK1h7sDEyvbblnXTXunSknEI6zrs40F1Og_RgB_kik6I6uILTFXTagr-rK1ShNZ7E2BEDB6R3J0-aU8GneRzNF7D08ZgY',
    },
  ],
};

export const NotificationsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'all' | 'unread' | 'mentions'>(
    'all',
  );

  const handleBack = () => {
    navigation.goBack();
  };

  const handleSettings = () => {
    console.log('Open notification settings');
  };

  const handleTabChange = (tab: 'all' | 'unread' | 'mentions') => {
    setActiveTab(tab);
  };

  const handleMarkAllAsRead = () => {
    console.log('Mark all as read');
  };

  const handleNotificationPress = (notificationId: number) => {
    console.log('Open notification:', notificationId);
  };

  const getFilteredNotifications = () => {
    switch (activeTab) {
      case 'unread':
        return mockNotificationData.all.filter(n => n.isUnread);
      case 'mentions':
        return mockNotificationData.all.filter(n => n.title.includes('@'));
      default:
        return mockNotificationData.all;
    }
  };

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back-ios" size={20} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={handleSettings}>
          <Icon name="settings" size={24} color="#1F2937" />
        </TouchableOpacity>
      </View>

      {/* Tab Navigation and Actions - Mobile design'dan birebir */}
      <View style={styles.tabSection}>
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'all' && styles.tabActive]}
            onPress={() => handleTabChange('all')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'all' && styles.tabTextActive,
              ]}>
              All
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'unread' && styles.tabActive]}
            onPress={() => handleTabChange('unread')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'unread' && styles.tabTextActive,
              ]}>
              Unread
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'mentions' && styles.tabActive]}
            onPress={() => handleTabChange('mentions')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'mentions' && styles.tabTextActive,
              ]}>
              Mentions
            </Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity onPress={handleMarkAllAsRead}>
          <Text style={styles.markAllText}>Mark all as read</Text>
        </TouchableOpacity>
      </View>

      {/* Notifications List - Mobile design'dan birebir */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.notificationsList}>
          {getFilteredNotifications().map(notification => (
            <TouchableOpacity
              key={notification.id}
              style={styles.notificationItem}
              onPress={() => handleNotificationPress(notification.id)}>
              <View style={styles.avatarContainer}>
                <Image
                  source={{uri: notification.avatar}}
                  style={styles.avatar}
                />
                {notification.isUnread && <View style={styles.unreadDot} />}
              </View>
              <View style={styles.notificationContent}>
                <Text style={styles.notificationTitle}>
                  {notification.title}
                </Text>
                <Text style={styles.notificationTime}>{notification.time}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'Be Vietnam Pro',
  },
  settingsButton: {
    padding: 8,
  },
  tabSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 8,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabActive: {
    borderBottomColor: '#1E13EC',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    fontFamily: 'Be Vietnam Pro',
  },
  tabTextActive: {
    color: '#111827',
    fontWeight: '600',
  },
  markAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E13EC',
    fontFamily: 'Be Vietnam Pro',
  },
  scrollView: {
    flex: 1,
  },
  notificationsList: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  notificationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  unreadDot: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#1E13EC',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    lineHeight: 20,
    fontFamily: 'Be Vietnam Pro',
  },
  notificationTime: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 4,
    fontFamily: 'Be Vietnam Pro',
  },
});
