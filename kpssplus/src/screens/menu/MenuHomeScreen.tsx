import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {SafeAreaView} from 'react-native-safe-area-context';
import {
  MenuStackParamList,
  MainTabParamList,
  RootStackParamList,
} from '../../navigation/types';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {useAuth} from '../../hooks/useAuth';
import {authService} from '../../services/api/authService';

type Props = CompositeScreenProps<
  StackScreenProps<MenuStackParamList, 'MenuHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Menu'>,
    StackScreenProps<RootStackParamList>
  >
>;

export const MenuHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isAuthenticated, user, logout} = useAuth();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const menuItems = [
    {
      id: 'profile',
      icon: 'person',
      title: 'Profil',
      subtitle: 'Profilinizi görüntüleyin ve düzenleyin',
      onPress: () => navigation.navigate('Profile'),
    },
    {
      id: 'settings',
      icon: 'settings',
      title: 'Ayarlar',
      subtitle: 'Uygulama ayarlarını özelleştirin',
      onPress: () => navigation.navigate('Settings'),
    },
    {
      id: 'badges',
      icon: 'medal',
      title: 'Rozetler',
      subtitle: 'Kazandığınız rozetleri görüntüleyin',
      onPress: () => navigation.navigate('Badges'),
    },
    {
      id: 'progress',
      icon: 'trending-up',
      title: 'İlerleme',
      subtitle: 'İlerlemenizi takip edin',
      onPress: () => navigation.navigate('Progress'),
    },
    {
      id: 'analytics',
      icon: 'analytics',
      title: 'Analitik',
      subtitle: 'Performansınızı analiz edin',
      onPress: () => navigation.navigate('Analytics'),
    },
    {
      id: 'goals',
      icon: 'flag',
      title: 'Hedefler',
      subtitle: 'Hedeflerinizi belirleyin',
      onPress: () => navigation.navigate('Goals'),
    },
    {
      id: 'notifications',
      icon: 'notifications',
      title: 'Bildirimler',
      subtitle: 'Bildirim ayarları',
      onPress: () => navigation.navigate('Notifications'),
    },
    {
      id: 'help',
      icon: 'help-circle',
      title: 'Yardım',
      subtitle: 'Yardım ve destek',
      onPress: () => navigation.navigate('Help'),
    },
    {
      id: 'about',
      icon: 'information-circle',
      title: 'Hakkında',
      subtitle: 'Uygulama hakkında bilgi',
      onPress: () => navigation.navigate('About'),
    },
  ];

  const handleLogout = async () => {
    Alert.alert(
      'Çıkış Yap',
      'Hesabınızdan çıkmak istediğinizden emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              setIsLoggingOut(true);
              await authService.logout();
              logout(); // Clear local auth state
              // Navigation will be handled by auth state change
            } catch (error: any) {
              console.error('Logout error:', error);
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu.');
            } finally {
              setIsLoggingOut(false);
            }
          },
        },
      ],
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* User Info Section */}
        {isAuthenticated && user && (
          <View style={styles.userSection}>
            <View style={styles.userAvatar}>
              <Text style={styles.userAvatarText}>
                {user.firstName?.charAt(0).toUpperCase() ||
                  user.username?.charAt(0).toUpperCase() ||
                  'U'}
              </Text>
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>
                {user.firstName && user.lastName
                  ? `${user.firstName} ${user.lastName}`
                  : user.username || 'Kullanıcı'}
              </Text>
              <Text style={styles.userEmail}>{user.email}</Text>
            </View>
          </View>
        )}

        <View style={styles.gridContainer}>
          {menuItems.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.menuCard}
              onPress={item.onPress}>
              <View style={styles.iconContainer}>
                <Icon name={item.icon} size={32} color={colors.primary} />
              </View>
              <View style={styles.menuItemContent}>
                <Text style={styles.menuItemTitle}>{item.title}</Text>
                <Text style={styles.menuItemSubtitle}>{item.subtitle}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Section */}
        {isAuthenticated && (
          <View style={styles.logoutSection}>
            <TouchableOpacity
              style={styles.logoutButton}
              onPress={handleLogout}
              disabled={isLoggingOut}>
              {isLoggingOut ? (
                <ActivityIndicator size="small" color="#EF4444" />
              ) : (
                <Icon name="log-out-outline" size={24} color="#EF4444" />
              )}
              <Text style={styles.logoutText}>
                {isLoggingOut ? 'Çıkış yapılıyor...' : 'Çıkış Yap'}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userAvatarText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
  },
  logoutSection: {
    marginHorizontal: 16,
    marginTop: 24,
    marginBottom: 32,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FEE2E2',
    gap: 12,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#EF4444',
  },

  scrollView: {
    flex: 1,
  },
  gridContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    paddingTop: 16,
    gap: 16,
  },
  menuCard: {
    width: '47%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    minHeight: 120,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#EEF2FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  menuItemContent: {
    alignItems: 'center',
    gap: 2,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 4,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 18,
  },
});
