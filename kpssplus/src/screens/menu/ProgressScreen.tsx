import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON>
const mockProgressData = {
  overall: {
    contentProgress: 75,
    quizPerformance: 60,
  },
  subjects: [
    {name: 'Mathematics', progress: 50, icon: 'calculate'},
    {name: 'Turkish Language', progress: 80, icon: 'book'},
    {name: 'History', progress: 65, icon: 'history_edu'},
    {name: 'Geography', progress: 90, icon: 'public'},
  ],
  quizResults: [
    {name: 'Mathematics Quiz 1', score: 70, icon: 'calculate'},
    {name: 'Turkish Language Quiz 1', score: 85, icon: 'book'},
    {name: 'History Quiz 1', score: 55, icon: 'history_edu'},
    {name: 'Geography Quiz 1', score: 95, icon: 'public'},
  ],
};

export const ProgressScreen: React.FC = () => {
  const navigation = useNavigation();

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#0e0d1b" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Progress</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Overall Progress Card - Mobile design'dan birebir */}
          <View style={styles.overallCard}>
            <Text style={styles.cardTitle}>Overall Progress</Text>
            <View style={styles.progressSection}>
              <View style={styles.progressItem}>
                <View style={styles.progressHeader}>
                  <Text style={styles.progressLabel}>Content Progress</Text>
                  <Text style={styles.progressValue}>
                    {mockProgressData.overall.contentProgress}%
                  </Text>
                </View>
                <View style={styles.progressBar}>
                  <View
                    style={[styles.progressFill, styles.contentProgressWidth]}
                  />
                </View>
              </View>
              <View style={styles.progressItem}>
                <View style={styles.progressHeader}>
                  <Text style={styles.progressLabel}>Quiz Performance</Text>
                  <Text style={styles.progressValue}>
                    {mockProgressData.overall.quizPerformance}%
                  </Text>
                </View>
                <View style={styles.progressBar}>
                  <View
                    style={[styles.progressFill, styles.quizProgressWidth]}
                  />
                </View>
              </View>
            </View>
          </View>

          {/* Content Progress Card - Mobile design'dan birebir */}
          <View style={styles.contentCard}>
            <Text style={styles.cardTitle}>Content Progress</Text>
            <View style={styles.subjectsList}>
              {mockProgressData.subjects.map((subject, index) => (
                <View key={index} style={styles.subjectItem}>
                  <View style={styles.subjectIcon}>
                    <Icon name={subject.icon} size={28} color="#1e13ec" />
                  </View>
                  <View style={styles.subjectInfo}>
                    <Text style={styles.subjectName}>{subject.name}</Text>
                    <Text style={styles.subjectProgress}>
                      {subject.progress}% completed
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Quiz Results Card - Mobile design'dan birebir */}
          <View style={styles.quizCard}>
            <Text style={styles.cardTitle}>Quiz Results</Text>
            <View style={styles.quizList}>
              {mockProgressData.quizResults.map((quiz, index) => (
                <View
                  key={index}
                  style={[
                    styles.quizItem,
                    index < mockProgressData.quizResults.length - 1 &&
                      styles.quizItemBorder,
                  ]}>
                  <View style={styles.quizIcon}>
                    <Icon name={quiz.icon} size={28} color="#1e13ec" />
                  </View>
                  <View style={styles.quizInfo}>
                    <Text style={styles.quizName}>{quiz.name}</Text>
                    <Text style={styles.quizScore}>{quiz.score}% score</Text>
                  </View>
                  <Text
                    style={[
                      styles.quizScoreValue,
                      quiz.score >= 70 ? styles.highScore : styles.lowScore,
                    ]}>
                    {quiz.score}%
                  </Text>
                </View>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#fcfcff', // bg-[#fcfcff]
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fcfcff', // bg-[#fcfcff]
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FFFFFF', // bg-white
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[#0e0d1b]
    textAlign: 'center',
    flex: 1,
    paddingRight: 40, // pr-10 equivalent
    fontFamily: 'Be Vietnam Pro',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    gap: 24, // space-y-6
  },
  // Overall Progress Card - Mobile design'dan birebir
  overallCard: {
    backgroundColor: '#FFFFFF', // bg-white
    borderRadius: 12, // rounded-xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    padding: 20, // p-5
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[#0e0d1b]
    marginBottom: 16, // mb-4
    fontFamily: 'Be Vietnam Pro',
  },
  progressSection: {
    gap: 20, // space-y-5
  },
  progressItem: {
    gap: 8, // flex flex-col gap-2
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0e0d1b', // text-[#0e0d1b]
    fontFamily: 'Be Vietnam Pro',
  },
  progressValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e13ec', // text-[#1e13ec]
    fontFamily: 'Be Vietnam Pro',
  },
  progressBar: {
    height: 10, // h-2.5
    width: '100%',
    borderRadius: 5,
    backgroundColor: '#E5E7EB', // bg-gray-200
  },
  progressFill: {
    height: 10, // h-2.5
    borderRadius: 5,
    backgroundColor: '#1e13ec', // bg-[#1e13ec]
  },
  contentProgressWidth: {
    width: '75%',
  },
  quizProgressWidth: {
    width: '60%',
  },
  // Content Progress Card - Mobile design'dan birebir
  contentCard: {
    backgroundColor: '#FFFFFF', // bg-white
    borderRadius: 12, // rounded-xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    padding: 20, // p-5
  },
  subjectsList: {
    gap: 16, // space-y-4
  },
  subjectItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16, // gap-4
  },
  subjectIcon: {
    width: 48, // size-12
    height: 48, // size-12
    borderRadius: 12, // rounded-xl
    backgroundColor: '#e8e7f3', // bg-[#e8e7f3]
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  subjectInfo: {
    flex: 1, // flex-grow
  },
  subjectName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0e0d1b', // text-[#0e0d1b]
    fontFamily: 'Be Vietnam Pro',
  },
  subjectProgress: {
    fontSize: 14,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
  // Quiz Results Card - Mobile design'dan birebir
  quizCard: {
    backgroundColor: '#FFFFFF', // bg-white
    borderRadius: 12, // rounded-xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    padding: 20, // p-5
  },
  quizList: {
    // divide-y divide-gray-100 equivalent
  },
  quizItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16, // gap-4
    paddingVertical: 12, // py-3
  },
  quizItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6', // divide-gray-100
  },
  quizIcon: {
    width: 48, // size-12
    height: 48, // size-12
    borderRadius: 12, // rounded-xl
    backgroundColor: '#e8e7f3', // bg-[#e8e7f3]
    alignItems: 'center',
    justifyContent: 'center',
    flexShrink: 0,
  },
  quizInfo: {
    flex: 1, // flex-grow
  },
  quizName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0e0d1b', // text-[#0e0d1b]
    fontFamily: 'Be Vietnam Pro',
  },
  quizScore: {
    fontSize: 14,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
  quizScoreValue: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Be Vietnam Pro',
  },
  highScore: {
    color: '#1e13ec',
  },
  lowScore: {
    color: '#6B7280',
  },
});
