import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';
import {
  preferencesService,
  UserPreferences,
} from '../../services/api/preferencesService';
import {useAuth} from '../../hooks/useAuth';

export const SettingsScreen: React.FC = () => {
  const navigation = useNavigation();
  const {isAuthenticated} = useAuth();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load user preferences
  const loadPreferences = useCallback(async () => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const userPrefs = await preferencesService.getUserPreferences();
      setPreferences(userPrefs);
    } catch (error) {
      console.error('Error loading preferences:', error);
      // Set default preferences
      setPreferences({
        theme: 'system',
        language: 'tr',
        notifications: {
          push_notifications: true,
          email_notifications: false,
          sms_notifications: true,
        },
        privacy: {
          profile_visibility: 'public',
          data_sharing: false,
        },
        account: {
          two_factor_auth: false,
          auto_logout: false,
        },
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  const handleBack = () => {
    navigation.goBack();
  };

  // Update preferences helper
  const updatePreferences = async (updates: Partial<UserPreferences>) => {
    if (!preferences || !isAuthenticated) {
      return;
    }

    try {
      setIsSaving(true);
      const updatedPrefs = await preferencesService.updateUserPreferences(
        updates,
      );
      setPreferences(updatedPrefs);
    } catch (error) {
      console.error('Error updating preferences:', error);
      Alert.alert('Hata', 'Ayarlar güncellenirken bir hata oluştu.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleThemePress = () => {
    console.log('Open theme settings');
  };

  const handleLanguagePress = () => {
    console.log('Open language settings');
  };

  const handlePrivacyPress = () => {
    console.log('Open privacy settings');
  };

  const handleNotificationToggle = async (
    type: keyof UserPreferences['notifications'],
    value: boolean,
  ) => {
    if (!preferences) {
      return;
    }

    const updatedNotifications = {
      ...preferences.notifications,
      [type]: value,
    };

    await updatePreferences({
      notifications: updatedNotifications,
    });
  };

  const handlePrivacyToggle = async (
    type: keyof UserPreferences['privacy'],
    value: boolean,
  ) => {
    if (!preferences) {
      return;
    }

    const updatedPrivacy = {
      ...preferences.privacy,
      [type]: value,
    };

    await updatePreferences({
      privacy: updatedPrivacy,
    });
  };

  const handleAccountToggle = async (
    type: keyof UserPreferences['account'],
    value: boolean,
  ) => {
    if (!preferences) {
      return;
    }

    const updatedAccount = {
      ...preferences.account,
      [type]: value,
    };

    await updatePreferences({
      account: updatedAccount,
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#1E13EC" />
        <Text style={styles.loadingText}>Ayarlar yükleniyor...</Text>
      </View>
    );
  }

  // Show guest state
  if (!isAuthenticated || !preferences) {
    return (
      <View style={[styles.container, styles.guestContainer]}>
        <Text style={styles.guestTitle}>Ayarlar</Text>
        <Text style={styles.guestMessage}>
          Ayarlarınızı görüntülemek için giriş yapın
        </Text>
        <TouchableOpacity style={styles.loginButton}>
          <Text style={styles.loginButtonText}>Giriş Yap</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back-ios" size={20} color="#0E0D1B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Settings</Text>
        <View style={styles.headerSpacer} />
        {isSaving && <ActivityIndicator size="small" color="#1E13EC" />}
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {/* General Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>GENERAL</Text>
            <View style={styles.settingsCard}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={handleThemePress}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Theme</Text>
                  <Text style={styles.settingSubtitle}>
                    Appearance settings
                  </Text>
                </View>
                <View style={styles.settingAction}>
                  <Text style={styles.settingValue}>
                    {preferences.theme === 'light'
                      ? 'Light'
                      : preferences.theme === 'dark'
                      ? 'Dark'
                      : 'System'}
                  </Text>
                  <Icon name="chevron-right" size={18} color="#504C9A" />
                </View>
              </TouchableOpacity>

              <View style={styles.settingDivider} />

              <TouchableOpacity
                style={styles.settingItem}
                onPress={handleLanguagePress}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Language</Text>
                  <Text style={styles.settingSubtitle}>App language</Text>
                </View>
                <View style={styles.settingAction}>
                  <Text style={styles.settingValue}>
                    {preferences.language === 'tr' ? 'Türkçe' : 'English'}
                  </Text>
                  <Icon name="chevron-right" size={18} color="#504C9A" />
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Notifications Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>NOTIFICATIONS</Text>
            <View style={styles.settingsCard}>
              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>Push Notifications</Text>
                <Switch
                  value={preferences.notifications.push_notifications}
                  onValueChange={value =>
                    handleNotificationToggle('push_notifications', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.notifications.push_notifications
                      ? '#FFFFFF'
                      : '#FFFFFF'
                  }
                />
              </View>

              <View style={styles.settingDivider} />

              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>Email Notifications</Text>
                <Switch
                  value={preferences.notifications.email_notifications}
                  onValueChange={value =>
                    handleNotificationToggle('email_notifications', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.notifications.email_notifications
                      ? '#FFFFFF'
                      : '#FFFFFF'
                  }
                />
              </View>

              <View style={styles.settingDivider} />

              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>SMS Notifications</Text>
                <Switch
                  value={preferences.notifications.sms_notifications}
                  onValueChange={value =>
                    handleNotificationToggle('sms_notifications', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.notifications.sms_notifications
                      ? '#FFFFFF'
                      : '#FFFFFF'
                  }
                />
              </View>
            </View>
          </View>

          {/* Privacy Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>PRIVACY</Text>
            <View style={styles.settingsCard}>
              <TouchableOpacity
                style={styles.settingItem}
                onPress={handlePrivacyPress}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Profile Visibility</Text>
                  <Text style={styles.settingSubtitle}>
                    Who can see your profile
                  </Text>
                </View>
                <View style={styles.settingAction}>
                  <Text style={styles.settingValue}>
                    {preferences.privacy.profile_visibility === 'public'
                      ? 'Public'
                      : preferences.privacy.profile_visibility === 'private'
                      ? 'Private'
                      : 'Friends'}
                  </Text>
                  <Icon name="chevron-right" size={18} color="#504C9A" />
                </View>
              </TouchableOpacity>

              <View style={styles.settingDivider} />

              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>Data Sharing</Text>
                <Switch
                  value={preferences.privacy.data_sharing}
                  onValueChange={value =>
                    handlePrivacyToggle('data_sharing', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.privacy.data_sharing ? '#FFFFFF' : '#FFFFFF'
                  }
                />
              </View>
            </View>
          </View>

          {/* Account Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>ACCOUNT</Text>
            <View style={styles.settingsCard}>
              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>
                  Two-Factor Authentication
                </Text>
                <Switch
                  value={preferences.account.two_factor_auth}
                  onValueChange={value =>
                    handleAccountToggle('two_factor_auth', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.account.two_factor_auth ? '#FFFFFF' : '#FFFFFF'
                  }
                />
              </View>

              <View style={styles.settingDivider} />

              <View style={styles.settingItem}>
                <Text style={styles.settingTitle}>Auto Logout</Text>
                <Switch
                  value={preferences.account.auto_logout}
                  onValueChange={value =>
                    handleAccountToggle('auto_logout', value)
                  }
                  trackColor={{false: '#E8E7F3', true: '#1E13EC'}}
                  thumbColor={
                    preferences.account.auto_logout ? '#FFFFFF' : '#FFFFFF'
                  }
                />
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8FC',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#504C9A',
    fontFamily: 'Be Vietnam Pro',
  },
  guestContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
    fontFamily: 'Be Vietnam Pro',
  },
  guestMessage: {
    fontSize: 16,
    color: '#504C9A',
    textAlign: 'center',
    marginBottom: 32,
    fontFamily: 'Be Vietnam Pro',
  },
  loginButton: {
    backgroundColor: '#1E13EC',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Be Vietnam Pro',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E7F3',
  },
  backButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    textAlign: 'center',
    paddingRight: 32,
    fontFamily: 'Be Vietnam Pro',
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingTop: 24,
    paddingBottom: 96,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#504C9A',
    letterSpacing: 1.2,
    marginBottom: 8,
    fontFamily: 'Be Vietnam Pro',
  },
  settingsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  settingDivider: {
    height: 1,
    backgroundColor: '#E8E7F3',
    marginLeft: 16,
  },
  settingInfo: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0E0D1B',
    fontFamily: 'Be Vietnam Pro',
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#504C9A',
    marginTop: 2,
    fontFamily: 'Be Vietnam Pro',
  },
  settingAction: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  settingValue: {
    fontSize: 14,
    color: '#504C9A',
    fontFamily: 'Be Vietnam Pro',
  },
});
