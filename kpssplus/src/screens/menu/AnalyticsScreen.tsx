import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON>
const mockAnalyticsData = {
  user: {
    name: '<PERSON>',
    username: '@ethan.carter',
    avatar:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuA4vgbnxQN7CIzKyUdOkvxoUh0f65PBDThnsHz1YDaDy4zicspYx7B0SCCmhpOreDA1c4gcnuAX-lPfm3FeibYQxKAPRpc-vDlfUOrbfPR177UqTzPVzAUI-cX2oXsa8ZzGiHkfDHyqpFIgN4lntrM4asd2W2xeFG3VTtoskG9V-jrwx3uGM2Xke0H8BEMMVAHTysl4BnFVMUrMCoPHceS30YcKVrxMo9_kUoyDGP5V_ALBbhZNzdrDCQQ2DcwekQKttRpumzxphTI',
  },
  overview: {
    totalStudyTime: '120h',
    averageScore: '75%',
    views: '1.2K',
    attempts: '50',
  },
  progress: {
    streak: '5 Days',
    streakMessage: 'Keep up the momentum!',
    badges: '12 Badges',
    badgesMessage: 'Collect them all!',
  },
  social: {
    friends: '25',
    posts: '10',
  },
  activity: {
    lastActive: '2 hours ago',
  },
};

export const AnalyticsScreen: React.FC = () => {
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Analytics</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* User Profile Section - Mobile design'dan birebir */}
          <View style={styles.profileSection}>
            <Image
              source={{uri: mockAnalyticsData.user.avatar}}
              style={styles.avatar}
            />
            <View style={styles.userInfo}>
              <Text style={styles.userName}>{mockAnalyticsData.user.name}</Text>
              <Text style={styles.userHandle}>
                {mockAnalyticsData.user.username}
              </Text>
            </View>
          </View>

          {/* Overview Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Overview</Text>
            <View style={styles.overviewGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Total Study Time</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.overview.totalStudyTime}
                </Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Average Score</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.overview.averageScore}
                </Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Views</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.overview.views}
                </Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Attempts</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.overview.attempts}
                </Text>
              </View>
            </View>
          </View>

          {/* Progress Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Progress</Text>
            <View style={styles.progressList}>
              <View style={styles.progressCard}>
                <View style={styles.progressInfo}>
                  <Text style={styles.progressLabel}>Streak</Text>
                  <Text style={styles.progressValue}>
                    {mockAnalyticsData.progress.streak}
                  </Text>
                  <Text style={styles.progressMessage}>
                    {mockAnalyticsData.progress.streakMessage}
                  </Text>
                </View>
                <View style={styles.progressIcon}>
                  <Icon
                    name="local-fire-department"
                    size={32}
                    color="#1e13ec"
                  />
                </View>
              </View>
              <View style={styles.progressCard}>
                <View style={styles.progressInfo}>
                  <Text style={styles.progressLabel}>Badges</Text>
                  <Text style={styles.progressValue}>
                    {mockAnalyticsData.progress.badges}
                  </Text>
                  <Text style={styles.progressMessage}>
                    {mockAnalyticsData.progress.badgesMessage}
                  </Text>
                </View>
                <View style={styles.progressIcon}>
                  <Icon name="military-tech" size={32} color="#1e13ec" />
                </View>
              </View>
            </View>
          </View>

          {/* Social Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Social</Text>
            <View style={styles.socialGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Friends</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.social.friends}
                </Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Posts</Text>
                <Text style={styles.statValue}>
                  {mockAnalyticsData.social.posts}
                </Text>
              </View>
            </View>
          </View>

          {/* Activity Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Activity</Text>
            <Text style={styles.activityText}>
              Last Active: {mockAnalyticsData.activity.lastActive}
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB', // bg-gray-50
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB', // bg-gray-50
    paddingHorizontal: 16,
    paddingVertical: 16,
    justifyContent: 'space-between',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827', // text-gray-900
    textAlign: 'center',
    flex: 1,
    paddingRight: 40, // pr-10 equivalent
    fontFamily: 'Be Vietnam Pro',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16, // p-4 sm:p-6
  },
  // Profile Section - Mobile design'dan birebir
  profileSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16, // gap-4
    marginBottom: 32, // mb-8
  },
  avatar: {
    width: 96, // h-24 w-24
    height: 96,
    borderRadius: 48,
    borderWidth: 4,
    borderColor: '#FFFFFF', // border-white
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 24, // text-2xl
    fontWeight: 'bold',
    color: '#111827', // text-gray-900
    fontFamily: 'Be Vietnam Pro',
  },
  userHandle: {
    fontSize: 16,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
  // Section - Mobile design'dan birebir
  section: {
    marginBottom: 32, // mb-8
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827', // text-gray-900
    marginBottom: 16, // mb-4
    fontFamily: 'Be Vietnam Pro',
  },
  // Overview Grid - Mobile design'dan birebir
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16, // gap-4
  },
  statCard: {
    flex: 1,
    minWidth: '45%', // grid-cols-2 equivalent
    flexDirection: 'column',
    gap: 8, // gap-2
    borderRadius: 12, // rounded-xl
    padding: 16, // p-4
    backgroundColor: '#FFFFFF', // bg-white
    borderWidth: 1,
    borderColor: '#E5E7EB', // border-gray-200
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280', // text-gray-600
    fontFamily: 'Be Vietnam Pro',
  },
  statValue: {
    fontSize: 30, // text-3xl
    fontWeight: 'bold',
    color: '#1e13ec', // text-[var(--primary-color)]
    fontFamily: 'Be Vietnam Pro',
  },
  // Progress List - Mobile design'dan birebir
  progressList: {
    gap: 16, // space-y-4
  },
  progressCard: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16, // gap-4
    borderRadius: 12, // rounded-xl
    padding: 16, // p-4
    backgroundColor: '#FFFFFF', // bg-white
    borderWidth: 1,
    borderColor: '#E5E7EB', // border-gray-200
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  progressInfo: {
    flex: 1,
    gap: 4, // gap-1
  },
  progressLabel: {
    fontSize: 14,
    color: '#6B7280', // text-gray-600
    fontFamily: 'Be Vietnam Pro',
  },
  progressValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827', // text-gray-900
    fontFamily: 'Be Vietnam Pro',
  },
  progressMessage: {
    fontSize: 14,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
  progressIcon: {
    width: 96, // w-24
    height: 64, // h-16
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  // Social Grid - Mobile design'dan birebir
  socialGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16, // gap-4
  },
  // Activity - Mobile design'dan birebir
  activityText: {
    fontSize: 14,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
});
