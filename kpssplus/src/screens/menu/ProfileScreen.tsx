import React, {useCallback, useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
  Image,
  ActivityIndicator,
  Alert,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';
import {useAuth} from '../../hooks/useAuth';
import {authService} from '../../services/api/authService';
import {badgeService} from '../../services/api/badgeService';
import {progressService} from '../../services/api/progressService';
import {UserInfo, UserBadge} from '../../services/api/types';

export const ProfileScreen: React.FC = React.memo(() => {
  const navigation = useNavigation();
  const {user, isAuthenticated} = useAuth();
  const [userProfile, setUserProfile] = useState<UserInfo | null>(null);
  const [badges, setBadges] = useState<UserBadge[]>([]);
  const [userStats, setUserStats] = useState({
    totalPoints: 0,
    badgeCount: 0,
    tokens: 0,
  });
  const [isLoading, setIsLoading] = useState(true);

  // Load user profile data
  const loadProfileData = useCallback(async () => {
    if (!isAuthenticated) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Load current user info
      const currentUser = await authService.getCurrentUser();
      setUserProfile(currentUser);

      // Load user badges
      try {
        const userBadges = await badgeService.getUserBadges();
        setBadges(userBadges.data || []);
      } catch (badgeError) {
        console.error('Error loading badges:', badgeError);
      }

      // Load user progress/stats
      try {
        const progress = await progressService.getAllUserProgress();
        // Calculate total points from completed content
        const totalPoints =
          progress.data?.reduce((sum, item) => {
            return sum + (item.completed ? 100 : item.progress_percentage);
          }, 0) || 0;

        setUserStats({
          totalPoints: Math.round(totalPoints),
          badgeCount: badges.length,
          tokens: 0, // TODO: Add tokens field to user model
        });
      } catch (progressError) {
        console.error('Error loading progress:', progressError);
      }
    } catch (error) {
      console.error('Error loading profile data:', error);
      Alert.alert('Hata', 'Profil bilgileri yüklenirken bir hata oluştu.');
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, badges.length]);

  useEffect(() => {
    loadProfileData();
  }, [loadProfileData]);

  // Memoize handlers
  const handleSettings = useCallback(() => {
    console.log('Settings');
  }, []);

  const handleBadges = useCallback(() => {
    console.log('Badges');
  }, []);

  const handleTokens = useCallback(() => {
    console.log('Tokens');
  }, []);

  const handleEditProfile = useCallback(() => {
    console.log('Edit profile');
  }, []);

  // Show loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#1E13EC" />
        <Text style={styles.loadingText}>Profil yükleniyor...</Text>
      </View>
    );
  }

  // Show guest state
  if (!isAuthenticated) {
    return (
      <View style={[styles.container, styles.guestContainer]}>
        <Text style={styles.guestTitle}>Profil</Text>
        <Text style={styles.guestMessage}>
          Profilinizi görüntülemek için giriş yapın
        </Text>
        <TouchableOpacity style={styles.loginButton}>
          <Text style={styles.loginButtonText}>Giriş Yap</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const displayUser = userProfile || user;
  const defaultAvatar =
    'https://lh3.googleusercontent.com/aida-public/AB6AXuA4BhHD9tJA0buY8UZHNS0MXtD0elYfenQcEN6Sh1iyXd4c00t03P3WWzLbZ8dMyBqLWkTL2fE4qB8rdkHK7xLWIFKedTOBcTPBFZkEDz3kKogXktT4il2em4VRjJBOGQO8IbkNDGkasgH6Z6Omn_XX4wTdJtLXl4QWWS1qr2VNV-qlYX0d4w7lfrli-oePoa6UaGn6xT4ljQIeLOX2wo2VX7GfE7A6VnPz-PB24pZ9b7jRZN4BvdcLZkdnE3_a0eE4ejFJB72sPII';
  const defaultCover =
    'https://lh3.googleusercontent.com/aida-public/AB6AXuDh6blg1PRDHyI0iktBx_PUOkaa1ub27eZPkDt85JZcrgZkYbjbvs_CbKKMs-myrnF1LQrIl9QOHkGvWKsiR_PWqpaUKaRftnQX8X2VuaObE3dMvaQ9wnkahkYlfnxaYF6uixZ6OKueUxh4LWZAprgvdq-JfryyTfKfOv4uTaBH_b_EY63AEhNU-Y2s90YtbizDaVURlz9OwqiT3QROSixyDcldRdoi2ExyfXH7FPlyqSpEL48NX-V_a8akEmNzz6dCrwMpK9P_cW8';

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#1E293B" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <TouchableOpacity
          style={styles.settingsButton}
          onPress={handleSettings}>
          <Icon name="settings" size={24} color="#1E293B" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Cover Image - Mobile design'dan birebir */}
        <View style={styles.coverContainer}>
          <ImageBackground
            source={{uri: defaultCover}}
            style={styles.coverImage}
            imageStyle={styles.coverImageStyle}
          />
        </View>

        {/* Profile Info - Mobile design'dan birebir */}
        <View style={styles.profileContainer}>
          <View style={styles.profileContent}>
            <View style={styles.profileInfo}>
              <Image source={{uri: defaultAvatar}} style={styles.avatar} />
              <View style={styles.userInfo}>
                <Text style={styles.userName}>
                  {displayUser?.username || 'Kullanıcı'}
                </Text>
                <Text style={styles.userHandle}>
                  @{displayUser?.username || 'user'}
                </Text>
                <Text style={styles.userPoints}>
                  {userStats.totalPoints.toLocaleString('tr-TR')} Puan
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Menu Items - Mobile design'dan birebir */}
        <View style={styles.menuContainer}>
          <TouchableOpacity style={styles.menuItem} onPress={handleBadges}>
            <View style={styles.menuIcon}>
              <Icon name="workspace-premium" size={24} color="#1E293B" />
            </View>
            <Text style={styles.menuText}>Badges</Text>
            <Icon name="chevron-right" size={24} color="#94A3B8" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.menuItem} onPress={handleTokens}>
            <View style={styles.menuItemContent}>
              <View style={styles.menuIcon}>
                <Icon name="monetization-on" size={24} color="#1E293B" />
              </View>
              <Text style={styles.menuText}>Tokens</Text>
            </View>
            <Text style={styles.tokenCount}>{userStats.tokens}</Text>
          </TouchableOpacity>
        </View>

        {/* Edit Profile Button - Mobile design'dan birebir */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.editButton}
            onPress={handleEditProfile}>
            <Text style={styles.editButtonText}>Edit Profile</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Bottom Navigation - Mobile design'dan birebir */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="home" size={24} color="#64748B" />
          <Text style={styles.navLabel}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="leaderboard" size={24} color="#64748B" />
          <Text style={styles.navLabel}>Leaderboard</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="checklist" size={24} color="#64748B" />
          <Text style={styles.navLabel}>Test</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="article" size={24} color="#64748B" />
          <Text style={styles.navLabel}>Mock Exams</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItemActive}>
          <Icon name="person" size={24} color="#4F46E5" />
          <Text style={styles.navLabelActive}>Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#504C9A',
    fontFamily: 'Be Vietnam Pro',
  },
  guestContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  guestTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
    fontFamily: 'Be Vietnam Pro',
  },
  guestMessage: {
    fontSize: 16,
    color: '#504C9A',
    textAlign: 'center',
    marginBottom: 32,
    fontFamily: 'Be Vietnam Pro',
  },
  loginButton: {
    backgroundColor: '#1E13EC',
    paddingHorizontal: 32,
    paddingVertical: 12,
    borderRadius: 8,
  },
  loginButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Be Vietnam Pro',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
    backgroundColor: '#FFFFFF',
  },
  backButton: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    flex: 1,
    fontFamily: 'Be Vietnam Pro',
  },
  settingsButton: {
    width: 48,
    height: 48,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  coverContainer: {
    paddingHorizontal: 16,
  },
  coverImage: {
    width: '100%',
    minHeight: 218,
    justifyContent: 'flex-end',
  },
  coverImageStyle: {
    borderRadius: 12,
  },
  profileContainer: {
    paddingHorizontal: 16,
    marginTop: -64,
  },
  profileContent: {
    width: '100%',
    alignItems: 'center',
    gap: 16,
  },
  profileInfo: {
    alignItems: 'center',
    gap: 16,
  },
  avatar: {
    width: 128,
    height: 128,
    borderRadius: 64,
    borderWidth: 4,
    borderColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  userInfo: {
    alignItems: 'center',
    paddingTop: 8,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1E293B',
    textAlign: 'center',
    fontFamily: 'Be Vietnam Pro',
  },
  userHandle: {
    fontSize: 16,
    color: '#64748B',
    textAlign: 'center',
    fontFamily: 'Be Vietnam Pro',
  },
  userPoints: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4F46E5',
    textAlign: 'center',
    marginTop: 8,
    fontFamily: 'Be Vietnam Pro',
  },
  menuContainer: {
    paddingHorizontal: 16,
    gap: 12,
    marginTop: 24,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#F8FAFC',
    padding: 16,
    borderRadius: 12,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#E2E8F0',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1E293B',
    flex: 1,
    fontFamily: 'Be Vietnam Pro',
  },
  tokenCount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1E293B',
    fontFamily: 'Be Vietnam Pro',
  },
  buttonContainer: {
    paddingHorizontal: 16,
    paddingVertical: 24,
    alignItems: 'center',
  },
  editButton: {
    width: '100%',
    height: 48,
    backgroundColor: '#4F46E5',
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  editButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Be Vietnam Pro',
  },
  bottomNav: {
    flexDirection: 'row',
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: '#E2E8F0',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 12,
  },
  navItem: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  navItemActive: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  navLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#64748B',
    fontFamily: 'Be Vietnam Pro',
  },
  navLabelActive: {
    fontSize: 12,
    fontWeight: '500',
    color: '#4F46E5',
    fontFamily: 'Be Vietnam Pro',
  },
});
