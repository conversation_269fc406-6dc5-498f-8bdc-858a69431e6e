import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock badge data - mobile design'dan <PERSON><PERSON><PERSON>
const mockBadges = [
  {
    id: '1',
    name: 'First Steps',
    description: 'Complete your first lesson',
    image:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuDsT2hn9RXTTaxWHOrm6-eG9tfGC2FFDU3B1TRFKnklOSqWiPVWsGAlcvM2QnxcRQ3sXBbdjanaj-1O1SRKWj5YkMN6ryy-T7oPTmE8kOGDy0GFx87qr6ppt_VrKKQxpypLMQUyQHtuHCo20E8W7CbvUX8I-1qCN8qYc9pFiiOcibOqrSDoGQ-VS4Mgzo7GfOCAHbhnLBkx2_kAk_llkwqrPW-2KhNl-XLHcgUh24kRkuJkVzmnnJ_zJUQ1aiwPrXsgcc6wIs5Swpk',
    unlocked: true,
  },
  {
    id: '2',
    name: 'Early Achiever',
    description: 'Complete 5 lessons',
    image:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuD880ykk7wsyMKzoMj7lUHrcrbLcXhR7Bq7aMsKRE_p1oTLAcoan9KuHW2_cNEU41AMR8NaZzf5STXTJCYrAOaMrwvwKpud0Zkg-uVUJGErjyQgy3vG5G4uRMiSHhhyKdAI_3HXdSJ4MZF63cVmiLRb3gNPVcYy-v84eftgTjx1UWl0ZhBKmp1IoceFT9mWVIW9hiFuXpnoDL0B2_RZzjSigKa09dE2WW_XCg8d-8o25ssQIagqhtujFHjcgND51YolvRi_PllKNsM',
    unlocked: true,
  },
  {
    id: '3',
    name: 'Consistent Learner',
    description: 'Study for 7 days in a row',
    image:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuBgyHzXjZEFKaGtJ4pXEnfoVhjkNW7t36kkmcwyQB4YypZuxoEZkDLHlp3mZSZGnlq54CSd5wAm5VJURPg7VIkChTw60hkbIvhewWTC7lgLAoe-bpA87PQz4M9EeKajiLnec59lpbXiYP28KsFnXtBBlA-FDu4fKamaD1YfzEPogXPtMSf-QS0D2UGa3d4ruEUrgLJmxVdwTbtfbaDXT130pBLnWQaejY6QC10zHUUOZgW1VAxXf1HZQ6wBP7mOTY1p8IZwNqhH37Y',
    unlocked: true,
  },
  {
    id: '4',
    name: 'Master of Quizzes',
    description: 'Score 90% or higher on 10 quizzes',
    image:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuCO8xFAakDXY3YabrfVjykhEvyo9fRlyxP34S5sNJ48sEti22BOsEUhu9mqnV5fH_PqiVjlh9mHLNkypJgRvZaExqOVAS7biPUhlymqqMuOTWmBun-3DepEdE1EUDFoLu97zUoAYWYZqnSzB1e8SmPOvtVk5bpuW0sMtV71D4xsl25x-OcvXPx7HZD8ND-76NeVahBhOaid6sx6fv1zo7bzJqe4-oBHRrJ_Y1FVMF73_OWKZ2BVhZ_kY9GpiTq_Kw9KIW03UBzULa8',
    unlocked: true,
  },
  {
    id: '5',
    name: 'Mock Exam Pro',
    description: 'Complete 5 mock exams',
    image:
      'https://lh3.googleusercontent.com/aida-public/AB6AXuA3Vp6oeqsJMe8T94H5OA36NI7eVAwBxQMWrncVeTNPfw_OzvK1DvjcVawNqla9NjVpYdZO2uxsu2_9PAL5pHD9kLvi__UJpgJ9ulW0aMB4PYKsSZ2FYqHUJAzaYeKb1Hb-rLWyjFHoRJOiLN91FvnacFY0mNXQjOEG5qvFNDtalAN1lhhurs6bZ92p6GeEoZ74EKx-lH98AAlJwX2xwkAX2_NM2b7XS83v9OHwfYM1rX2AOds6Dpv3n4jHX3Z-CDd7x_mj9GP3ZWw',
    unlocked: true,
  },
  {
    id: '6',
    name: 'Social Butterfly',
    description: 'Make 10 friends',
    unlocked: false,
  },
  {
    id: '7',
    name: 'Community Contributor',
    description: 'Help 5 other students',
    unlocked: false,
  },
  {
    id: '8',
    name: 'Top Performer',
    description: 'Rank in top 10',
    unlocked: false,
  },
];

export const BadgesScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleBadgePress = (badge: any) => {
    // Badge detay sayfasına git veya modal aç
    console.log('Badge pressed:', badge.name);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back-ios" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Badges</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Subtitle - Mobile design'dan birebir */}
        <Text style={styles.subtitle}>
          Earn badges by completing tasks and challenges.
        </Text>

        {/* Badges Grid - Mobile design'dan birebir */}
        <View style={styles.badgesGrid}>
          {mockBadges.map(badge => (
            <TouchableOpacity
              key={badge.id}
              style={[
                styles.badgeCard,
                badge.unlocked ? styles.unlockedBadge : styles.lockedBadge,
              ]}
              onPress={() => handleBadgePress(badge)}>
              <View style={styles.badgeImageContainer}>
                {badge.unlocked && badge.image ? (
                  <Image
                    source={{uri: badge.image}}
                    style={styles.badgeImage}
                  />
                ) : (
                  <View style={styles.lockedBadgeIcon}>
                    <Icon name="lock" size={32} color="#9CA3AF" />
                  </View>
                )}
              </View>
              <Text
                style={[
                  styles.badgeTitle,
                  badge.unlocked ? styles.unlockedText : styles.lockedText,
                ]}>
                {badge.name}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB', // bg-gray-50
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#F9FAFB', // bg-gray-50
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827', // text-gray-900
    fontFamily: 'Be Vietnam Pro',
  },
  headerSpacer: {
    width: 40,
    height: 40,
  },
  scrollView: {
    flex: 1,
  },
  // Subtitle - Mobile design'dan birebir
  subtitle: {
    fontSize: 16,
    color: '#6B7280', // text-gray-600
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 16,
    fontFamily: 'Be Vietnam Pro',
  },
  // Badges Grid - Mobile design'dan birebir
  badgesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 16,
    gap: 16,
  },
  badgeCard: {
    width: '47%', // grid-cols-2 equivalent
    flexDirection: 'column',
    alignItems: 'center',
    gap: 8,
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  unlockedBadge: {
    backgroundColor: '#FFFFFF', // bg-white
  },
  lockedBadge: {
    backgroundColor: '#E5E7EB', // bg-gray-200
  },
  badgeImageContainer: {
    width: 96, // w-24
    height: 96, // h-24
    borderRadius: 48,
    overflow: 'hidden',
  },
  badgeImage: {
    width: '100%',
    height: '100%',
    borderRadius: 48,
  },
  lockedBadgeIcon: {
    width: '100%',
    height: '100%',
    borderRadius: 48,
    backgroundColor: '#D1D5DB', // bg-gray-300
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeTitle: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
    fontFamily: 'Be Vietnam Pro',
  },
  unlockedText: {
    color: '#111827', // text-gray-900
  },
  lockedText: {
    color: '#6B7280', // text-gray-500
  },
});
