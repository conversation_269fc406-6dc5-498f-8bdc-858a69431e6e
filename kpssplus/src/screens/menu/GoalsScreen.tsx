import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

export const GoalsScreen: React.FC = () => {
  const navigation = useNavigation();
  const [studyDuration, setStudyDuration] = useState('60');
  const [questionsToSolve, setQuestionsToSolve] = useState('50');

  // Mock streak data - mobile design'dan alınan
  const streakDays = 15;
  const streakProgress = 75; // 75% of circle filled

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back-ios" size={20} color="#0e0d1b" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Goals</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Daily Study Goal Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Daily Study Goal</Text>
            <View style={styles.goalCard}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Study Duration (minutes)</Text>
                <TextInput
                  style={styles.input}
                  value={studyDuration}
                  onChangeText={setStudyDuration}
                  placeholder="e.g. 60"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Questions to Solve</Text>
                <TextInput
                  style={styles.input}
                  value={questionsToSolve}
                  onChangeText={setQuestionsToSolve}
                  placeholder="e.g. 50"
                  placeholderTextColor="#9CA3AF"
                  keyboardType="numeric"
                />
              </View>
              <TouchableOpacity style={styles.updateButton}>
                <Text style={styles.updateButtonText}>Update Goal</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Streak Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Your Streak</Text>
            <View style={styles.streakCard}>
              <View style={styles.streakContent}>
                {/* Circular Progress - Mobile design'dan birebir */}
                <View style={styles.circularProgress}>
                  <View style={styles.progressCircle}>
                    <View style={styles.progressBackground} />
                    <View
                      style={[
                        styles.progressFill,
                        {
                          transform: [
                            {rotate: `${(streakProgress / 100) * 360}deg`},
                          ],
                        },
                      ]}
                    />
                  </View>
                  <View style={styles.progressCenter}>
                    <Text style={styles.streakNumber}>{streakDays}</Text>
                    <Text style={styles.streakLabel}>Days</Text>
                  </View>
                </View>
                <Text style={styles.streakMessage}>You're on a roll! 🔥</Text>
                <Text style={styles.streakSubtext}>
                  Keep up the great work.
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#f8f8fc', // bg-[#f8f8fc]
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF', // bg-white
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[#0e0d1b]
    textAlign: 'center',
    flex: 1,
    fontFamily: 'Be Vietnam Pro',
  },
  headerSpacer: {
    width: 40, // w-10
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 24, // p-6
  },
  // Section - Mobile design'dan birebir
  section: {
    marginBottom: 32, // mb-8
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[#0e0d1b]
    marginBottom: 16, // mb-4
    fontFamily: 'Be Vietnam Pro',
  },
  // Goal Card - Mobile design'dan birebir
  goalCard: {
    backgroundColor: '#FFFFFF', // bg-white
    padding: 24, // p-6
    borderRadius: 16, // rounded-2xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  inputGroup: {
    marginBottom: 24, // mb-6
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#504c9a', // text-[#504c9a]
    marginBottom: 8, // mb-2
    fontFamily: 'Be Vietnam Pro',
  },
  input: {
    width: '100%',
    borderRadius: 12, // rounded-xl
    borderWidth: 1,
    borderColor: '#D1D5DB', // border-gray-300
    backgroundColor: '#F9FAFB', // bg-gray-50
    padding: 16, // p-4
    fontSize: 18,
    color: '#0e0d1b', // text-[#0e0d1b]
    fontFamily: 'Be Vietnam Pro',
  },
  updateButton: {
    width: '100%',
    height: 56, // h-14
    borderRadius: 12, // rounded-xl
    backgroundColor: '#1e13ec', // bg-[#1e13ec]
    alignItems: 'center',
    justifyContent: 'center',
  },
  updateButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Be Vietnam Pro',
  },
  // Streak Card - Mobile design'dan birebir
  streakCard: {
    backgroundColor: '#FFFFFF', // bg-white
    padding: 24, // p-6
    borderRadius: 16, // rounded-2xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  streakContent: {
    alignItems: 'center',
  },
  // Circular Progress - Mobile design'dan birebir
  circularProgress: {
    position: 'relative',
    width: 128, // w-32
    height: 128, // h-32
    marginBottom: 16, // mt-4
  },
  progressCircle: {
    width: 128,
    height: 128,
    borderRadius: 64,
    position: 'relative',
  },
  progressBackground: {
    position: 'absolute',
    width: 128,
    height: 128,
    borderRadius: 64,
    borderWidth: 8,
    borderColor: '#E5E7EB', // text-gray-200
  },
  progressFill: {
    position: 'absolute',
    width: 128,
    height: 128,
    borderRadius: 64,
    borderWidth: 8,
    borderColor: '#1e13ec', // text-[#1e13ec]
    borderTopColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
  },
  progressCenter: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: 128,
    height: 128,
    alignItems: 'center',
    justifyContent: 'center',
  },
  streakNumber: {
    fontSize: 36, // text-4xl
    fontWeight: 'bold',
    color: '#1e13ec', // text-[#1e13ec]
    fontFamily: 'Be Vietnam Pro',
  },
  streakLabel: {
    fontSize: 18,
    fontWeight: '500',
    color: '#504c9a', // text-[#504c9a]
    fontFamily: 'Be Vietnam Pro',
  },
  streakMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: '#0e0d1b', // text-[#0e0d1b]
    marginBottom: 4,
    fontFamily: 'Be Vietnam Pro',
  },
  streakSubtext: {
    fontSize: 14,
    color: '#6B7280', // text-gray-500
    fontFamily: 'Be Vietnam Pro',
  },
});
