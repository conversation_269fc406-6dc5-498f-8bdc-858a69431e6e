import React, {useState} from 'react';
import {View, Text, StyleSheet, ScrollView, Alert} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {Button, Input, SocialButton} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';

export const DesignShowcaseScreen: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const showAlert = (title: string) => {
    Alert.alert('Demo', `${title} button pressed`);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Modern Login Design Showcase</Text>
          <Text style={styles.sectionSubtitle}>
            KPSS Plus authentication components
          </Text>
        </View>

        {/* Logo Section */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Logo Container</Text>
          <View style={styles.logoContainer}>
            <Icon name="school-outline" size={48} color={colors.white} />
          </View>
        </View>

        {/* Input Components */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Input Components</Text>

          <Text style={styles.variantTitle}>Filled Variant (Modern)</Text>
          <Input
            placeholder="Email or Username"
            value={email}
            onChangeText={setEmail}
            variant="filled"
            size="large"
            style={styles.modernInput}
          />

          <Input
            placeholder="Password"
            value={password}
            onChangeText={setPassword}
            variant="filled"
            size="large"
            secureTextEntry
            style={styles.modernInput}
          />

          <Text style={styles.variantTitle}>
            Outlined Variant (Traditional)
          </Text>
          <Input
            placeholder="Traditional Input"
            variant="outlined"
            size="medium"
          />
        </View>

        {/* Button Components */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Button Components</Text>

          <Button
            title="Primary Button (Large)"
            onPress={() => showAlert('Primary')}
            size="large"
            fullWidth
            style={styles.modernButton}
          />

          <Button
            title="Secondary Button"
            onPress={() => showAlert('Secondary')}
            variant="secondary"
            size="large"
            fullWidth
            style={styles.button}
          />

          <Button
            title="Outline Button"
            onPress={() => showAlert('Outline')}
            variant="outline"
            size="large"
            fullWidth
            style={styles.button}
          />

          <Button
            title="Ghost Button"
            onPress={() => showAlert('Ghost')}
            variant="ghost"
            size="medium"
            fullWidth
            style={styles.button}
          />
        </View>

        {/* Social Buttons */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Social Login Buttons</Text>

          <View style={styles.socialRow}>
            <SocialButton
              provider="google"
              onPress={() => showAlert('Google')}
              style={styles.socialButton}
            />

            <SocialButton
              provider="apple"
              onPress={() => showAlert('Apple')}
              style={styles.socialButton}
            />
          </View>

          <SocialButton
            provider="facebook"
            onPress={() => showAlert('Facebook')}
            fullWidth
            style={styles.button}
          />

          <SocialButton
            provider="twitter"
            onPress={() => showAlert('Twitter')}
            fullWidth
            style={styles.button}
          />
        </View>

        {/* Divider Example */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Divider Component</Text>
          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>Or continue with</Text>
            <View style={styles.dividerLine} />
          </View>
        </View>

        {/* Typography Examples */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Typography</Text>
          <Text style={styles.titleExample}>Main Title (32px, Weight 900)</Text>
          <Text style={styles.subtitleExample}>
            Subtitle (16px, Weight 400)
          </Text>
          <Text style={styles.bodyExample}>Body text (16px, Weight 400)</Text>
          <Text style={styles.linkExample}>Link text (14px, Weight 500)</Text>
        </View>

        {/* Color Palette */}
        <View style={styles.section}>
          <Text style={styles.componentTitle}>Color Palette</Text>
          <View style={styles.colorRow}>
            <View style={[styles.colorBox, {backgroundColor: colors.primary}]}>
              <Text style={styles.colorLabel}>Primary</Text>
            </View>
            <View
              style={[styles.colorBox, {backgroundColor: colors.gray[100]}]}>
              <Text style={[styles.colorLabel, {color: colors.text.primary}]}>
                Gray 100
              </Text>
            </View>
          </View>
          <View style={styles.colorRow}>
            <View
              style={[styles.colorBox, {backgroundColor: colors.text.primary}]}>
              <Text style={[styles.colorLabel, {color: colors.white}]}>
                Text Primary
              </Text>
            </View>
            <View
              style={[
                styles.colorBox,
                {backgroundColor: colors.text.secondary},
              ]}>
              <Text style={styles.colorLabel}>Text Secondary</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
  },
  section: {
    marginBottom: spacing[8],
  },
  sectionTitle: {
    ...typography.h1,
    fontSize: 28,
    fontWeight: '900',
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing[2],
  },
  sectionSubtitle: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  componentTitle: {
    ...typography.h3,
    fontWeight: '700',
    color: colors.text.primary,
    marginBottom: spacing[4],
  },
  variantTitle: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.secondary,
    marginBottom: spacing[2],
    marginTop: spacing[2],
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    shadowColor: colors.primary,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  modernInput: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    borderWidth: 0,
    marginBottom: spacing[3],
  },
  modernButton: {
    borderRadius: 12,
    shadowColor: colors.primary,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: spacing[3],
  },
  button: {
    marginBottom: spacing[3],
  },
  socialRow: {
    flexDirection: 'row',
    gap: spacing[3],
    marginBottom: spacing[3],
  },
  socialButton: {
    flex: 1,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: spacing[4],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray[300],
  },
  dividerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginHorizontal: spacing[4],
  },
  titleExample: {
    ...typography.h1,
    fontSize: 32,
    fontWeight: '900',
    color: colors.text.primary,
    marginBottom: spacing[2],
  },
  subtitleExample: {
    ...typography.body1,
    color: colors.text.secondary,
    marginBottom: spacing[2],
  },
  bodyExample: {
    ...typography.body1,
    color: colors.text.primary,
    marginBottom: spacing[2],
  },
  linkExample: {
    ...typography.body2,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  colorRow: {
    flexDirection: 'row',
    gap: spacing[3],
    marginBottom: spacing[3],
  },
  colorBox: {
    flex: 1,
    height: 60,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  colorLabel: {
    ...typography.body2,
    color: colors.white,
    fontWeight: '600',
  },
});
