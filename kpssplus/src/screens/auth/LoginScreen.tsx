import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {AuthStackParamList} from '../../navigation/types';
import {Button, Input, SocialButton} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
// import {useAuth} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';
import {authService} from '../../services/api/authService';
import {ApiError} from '../../services/api/client';

type Props = StackScreenProps<AuthStackParamList, 'Login'>;

interface LoginForm {
  email: string;
  password: string;
}

interface LoginErrors {
  email?: string;
  password?: string;
}

export const LoginScreen: React.FC<Props> = () => {
  const [form, setForm] = useState<LoginForm>({
    email: '',
    password: '',
  });
  const [errors, setErrors] = useState<LoginErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: LoginErrors = {};

    // Email validation - can be email or username
    if (!form.email.trim()) {
      newErrors.email = 'E-posta veya kullanıcı adı gerekli';
    }

    // Password validation
    if (!form.password) {
      newErrors.password = 'Şifre gerekli';
    } else if (form.password.length < 6) {
      newErrors.password = 'Şifre en az 6 karakter olmalı';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const identifier = form.email.trim().toLowerCase();

      // Check if identifier is email or username
      const isEmail = /\S+@\S+\.\S+/.test(identifier);

      if (isEmail) {
        await authService.loginWithEmail({
          email: identifier,
          password: form.password,
        });
      } else {
        await authService.login({
          username: identifier,
          password: form.password,
        });
      }

      Alert.alert('Başarılı', 'Giriş başarılı!', [
        {
          text: 'Tamam',
          onPress: () => {
            // Navigation will be handled by auth state change
          },
        },
      ]);
    } catch (error: unknown) {
      console.error('Login error:', error);

      let errorMessage = 'Giriş yapılırken bir hata oluştu';

      if (error instanceof ApiError) {
        switch (error.status) {
          case 400:
            errorMessage = 'Geçersiz kullanıcı adı veya şifre';
            break;
          case 401:
            errorMessage = 'Kullanıcı adı veya şifre hatalı';
            break;
          case 404:
            errorMessage = 'Kullanıcı bulunamadı';
            break;
          case 429:
            errorMessage = 'Çok fazla deneme. Lütfen daha sonra tekrar deneyin';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      Alert.alert('Giriş Hatası', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGuestLogin = async () => {
    setIsLoading(true);
    try {
      await authService.loginAsGuest();

      Alert.alert('Başarılı', 'Misafir olarak giriş yaptınız!', [
        {
          text: 'Tamam',
          onPress: () => {
            // Navigation will be handled by auth state change
          },
        },
      ]);
    } catch (error: unknown) {
      console.error('Guest login error:', error);

      let errorMessage = 'Misafir girişi yapılırken bir hata oluştu';

      if (error instanceof ApiError) {
        errorMessage = error.message || errorMessage;
      } else if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      Alert.alert('Giriş Hatası', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const updateForm = (field: keyof LoginForm, value: string) => {
    setForm(prev => ({...prev, [field]: value}));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({...prev, [field]: undefined}));
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          {/* Header with Logo */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Icon name="school-outline" size={48} color={colors.white} />
            </View>
            <Text style={styles.title}>KPSS Plus</Text>
            <Text style={styles.subtitle}>
              Hesabına giriş yap ve öğrenmeye devam et
            </Text>
          </View>

          {/* Login Form */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <Input
                placeholder="E-posta veya Kullanıcı Adı"
                value={form.email}
                onChangeText={value => updateForm('email', value)}
                error={errors.email}
                variant="filled"
                size="large"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="emailAddress"
                style={styles.modernInput}
              />

              <Input
                placeholder="Şifre"
                value={form.password}
                onChangeText={value => updateForm('password', value)}
                error={errors.password}
                variant="filled"
                size="large"
                secureTextEntry
                textContentType="password"
                style={styles.modernInput}
              />
            </View>

            <Button
              title="Giriş Yap"
              onPress={handleLogin}
              loading={isLoading}
              disabled={isLoading}
              fullWidth
              size="large"
              style={styles.loginButton}
            />

            <View style={styles.linkContainer}>
              <TouchableOpacity
                onPress={() => console.log('Navigation disabled')}>
                <Text style={styles.linkText}>Şifremi Unuttum</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => console.log('Navigation disabled')}>
                <Text style={styles.linkText}>Yeni Hesap Oluştur</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Social Login Section */}
          <View style={styles.socialSection}>
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>Veya şununla devam et</Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <SocialButton
                provider="google"
                onPress={() => console.log('Google login')}
                fullWidth
                style={styles.socialButtonSpacing}
              />

              <SocialButton
                provider="apple"
                onPress={() => console.log('Apple login')}
                fullWidth
                style={styles.socialButtonSpacing}
              />
            </View>

            {/* Guest Login */}
            <TouchableOpacity
              style={styles.guestButton}
              onPress={handleGuestLogin}
              disabled={isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.primary} />
              ) : (
                <>
                  <Icon
                    name="person-outline"
                    size={20}
                    color={colors.primary}
                  />
                  <Text style={styles.guestButtonText}>
                    Misafir Olarak Devam Et
                  </Text>
                </>
              )}
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
    minHeight: Dimensions.get('window').height - 100,
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing[10],
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[4],
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  title: {
    ...typography.h1,
    fontWeight: '900',
    marginBottom: spacing[2],
    textAlign: 'center',
    color: colors.text.primary,
    fontSize: 32,
  },
  subtitle: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 16,
  },
  form: {
    marginBottom: spacing[8],
  },
  inputContainer: {
    marginBottom: spacing[4],
  },
  modernInput: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    borderWidth: 0,
    marginBottom: spacing[3],
  },
  loginButton: {
    marginTop: spacing[4],
    marginBottom: spacing[4],
    borderRadius: 12,
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  linkContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  linkText: {
    ...typography.body2,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  socialSection: {
    marginTop: spacing[8],
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[6],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray[300],
  },
  dividerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginHorizontal: spacing[4],
    fontSize: 14,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing[4],
  },
  socialButtonSpacing: {
    flex: 1,
  },
  guestButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing[4],
    paddingHorizontal: spacing[6],
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: 'transparent',
    marginTop: spacing[4],
    gap: spacing[2],
  },
  guestButtonText: {
    ...typography.body1,
    color: colors.primary,
    fontWeight: '600',
  },
});
