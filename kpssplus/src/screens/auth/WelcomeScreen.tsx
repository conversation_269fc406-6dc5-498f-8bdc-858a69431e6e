import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {AuthStackParamList} from '../../navigation/types';
import {Button, SocialButton} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = StackScreenProps<AuthStackParamList, 'Login'>;

export const WelcomeScreen: React.FC<Props> = ({navigation}) => {
  const handleLogin = () => {
    navigation.navigate('Login');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  const handleGuestMode = () => {
    // Handle guest mode login
    console.log('Guest mode');
  };

  const handleSocialLogin = (provider: string) => {
    console.log(`${provider} login`);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <View style={styles.logoContainer}>
            <Icon name="school-outline" size={64} color={colors.white} />
          </View>

          <Text style={styles.title}>KPSS Plus</Text>
          <Text style={styles.subtitle}>
            KPSS sınavına hazırlanmanın en etkili yolu
          </Text>

          <View style={styles.features}>
            <View style={styles.feature}>
              <Icon name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={styles.featureText}>Binlerce güncel soru</Text>
            </View>
            <View style={styles.feature}>
              <Icon name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={styles.featureText}>Detaylı çözümler</Text>
            </View>
            <View style={styles.feature}>
              <Icon name="checkmark-circle" size={20} color={colors.primary} />
              <Text style={styles.featureText}>İlerleme takibi</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionSection}>
          <Button
            title="Giriş Yap"
            onPress={handleLogin}
            fullWidth
            size="large"
            style={styles.primaryButton}
          />

          <Button
            title="Hesap Oluştur"
            onPress={handleRegister}
            variant="outline"
            fullWidth
            size="large"
            style={styles.secondaryButton}
          />

          {/* Social Login */}
          <View style={styles.socialSection}>
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>Veya</Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <SocialButton
                provider="google"
                onPress={() => handleSocialLogin('Google')}
                fullWidth
                style={styles.socialButton}
              />

              <SocialButton
                provider="apple"
                onPress={() => handleSocialLogin('Apple')}
                fullWidth
                style={styles.socialButton}
              />
            </View>
          </View>

          {/* Guest Mode */}
          <Button
            title="Misafir Olarak Devam Et"
            onPress={handleGuestMode}
            variant="ghost"
            fullWidth
            style={styles.guestButton}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

// const {width, height} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
  },
  heroSection: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing[8],
  },
  logoContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[6],
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 12,
    },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 12,
  },
  title: {
    ...typography.h1,
    fontSize: 42,
    fontWeight: '900',
    color: colors.text.primary,
    marginBottom: spacing[3],
    textAlign: 'center',
  },
  subtitle: {
    ...typography.h3,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[8],
    lineHeight: 28,
    paddingHorizontal: spacing[4],
  },
  features: {
    alignItems: 'flex-start',
  },
  feature: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  featureText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginLeft: spacing[2],
    fontWeight: '500',
  },
  actionSection: {
    paddingBottom: spacing[4],
  },
  primaryButton: {
    marginBottom: spacing[3],
    borderRadius: 12,
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  secondaryButton: {
    marginBottom: spacing[6],
    borderRadius: 12,
  },
  socialSection: {
    marginBottom: spacing[6],
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray[300],
  },
  dividerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginHorizontal: spacing[4],
  },
  socialButtons: {
    flexDirection: 'row',
    gap: spacing[3],
  },
  socialButton: {
    flex: 1,
  },
  guestButton: {
    borderRadius: 12,
  },
});
