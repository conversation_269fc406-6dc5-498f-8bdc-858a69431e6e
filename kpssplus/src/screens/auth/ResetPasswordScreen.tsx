import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {authService} from '../../services/api/authService';

interface Props {
  route?: {
    params?: {
      token?: string;
    };
  };
  navigation?: any;
}

export const ResetPasswordScreen: React.FC<Props> = ({route, navigation}) => {
  const token = route?.params?.token;
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const isValidPassword = password.length >= 8;
  const passwordsMatch =
    password === confirmPassword && confirmPassword.length > 0;
  const canSubmit = isValidPassword && passwordsMatch && !isLoading;

  const handleResetPassword = async () => {
    if (!canSubmit) {
      return;
    }

    if (!token) {
      Alert.alert('Hata', 'Geçersiz sıfırlama bağlantısı.');
      return;
    }

    try {
      setIsLoading(true);
      await authService.resetPassword({
        token,
        password: password,
      });

      Alert.alert(
        'Başarılı',
        'Şifreniz başarıyla sıfırlandı. Giriş sayfasına yönlendiriliyorsunuz.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation?.navigate('Login'),
          },
        ],
      );
    } catch (error: any) {
      console.error('Reset password error:', error);
      Alert.alert(
        'Hata',
        error.message || 'Şifre sıfırlanırken bir hata oluştu.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation?.goBack()}>
              <Icon name="arrow-back" size={24} color="#71717A" />
            </TouchableOpacity>
          </View>

          {/* Form Content */}
          <View style={styles.formContent}>
            <Text style={styles.title}>Yeni Şifre Oluştur</Text>
            <Text style={styles.description}>
              Yeni şifreniz en az 8 karakter olmalı ve güvenli olmalıdır.
            </Text>

            {/* New Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Yeni Şifre</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="En az 8 karakter"
                  placeholderTextColor="#6B7280"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowPassword(!showPassword)}>
                  <Icon
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#6B7280"
                  />
                </TouchableOpacity>
              </View>
              {password.length > 0 && (
                <View style={styles.passwordStrength}>
                  <View
                    style={[
                      styles.strengthIndicator,
                      isValidPassword
                        ? styles.strengthGood
                        : styles.strengthWeak,
                    ]}
                  />
                  <Text
                    style={[
                      styles.strengthText,
                      isValidPassword
                        ? styles.strengthTextGood
                        : styles.strengthTextWeak,
                    ]}>
                    {isValidPassword ? 'Güçlü şifre' : 'Zayıf şifre'}
                  </Text>
                </View>
              )}
            </View>

            {/* Confirm Password Input */}
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Şifre Tekrarı</Text>
              <View style={styles.passwordInputContainer}>
                <TextInput
                  style={styles.passwordInput}
                  placeholder="Şifrenizi tekrar girin"
                  placeholderTextColor="#6B7280"
                  value={confirmPassword}
                  onChangeText={setConfirmPassword}
                  secureTextEntry={!showConfirmPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={() => setShowConfirmPassword(!showConfirmPassword)}>
                  <Icon
                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color="#6B7280"
                  />
                </TouchableOpacity>
              </View>
              {confirmPassword.length > 0 && (
                <Text
                  style={[
                    styles.matchText,
                    passwordsMatch ? styles.matchTextGood : styles.matchTextBad,
                  ]}>
                  {passwordsMatch
                    ? 'Şifreler eşleşiyor'
                    : 'Şifreler eşleşmiyor'}
                </Text>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.primaryButton,
                !canSubmit && styles.primaryButtonDisabled,
              ]}
              onPress={handleResetPassword}
              disabled={!canSubmit}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Text style={styles.primaryButtonText}>Şifreyi Güncelle</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContent: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0E0D1B',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 48,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0E0D1B',
    marginBottom: 8,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 12,
  },
  passwordInput: {
    flex: 1,
    paddingHorizontal: 16,
    paddingVertical: 16,
    fontSize: 16,
    color: '#0E0D1B',
  },
  eyeButton: {
    padding: 16,
  },
  passwordStrength: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 8,
  },
  strengthIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  strengthWeak: {
    backgroundColor: '#EF4444',
  },
  strengthGood: {
    backgroundColor: '#10B981',
  },
  strengthText: {
    fontSize: 14,
    fontWeight: '500',
  },
  strengthTextWeak: {
    color: '#EF4444',
  },
  strengthTextGood: {
    color: '#10B981',
  },
  matchText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
  },
  matchTextGood: {
    color: '#10B981',
  },
  matchTextBad: {
    color: '#EF4444',
  },
  primaryButton: {
    backgroundColor: '#1E13EC',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  primaryButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
