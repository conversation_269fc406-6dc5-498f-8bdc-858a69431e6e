import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {AuthStackParamList} from '../../navigation/types';
import {Button, Input, SocialButton} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
// import {useAuth} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';
import {authService} from '../../services/api/authService';
import {ApiError} from '../../services/api/client';

type Props = StackScreenProps<AuthStackParamList, 'Register'>;

interface RegisterForm {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface RegisterErrors {
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
}

export const RegisterScreen: React.FC<Props> = () => {
  const [form, setForm] = useState<RegisterForm>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<RegisterErrors>({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: RegisterErrors = {};

    // First name validation
    if (!form.firstName.trim()) {
      newErrors.firstName = 'Ad gerekli';
    } else if (form.firstName.trim().length < 2) {
      newErrors.firstName = 'Ad en az 2 karakter olmalı';
    }

    // Last name validation
    if (!form.lastName.trim()) {
      newErrors.lastName = 'Soyad gerekli';
    } else if (form.lastName.trim().length < 2) {
      newErrors.lastName = 'Soyad en az 2 karakter olmalı';
    }

    // Username validation
    if (!form.username.trim()) {
      newErrors.username = 'Kullanıcı adı gerekli';
    } else if (form.username.trim().length < 3) {
      newErrors.username = 'Kullanıcı adı en az 3 karakter olmalı';
    } else if (!/^[a-zA-Z0-9_]+$/.test(form.username.trim())) {
      newErrors.username = 'Kullanıcı adı sadece harf, rakam ve _ içerebilir';
    }

    // Email validation
    if (!form.email.trim()) {
      newErrors.email = 'E-posta adresi gerekli';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = 'Geçerli bir e-posta adresi girin';
    }

    // Password validation
    if (!form.password) {
      newErrors.password = 'Şifre gerekli';
    } else if (form.password.length < 8) {
      newErrors.password = 'Şifre en az 8 karakter olmalı';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)) {
      newErrors.password =
        'Şifre en az 1 küçük harf, 1 büyük harf ve 1 rakam içermeli';
    }

    // Confirm password validation
    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Şifre tekrarı gerekli';
    } else if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = 'Şifreler eşleşmiyor';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const fullName = `${form.firstName.trim()} ${form.lastName.trim()}`;

      await authService.register({
        username: form.username.trim().toLowerCase(),
        email: form.email.trim().toLowerCase(),
        password: form.password,
        name: fullName,
      });

      Alert.alert(
        'Kayıt Başarılı',
        'Hesabınız başarıyla oluşturuldu. Şimdi giriş yapabilirsiniz.',
        [
          {
            text: 'Giriş Yap',
            onPress: () => {
              // Navigate to login screen
              console.log('Navigate to login');
            },
          },
        ],
      );
    } catch (error: unknown) {
      console.error('Register error:', error);

      let errorMessage = 'Kayıt olurken bir hata oluştu';

      if (error instanceof ApiError) {
        switch (error.status) {
          case 400:
            errorMessage = 'Geçersiz bilgiler. Lütfen kontrol edin';
            break;
          case 409:
            errorMessage = 'Bu kullanıcı adı veya e-posta zaten kullanımda';
            break;
          case 422:
            errorMessage = 'Girilen bilgiler geçerli değil';
            break;
          default:
            errorMessage = error.message || errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      Alert.alert('Kayıt Hatası', errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const updateForm = (field: keyof RegisterForm, value: string) => {
    setForm(prev => ({...prev, [field]: value}));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({...prev, [field]: undefined}));
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          {/* Header with Logo */}
          <View style={styles.header}>
            <View style={styles.logoContainer}>
              <Icon name="person-add-outline" size={48} color={colors.white} />
            </View>
            <Text style={styles.title}>Hesap Oluştur</Text>
            <Text style={styles.subtitle}>
              Ücretsiz hesabını oluştur ve öğrenmeye başla
            </Text>
          </View>

          {/* Registration Form */}
          <View style={styles.form}>
            <View style={styles.inputContainer}>
              <View style={styles.nameRow}>
                <Input
                  placeholder="Ad"
                  value={form.firstName}
                  onChangeText={value => updateForm('firstName', value)}
                  error={errors.firstName}
                  variant="filled"
                  size="large"
                  containerStyle={styles.nameInput}
                  autoCapitalize="words"
                  textContentType="givenName"
                  style={styles.modernInput}
                />
                <Input
                  placeholder="Soyad"
                  value={form.lastName}
                  onChangeText={value => updateForm('lastName', value)}
                  error={errors.lastName}
                  variant="filled"
                  size="large"
                  containerStyle={styles.nameInput}
                  autoCapitalize="words"
                  textContentType="familyName"
                  style={styles.modernInput}
                />
              </View>

              <Input
                placeholder="Kullanıcı Adı"
                value={form.username}
                onChangeText={value => updateForm('username', value)}
                error={errors.username}
                variant="filled"
                size="large"
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="username"
                style={styles.modernInput}
              />

              <Input
                placeholder="E-posta Adresi"
                value={form.email}
                onChangeText={value => updateForm('email', value)}
                error={errors.email}
                variant="filled"
                size="large"
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
                textContentType="emailAddress"
                style={styles.modernInput}
              />

              <Input
                placeholder="Şifre"
                value={form.password}
                onChangeText={value => updateForm('password', value)}
                error={errors.password}
                variant="filled"
                size="large"
                secureTextEntry
                textContentType="newPassword"
                style={styles.modernInput}
              />

              <Input
                placeholder="Şifre Tekrarı"
                value={form.confirmPassword}
                onChangeText={value => updateForm('confirmPassword', value)}
                error={errors.confirmPassword}
                variant="filled"
                size="large"
                secureTextEntry
                textContentType="newPassword"
                style={styles.modernInput}
              />
            </View>

            <Button
              title="Hesap Oluştur"
              onPress={handleRegister}
              loading={isLoading}
              disabled={isLoading}
              fullWidth
              size="large"
              style={styles.registerButton}
            />

            <View style={styles.linkContainer}>
              <TouchableOpacity
                onPress={() => console.log('Navigation disabled')}>
                <Text style={styles.linkText}>
                  Zaten hesabın var mı? Giriş Yap
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Social Registration Section */}
          <View style={styles.socialSection}>
            <View style={styles.divider}>
              <View style={styles.dividerLine} />
              <Text style={styles.dividerText}>Veya şununla kayıt ol</Text>
              <View style={styles.dividerLine} />
            </View>

            <View style={styles.socialButtons}>
              <SocialButton
                provider="google"
                onPress={() => console.log('Google register')}
                fullWidth
                style={styles.socialButtonSpacing}
              />

              <SocialButton
                provider="apple"
                onPress={() => console.log('Apple register')}
                fullWidth
                style={styles.socialButtonSpacing}
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

// const {width} = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing[8],
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[4],
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  title: {
    ...typography.h1,
    fontWeight: '900',
    marginBottom: spacing[2],
    textAlign: 'center',
    color: colors.text.primary,
    fontSize: 32,
  },
  subtitle: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    marginBottom: spacing[8],
  },
  inputContainer: {
    marginBottom: spacing[4],
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: spacing[3],
  },
  nameInput: {
    flex: 1,
  },
  modernInput: {
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    borderWidth: 0,
    marginBottom: spacing[3],
  },
  registerButton: {
    marginTop: spacing[4],
    marginBottom: spacing[4],
    borderRadius: 12,
    shadowColor: colors.primary,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  linkContainer: {
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  linkText: {
    ...typography.body2,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  socialSection: {
    marginTop: spacing[6],
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[6],
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: colors.gray[300],
  },
  dividerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginHorizontal: spacing[4],
    fontSize: 14,
  },
  socialButtons: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: spacing[4],
  },
  socialButtonSpacing: {
    flex: 1,
  },
});
