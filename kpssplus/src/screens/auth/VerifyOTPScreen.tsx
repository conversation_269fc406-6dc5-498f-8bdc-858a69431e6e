import React, {useState, useRef, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {authService} from '../../services/api/authService';

interface Props {
  route?: {
    params?: {
      email?: string;
      phone?: string;
      type?: 'email' | 'phone';
    };
  };
  navigation?: any;
}

export const VerifyOTPScreen: React.FC<Props> = ({route, navigation}) => {
  const {email, phone, type = 'email'} = route?.params || {};
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const inputRefs = useRef<TextInput[]>([]);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(prev => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      Alert.alert('Hata', 'Lütfen 6 haneli doğrulama kodunu girin.');
      return;
    }

    try {
      setIsLoading(true);
      await authService.verifyOTP({
        code: otpCode,
        email: email,
        phone: phone,
      });

      Alert.alert(
        'Başarılı',
        'Doğrulama başarılı! Giriş sayfasına yönlendiriliyorsunuz.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation?.navigate('Login'),
          },
        ],
      );
    } catch (error: any) {
      console.error('OTP verification error:', error);
      Alert.alert('Hata', error.message || 'Doğrulama kodu geçersiz.');
      // Clear OTP on error
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    try {
      if (type === 'email' && email) {
        await authService.forgotPassword({email});
      }
      // TODO: Add phone OTP resend when available

      setTimer(60);
      setCanResend(false);
      setOtp(['', '', '', '', '', '']);
      inputRefs.current[0]?.focus();

      Alert.alert('Başarılı', 'Doğrulama kodu tekrar gönderildi.');
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      Alert.alert('Hata', error.message || 'Doğrulama kodu gönderilemedi.');
    }
  };

  const isComplete = otp.every(digit => digit !== '');

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <View style={styles.content}>
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => navigation?.goBack()}>
              <Icon name="arrow-back" size={24} color="#71717A" />
            </TouchableOpacity>
          </View>

          {/* Form Content */}
          <View style={styles.formContent}>
            <Text style={styles.title}>Doğrulama Kodu</Text>
            <Text style={styles.description}>
              {type === 'email' && email
                ? `${email} adresine gönderilen 6 haneli kodu girin.`
                : type === 'phone' && phone
                ? `${phone} numarasına gönderilen 6 haneli kodu girin.`
                : 'Gönderilen 6 haneli doğrulama kodunu girin.'}
            </Text>

            {/* OTP Input */}
            <View style={styles.otpContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={ref => {
                    if (ref) {
                      inputRefs.current[index] = ref;
                    }
                  }}
                  style={[
                    styles.otpInput,
                    digit ? styles.otpInputFilled : null,
                  ]}
                  value={digit}
                  onChangeText={value => handleOtpChange(value, index)}
                  onKeyPress={({nativeEvent}) =>
                    handleKeyPress(nativeEvent.key, index)
                  }
                  keyboardType="numeric"
                  maxLength={1}
                  selectTextOnFocus
                />
              ))}
            </View>

            {/* Timer */}
            <View style={styles.timerContainer}>
              {!canResend ? (
                <Text style={styles.timerText}>Yeniden gönder ({timer}s)</Text>
              ) : (
                <TouchableOpacity onPress={handleResend}>
                  <Text style={styles.resendText}>Kodu yeniden gönder</Text>
                </TouchableOpacity>
              )}
            </View>

            <TouchableOpacity
              style={[
                styles.primaryButton,
                (!isComplete || isLoading) && styles.primaryButtonDisabled,
              ]}
              onPress={handleVerify}
              disabled={!isComplete || isLoading}>
              {isLoading ? (
                <ActivityIndicator size="small" color={colors.white} />
              ) : (
                <Text style={styles.primaryButtonText}>Doğrula</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.changeEmailButton}
              onPress={() => navigation?.goBack()}>
              <Text style={styles.changeEmailText}>
                {type === 'email'
                  ? 'E-posta adresini değiştir'
                  : 'Telefon numarasını değiştir'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  header: {
    paddingTop: 16,
    paddingBottom: 32,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  formContent: {
    flex: 1,
    justifyContent: 'center',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#0E0D1B',
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 48,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    gap: 12,
  },
  otpInput: {
    flex: 1,
    height: 56,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    borderRadius: 12,
    textAlign: 'center',
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0E0D1B',
    backgroundColor: '#F9FAFB',
  },
  otpInputFilled: {
    borderColor: '#1E13EC',
    backgroundColor: colors.white,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 16,
    color: '#6B7280',
  },
  resendText: {
    fontSize: 16,
    color: '#1E13EC',
    fontWeight: '500',
  },
  primaryButton: {
    backgroundColor: '#1E13EC',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 16,
  },
  primaryButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
  changeEmailButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  changeEmailText: {
    fontSize: 16,
    color: '#6B7280',
    fontWeight: '500',
  },
});
