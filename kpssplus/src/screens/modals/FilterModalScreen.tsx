import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: FilterOptions) => void;
}

interface FilterOptions {
  category: string[];
  difficulty: string[];
  duration: string[];
  sortBy: string;
}

export const FilterModalScreen: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  onApply,
}) => {
  const [filters, setFilters] = useState<FilterOptions>({
    category: [],
    difficulty: [],
    duration: [],
    sortBy: 'newest',
  });

  const categories = [
    'Matematik',
    'Türkçe',
    'Tarih',
    'Coğrafya',
    'Vatanda<PERSON>l<PERSON>k',
    '<PERSON><PERSON>ültür',
  ];

  const difficulties = ['Kolay', 'Orta', 'Zor'];
  const durations = ['5 dk', '10 dk', '15 dk', '30 dk', '60 dk'];
  const sortOptions = [
    {key: 'newest', label: 'En Yeni'},
    {key: 'oldest', label: 'En Eski'},
    {key: 'popular', label: 'En Popüler'},
    {key: 'difficulty', label: 'Zorluk'},
  ];

  const toggleFilter = (type: keyof FilterOptions, value: string) => {
    if (type === 'sortBy') {
      setFilters(prev => ({...prev, sortBy: value}));
    } else {
      setFilters(prev => ({
        ...prev,
        [type]: prev[type].includes(value)
          ? prev[type].filter(item => item !== value)
          : [...prev[type], value],
      }));
    }
  };

  const clearFilters = () => {
    setFilters({
      category: [],
      difficulty: [],
      duration: [],
      sortBy: 'newest',
    });
  };

  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="close" size={24} color="#71717A" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Filtrele</Text>
          <TouchableOpacity onPress={clearFilters} style={styles.clearButton}>
            <Text style={styles.clearText}>Temizle</Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Category Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Kategori</Text>
            <View style={styles.optionsGrid}>
              {categories.map(category => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.optionButton,
                    filters.category.includes(category) &&
                      styles.optionButtonActive,
                  ]}
                  onPress={() => toggleFilter('category', category)}>
                  <Text
                    style={[
                      styles.optionText,
                      filters.category.includes(category) &&
                        styles.optionTextActive,
                    ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Difficulty Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Zorluk</Text>
            <View style={styles.optionsRow}>
              {difficulties.map(difficulty => (
                <TouchableOpacity
                  key={difficulty}
                  style={[
                    styles.optionButton,
                    filters.difficulty.includes(difficulty) &&
                      styles.optionButtonActive,
                  ]}
                  onPress={() => toggleFilter('difficulty', difficulty)}>
                  <Text
                    style={[
                      styles.optionText,
                      filters.difficulty.includes(difficulty) &&
                        styles.optionTextActive,
                    ]}>
                    {difficulty}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Duration Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Süre</Text>
            <View style={styles.optionsRow}>
              {durations.map(duration => (
                <TouchableOpacity
                  key={duration}
                  style={[
                    styles.optionButton,
                    filters.duration.includes(duration) &&
                      styles.optionButtonActive,
                  ]}
                  onPress={() => toggleFilter('duration', duration)}>
                  <Text
                    style={[
                      styles.optionText,
                      filters.duration.includes(duration) &&
                        styles.optionTextActive,
                    ]}>
                    {duration}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Sort Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.sectionTitle}>Sıralama</Text>
            <View style={styles.sortOptions}>
              {sortOptions.map(option => (
                <TouchableOpacity
                  key={option.key}
                  style={styles.sortOption}
                  onPress={() => toggleFilter('sortBy', option.key)}>
                  <Text style={styles.sortOptionText}>{option.label}</Text>
                  <View style={styles.radioButton}>
                    {filters.sortBy === option.key && (
                      <View style={styles.radioButtonActive} />
                    )}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </ScrollView>

        {/* Apply Button */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.applyButton} onPress={handleApply}>
            <Text style={styles.applyButtonText}>Filtreleri Uygula</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  clearText: {
    fontSize: 16,
    color: '#1E13EC',
    fontWeight: '500',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  filterSection: {
    paddingVertical: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  optionsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: colors.white,
  },
  optionButtonActive: {
    backgroundColor: '#1E13EC',
    borderColor: '#1E13EC',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  optionTextActive: {
    color: colors.white,
  },
  sortOptions: {
    gap: 16,
  },
  sortOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  sortOptionText: {
    fontSize: 16,
    color: '#0E0D1B',
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonActive: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#1E13EC',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  applyButton: {
    backgroundColor: '#1E13EC',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
