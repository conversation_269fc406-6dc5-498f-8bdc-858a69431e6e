import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  FlatList,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {contentService} from '../../services/api/contentService';
import {quizService} from '../../services/api/quizService';
import {socialService} from '../../services/api/socialService';

interface SearchModalProps {
  visible: boolean;
  onClose: () => void;
  onSearch: (query: string) => void;
}

interface SearchResult {
  id: string;
  title: string;
  type: 'quiz' | 'content' | 'user';
  description?: string;
  avatar?: string;
}

export const SearchModalScreen: React.FC<SearchModalProps> = ({
  visible,
  onClose,
  onSearch,
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const recentSearches = [
    'KPSS Matematik',
    'Türkçe Quiz',
    'Tarih Soruları',
    'Genel Kültür',
  ];

  const popularSearches = [
    'KPSS 2024',
    'Matematik Formülleri',
    'Türkçe Dil Bilgisi',
    'Tarih Kronolojisi',
    'Coğrafya Haritaları',
  ];

  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      return;
    }

    setIsSearching(true);
    try {
      const searchResults: SearchResult[] = [];

      // Search content
      try {
        const contentResponse = await contentService.searchContent(searchQuery);
        const contentResults: SearchResult[] = contentResponse.data
          .slice(0, 3)
          .map(content => ({
            id: content.id,
            title: content.title,
            type: 'content' as const,
            description: content.description,
          }));
        searchResults.push(...contentResults);
      } catch (error) {
        console.error('Error searching content:', error);
      }

      // Search quizzes
      try {
        const quizResponse = await quizService.getQuizList({
          search: searchQuery,
        });
        const quizResults: SearchResult[] = quizResponse.data
          .slice(0, 3)
          .map(quiz => ({
            id: quiz.id,
            title: quiz.title,
            type: 'quiz' as const,
            description: quiz.description,
          }));
        searchResults.push(...quizResults);
      } catch (error) {
        console.error('Error searching quizzes:', error);
      }

      // Search users
      try {
        const userResponse = await socialService.searchUsers(searchQuery);
        const userResults: SearchResult[] = userResponse.data
          .slice(0, 3)
          .map(user => ({
            id: user.id,
            title: user.name || user.username,
            type: 'user' as const,
            description: `@${user.username}`,
            avatar: user.avatar_url,
          }));
        searchResults.push(...userResults);
      } catch (error) {
        console.error('Error searching users:', error);
      }

      // Fallback to mock data if no results
      if (searchResults.length === 0) {
        searchResults.push(
          {
            id: '1',
            title: 'KPSS Matematik Quiz',
            type: 'quiz',
            description: '50 soruluk matematik testi',
          },
          {
            id: '2',
            title: 'Matematik Formülleri',
            type: 'content',
            description: 'Temel matematik formülleri',
          },
          {
            id: '3',
            title: 'Ahmet Yılmaz',
            type: 'user',
            description: 'KPSS öğrencisi',
          },
        );
      }

      setResults(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      // Fallback to mock data
      setResults([
        {
          id: '1',
          title: 'KPSS Matematik Quiz',
          type: 'quiz',
          description: '50 soruluk matematik testi',
        },
        {
          id: '2',
          title: 'Matematik Formülleri',
          type: 'content',
          description: 'Temel matematik formülleri',
        },
        {
          id: '3',
          title: 'Ahmet Yılmaz',
          type: 'user',
          description: 'KPSS öğrencisi',
        },
      ]);
    } finally {
      setIsSearching(false);
    }

    onSearch(searchQuery);
  };

  const renderSearchResult = ({item}: {item: SearchResult}) => (
    <TouchableOpacity style={styles.resultItem}>
      <View style={styles.resultIcon}>
        <Icon
          name={
            item.type === 'quiz'
              ? 'help-circle'
              : item.type === 'content'
              ? 'document-text'
              : 'person'
          }
          size={20}
          color="#6B7280"
        />
      </View>
      <View style={styles.resultContent}>
        <Text style={styles.resultTitle}>{item.title}</Text>
        {item.description && (
          <Text style={styles.resultDescription}>{item.description}</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen">
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.searchContainer}>
            <Icon name="search" size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Quiz, içerik veya kullanıcı ara..."
              placeholderTextColor="#6B7280"
              value={query}
              onChangeText={setQuery}
              onSubmitEditing={() => handleSearch(query)}
              autoFocus
            />
            {query.length > 0 && (
              <TouchableOpacity onPress={() => setQuery('')}>
                <Icon name="close-circle" size={20} color="#6B7280" />
              </TouchableOpacity>
            )}
          </View>
          <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
            <Text style={styles.cancelText}>İptal</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {query.length === 0 ? (
            <>
              {/* Recent Searches */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Son Aramalar</Text>
                {recentSearches.map((search, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.searchItem}
                    onPress={() => {
                      setQuery(search);
                      handleSearch(search);
                    }}>
                    <Icon name="time" size={16} color="#6B7280" />
                    <Text style={styles.searchItemText}>{search}</Text>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Popular Searches */}
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Popüler Aramalar</Text>
                {popularSearches.map((search, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.searchItem}
                    onPress={() => {
                      setQuery(search);
                      handleSearch(search);
                    }}>
                    <Icon name="trending-up" size={16} color="#6B7280" />
                    <Text style={styles.searchItemText}>{search}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </>
          ) : (
            <>
              {/* Search Results */}
              {isSearching ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Aranıyor...</Text>
                </View>
              ) : (
                <FlatList
                  data={results}
                  renderItem={renderSearchResult}
                  keyExtractor={item => item.id}
                  showsVerticalScrollIndicator={false}
                />
              )}
            </>
          )}
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 12,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#0E0D1B',
  },
  cancelButton: {
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  cancelText: {
    fontSize: 16,
    color: '#1E13EC',
    fontWeight: '500',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 12,
  },
  searchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  searchItemText: {
    fontSize: 16,
    color: '#0E0D1B',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  resultContent: {
    flex: 1,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  resultDescription: {
    fontSize: 14,
    color: '#6B7280',
  },
});
