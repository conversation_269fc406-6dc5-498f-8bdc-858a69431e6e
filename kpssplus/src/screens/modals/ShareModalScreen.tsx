import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
} from 'react-native';

import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';

interface ShareModalProps {
  visible: boolean;
  onClose: () => void;
  content: {
    title: string;
    description?: string;
    url?: string;
  };
}

export const ShareModalScreen: React.FC<ShareModalProps> = ({
  visible,
  onClose,
  content,
}) => {
  const shareOptions = [
    {
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: 'logo-whatsapp',
      color: '#25D366',
    },
    {
      id: 'instagram',
      name: 'Instagram',
      icon: 'logo-instagram',
      color: '#E4405F',
    },
    {
      id: 'twitter',
      name: 'Twitter',
      icon: 'logo-twitter',
      color: '#1DA1F2',
    },
    {
      id: 'facebook',
      name: 'Facebook',
      icon: 'logo-facebook',
      color: '#1877F2',
    },
    {
      id: 'telegram',
      name: 'Telegram',
      icon: 'paper-plane',
      color: '#0088CC',
    },
    {
      id: 'copy',
      name: '<PERSON><PERSON><PERSON>',
      icon: 'copy',
      color: '#6B7280',
    },
  ];

  const handleShare = (optionId: string) => {
    // Handle share logic based on optionId
    console.log(`Sharing via ${optionId}:`, content);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      transparent>
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.dragHandle} />
            <Text style={styles.headerTitle}>Paylaş</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Icon name="close" size={24} color="#71717A" />
            </TouchableOpacity>
          </View>

          {/* Content Preview */}
          <View style={styles.contentPreview}>
            <View style={styles.previewCard}>
              <Text style={styles.previewTitle} numberOfLines={2}>
                {content.title}
              </Text>
              {content.description && (
                <Text style={styles.previewDescription} numberOfLines={3}>
                  {content.description}
                </Text>
              )}
            </View>
          </View>

          {/* Share Options */}
          <ScrollView
            style={styles.optionsContainer}
            showsVerticalScrollIndicator={false}>
            <View style={styles.optionsGrid}>
              {shareOptions.map(option => (
                <TouchableOpacity
                  key={option.id}
                  style={styles.shareOption}
                  onPress={() => handleShare(option.id)}>
                  <View
                    style={[
                      styles.shareIconContainer,
                      {backgroundColor: option.color},
                    ]}>
                    <Icon name={option.icon} size={24} color={colors.white} />
                  </View>
                  <Text style={styles.shareOptionText}>{option.name}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>

          {/* Cancel Button */}
          <View style={styles.footer}>
            <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
              <Text style={styles.cancelButtonText}>İptal</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    position: 'relative',
  },
  dragHandle: {
    position: 'absolute',
    top: 8,
    left: '50%',
    marginLeft: -20,
    width: 40,
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    flex: 1,
    textAlign: 'center',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentPreview: {
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  previewCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 8,
  },
  previewDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  optionsContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingVertical: 16,
  },
  shareOption: {
    width: '30%',
    alignItems: 'center',
    marginBottom: 24,
  },
  shareIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  shareOptionText: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
    fontWeight: '500',
  },
  footer: {
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#F3F4F6',
  },
  cancelButton: {
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
});
