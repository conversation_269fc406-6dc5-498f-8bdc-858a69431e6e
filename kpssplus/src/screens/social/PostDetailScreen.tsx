import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  TextInput,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';
import {Post, Comment} from '../../services/api/types';

interface Props {
  navigation?: any;
  route?: {
    params?: {
      postId?: string;
    };
  };
}

export const PostDetailScreen: React.FC<Props> = ({navigation, route}) => {
  const {postId} = route?.params || {};
  const [post, setPost] = useState<Post | null>(null);
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);

  // Mock post data - fallback
  const mockPost = {
    id: '1',
    user: {
      name: 'Elif Kaya',
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
    },
    content:
      'KPSS yolculuğuma başlıyorum! Yeni başlayanlar için tavsiyeleriniz var mı? Özellikle matematik konularında hangi kaynakları önerirsiniz?',
    timeAgo: '1g önce',
    likes: 23,
    comments: 5,
    shares: 1,
    isLiked: true,
  };

  // Mock comments
  const mockComments = [
    {
      id: '1',
      user: {
        name: 'Ahmet Yılmaz',
        avatar: 'A',
      },
      content: 'Matematik için kesinlikle Aydın Yayınları öneriyorum!',
      timeAgo: '30dk önce',
      likes: 5,
    },
    {
      id: '2',
      user: {
        name: 'Zeynep Demir',
        avatar: 'Z',
      },
      content:
        'Benim de aynı durumum vardı. Günlük çalışma programı çok önemli.',
      timeAgo: '45dk önce',
      likes: 3,
    },
  ];

  const loadPostDetail = async () => {
    if (!postId) {
      setPost(mockPost as unknown as Post);
      setComments(mockComments as unknown as Comment[]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const [postData, commentsData] = await Promise.all([
        socialService.getPost(postId),
        socialService.getPostComments(postId),
      ]);
      setPost(postData);
      setComments(commentsData.data);
    } catch (error: any) {
      console.error('Error loading post detail:', error);
      // Use mock data as fallback
      setPost(mockPost as unknown as Post);
      setComments(mockComments as unknown as Comment[]);
      Alert.alert(
        'Bilgi',
        'Gönderi detayları yüklenirken bir sorun oluştu. Örnek veriler gösteriliyor.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleLikePost = async () => {
    if (!post) {
      return;
    }

    try {
      await socialService.likePost(post.id);
      setPost(prevPost =>
        prevPost
          ? {
              ...prevPost,
              like_count: prevPost.is_liked
                ? prevPost.like_count - 1
                : prevPost.like_count + 1,
              is_liked: !prevPost.is_liked,
            }
          : null,
      );
    } catch (error: any) {
      console.error('Error liking post:', error);
      Alert.alert('Hata', 'Beğeni işlemi başarısız oldu.');
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim() || !post) {
      return;
    }

    try {
      setIsSubmittingComment(true);
      const comment = await socialService.createComment(
        post.id,
        newComment.trim(),
      );
      setComments(prevComments => [comment, ...prevComments]);
      setNewComment('');
      Alert.alert('Başarılı', 'Yorumunuz eklendi.');
    } catch (error: any) {
      console.error('Error submitting comment:', error);
      Alert.alert('Hata', 'Yorum eklenirken bir hata oluştu.');
    } finally {
      setIsSubmittingComment(false);
    }
  };

  useEffect(() => {
    loadPostDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postId]);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation?.goBack()}>
            <Icon name="arrow-back" size={24} color="#71717A" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Gönderi</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Icon name="ellipsis-horizontal" size={24} color="#71717A" />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Gönderi yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!post) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation?.goBack()}>
            <Icon name="arrow-back" size={24} color="#71717A" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Gönderi</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Icon name="ellipsis-horizontal" size={24} color="#71717A" />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <Text style={styles.errorText}>Gönderi bulunamadı.</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation?.goBack()}>
            <Icon name="arrow-back" size={24} color="#71717A" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Gönderi</Text>
          <TouchableOpacity style={styles.moreButton}>
            <Icon name="ellipsis-horizontal" size={24} color="#71717A" />
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Post */}
          <View style={styles.postCard}>
            {/* Post Header */}
            <View style={styles.postHeader}>
              <Image
                source={{
                  uri: post.user.avatar_url || 'https://via.placeholder.com/40',
                }}
                style={styles.avatar}
              />
              <View style={styles.postUserInfo}>
                <Text style={styles.userName}>{post.user.name}</Text>
                <Text style={styles.timeAgo}>
                  {new Date(post.created_at).toLocaleDateString('tr-TR')}
                </Text>
              </View>
            </View>

            {/* Post Content */}
            <Text style={styles.postContent}>{post.content}</Text>

            {/* Post Actions */}
            <View style={styles.postActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleLikePost}>
                <Icon
                  name={post.is_liked ? 'heart' : 'heart-outline'}
                  size={20}
                  color={post.is_liked ? '#EF4444' : '#6B7280'}
                />
                <Text style={styles.actionText}>{post.like_count}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Icon name="chatbubble-outline" size={20} color="#6B7280" />
                <Text style={styles.actionText}>{post.comment_count}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Icon name="paper-plane-outline" size={20} color="#6B7280" />
                <Text style={styles.actionText}>{post.share_count}</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Comments Section */}
          <View style={styles.commentsSection}>
            <Text style={styles.commentsTitle}>
              Yorumlar ({comments.length})
            </Text>

            {comments.map(comment => (
              <View key={comment.id} style={styles.commentCard}>
                <View style={styles.commentAvatar}>
                  <Text style={styles.commentAvatarText}>
                    {comment.user.name.charAt(0).toUpperCase()}
                  </Text>
                </View>
                <View style={styles.commentContent}>
                  <View style={styles.commentHeader}>
                    <Text style={styles.commentUserName}>
                      {comment.user.name}
                    </Text>
                    <Text style={styles.commentTimeAgo}>
                      {new Date(comment.created_at).toLocaleDateString('tr-TR')}
                    </Text>
                  </View>
                  <Text style={styles.commentText}>{comment.content}</Text>
                  <TouchableOpacity style={styles.commentLikeButton}>
                    <Icon name="heart-outline" size={16} color="#6B7280" />
                    <Text style={styles.commentLikes}>
                      {comment.like_count}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            ))}
          </View>
        </ScrollView>

        {/* Comment Input */}
        <View style={styles.commentInputContainer}>
          <View style={styles.commentInputAvatar}>
            <Text style={styles.commentInputAvatarText}>S</Text>
          </View>
          <TextInput
            style={styles.commentInput}
            placeholder="Yorum yaz..."
            placeholderTextColor="#6B7280"
            multiline
            value={newComment}
            onChangeText={setNewComment}
          />
          <TouchableOpacity
            style={[
              styles.sendButton,
              (!newComment.trim() || isSubmittingComment) &&
                styles.sendButtonDisabled,
            ]}
            onPress={handleSubmitComment}
            disabled={!newComment.trim() || isSubmittingComment}>
            {isSubmittingComment ? (
              <ActivityIndicator size="small" color="#1E13EC" />
            ) : (
              <Icon name="send" size={20} color="#1E13EC" />
            )}
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    flex: 1,
    textAlign: 'center',
  },
  moreButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  postCard: {
    backgroundColor: colors.white,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    gap: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  postUserInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  timeAgo: {
    fontSize: 14,
    color: '#6B7280',
  },
  postContent: {
    fontSize: 16,
    color: '#111827',
    paddingHorizontal: 16,
    marginTop: 8,
    lineHeight: 24,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingVertical: 4,
    marginTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  commentsSection: {
    backgroundColor: colors.white,
    paddingTop: 16,
  },
  commentsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  commentCard: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  commentAvatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#E4E4E7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  commentAvatarText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#71717A',
  },
  commentContent: {
    flex: 1,
  },
  commentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4,
  },
  commentUserName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0E0D1B',
  },
  commentTimeAgo: {
    fontSize: 12,
    color: '#6B7280',
  },
  commentText: {
    fontSize: 14,
    color: '#0E0D1B',
    lineHeight: 20,
    marginBottom: 8,
  },
  commentLikeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  commentLikes: {
    fontSize: 12,
    color: '#6B7280',
  },
  commentInputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  commentInputAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#E4E4E7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  commentInputAvatarText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#71717A',
  },
  commentInput: {
    flex: 1,
    backgroundColor: '#F4F4F5',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 14,
    color: '#0E0D1B',
    maxHeight: 100,
  },
  sendButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    opacity: 0.5,
  },
});
