import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';
import {UserInfo} from '../../services/api/types';

interface Props {
  navigation?: any;
}

export const FriendsScreen: React.FC<Props> = ({navigation}) => {
  const [friends, setFriends] = useState<UserInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock friends data - fallback
  const mockFriends = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      avatar: 'A',
      status: 'online',
      mutualFriends: 12,
    },
    {
      id: '2',
      name: '<PERSON><PERSON>',
      avatar: 'E',
      status: 'offline',
      mutualFriends: 8,
    },
    {
      id: '3',
      name: 'Mehmet Demir',
      avatar: 'M',
      status: 'online',
      mutualFriends: 15,
    },
    {
      id: '4',
      name: 'Zeynep Öz',
      avatar: 'Z',
      status: 'offline',
      mutualFriends: 6,
    },
  ];

  const loadFriends = async () => {
    try {
      setIsLoading(true);
      const friendsData = await socialService.getFriends();
      setFriends(friendsData.data as unknown as UserInfo[]);
    } catch (error: any) {
      console.error('Error loading friends:', error);
      // Use mock data as fallback
      setFriends(mockFriends as unknown as UserInfo[]);
      Alert.alert(
        'Bilgi',
        'Arkadaş listesi yüklenirken bir sorun oluştu. Örnek veriler gösteriliyor.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadFriends();
    setRefreshing(false);
  };

  const handleFollowUser = async (userId: string) => {
    try {
      await socialService.followUser(userId);
      Alert.alert('Başarılı', 'Kullanıcı takip edildi.');
      await loadFriends(); // Refresh the list
    } catch (error: any) {
      console.error('Error following user:', error);
      Alert.alert('Hata', 'Takip işlemi başarısız oldu.');
    }
  };

  const filteredFriends = friends.filter(friend =>
    friend.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  useEffect(() => {
    loadFriends();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation?.goBack()}>
            <Icon name="arrow-back" size={24} color="#71717A" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Arkadaşlar</Text>
          <TouchableOpacity style={styles.searchButton}>
            <Icon name="search" size={24} color="#71717A" />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Arkadaşlar yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="arrow-back" size={24} color="#71717A" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Arkadaşlar</Text>
        <TouchableOpacity style={styles.searchButton}>
          <Icon name="search" size={24} color="#71717A" />
        </TouchableOpacity>
      </View>

      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <Icon
            name="search"
            size={20}
            color="#6B7280"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Arkadaş ara..."
            placeholderTextColor="#6B7280"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Friends List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Arkadaşlarım ({filteredFriends.length})
          </Text>

          {filteredFriends.map(friend => (
            <TouchableOpacity key={friend.id} style={styles.friendCard}>
              <View style={styles.friendAvatar}>
                <Text style={styles.friendAvatarText}>
                  {friend.name.charAt(0).toUpperCase()}
                </Text>
                <View
                  style={[
                    styles.statusIndicator,
                    friend.is_active
                      ? styles.onlineStatus
                      : styles.offlineStatus,
                  ]}
                />
              </View>
              <View style={styles.friendInfo}>
                <Text style={styles.friendName}>{friend.name}</Text>
                <Text style={styles.mutualFriends}>
                  {friend.is_verified ? 'Doğrulanmış hesap' : 'Kullanıcı'}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.moreButton}
                onPress={() => handleFollowUser(friend.id)}>
                <Icon name="person-add" size={20} color="#1E13EC" />
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
    backgroundColor: colors.white,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    flex: 1,
    textAlign: 'center',
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F4F4F5',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchIcon: {
    color: '#6B7280',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#0E0D1B',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
  },
  friendCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingVertical: 12,
    paddingHorizontal: 4,
    marginBottom: 8,
    gap: 16,
  },
  friendAvatar: {
    position: 'relative',
  },
  friendAvatarText: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E4E4E7',
    textAlign: 'center',
    lineHeight: 48,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#71717A',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 12,
    height: 12,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: colors.white,
  },
  onlineStatus: {
    backgroundColor: '#10B981',
  },
  offlineStatus: {
    backgroundColor: '#6B7280',
  },
  friendInfo: {
    flex: 1,
  },
  friendName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  mutualFriends: {
    fontSize: 14,
    color: '#71717A',
  },
  moreButton: {
    padding: 8,
  },
});
