import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';
import {UserInfo} from '../../services/api/types';

interface Props {
  navigation?: any;
}

export const UserSearchScreen: React.FC<Props> = ({navigation}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [users, setUsers] = useState<UserInfo[]>([]);
  const [searchResults, setSearchResults] = useState<UserInfo[]>([]);

  const mockUsers = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      avatar_url:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
      is_verified: false,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '2',
      name: 'Ayşe Demir',
      email: '<EMAIL>',
      avatar_url:
        'https://images.unsplash.com/photo-1494790108755-2616b612b5e5?w=100',
      is_verified: true,
      is_active: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '3',
      name: 'Mehmet Kaya',
      email: '<EMAIL>',
      is_verified: false,
      is_active: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  const handleSearch = async (query: string) => {
    setSearchQuery(query);
    if (query.trim().length > 0) {
      setIsSearching(true);
      try {
        const results = await socialService.searchUsers(query.trim());
        setSearchResults(results.data);
      } catch (error: any) {
        console.error('Error searching users:', error);
        // Use mock data as fallback
        const filteredUsers = mockUsers.filter(user =>
          user.name.toLowerCase().includes(query.toLowerCase()),
        );
        setSearchResults(filteredUsers as unknown as UserInfo[]);
        Alert.alert(
          'Bilgi',
          'Kullanıcı arama sırasında bir sorun oluştu. Örnek veriler gösteriliyor.',
        );
      } finally {
        setIsSearching(false);
      }
    } else {
      setSearchResults([]);
    }
  };

  const handleFollowUser = async (userId: string) => {
    try {
      await socialService.followUser(userId);
      setSearchResults(prev =>
        prev.map(user =>
          user.id === userId ? {...user, is_active: !user.is_active} : user,
        ),
      );
      Alert.alert('Başarılı', 'Kullanıcı takip edildi.');
    } catch (error: any) {
      console.error('Error following user:', error);
      Alert.alert('Hata', 'Takip işlemi başarısız oldu.');
    }
  };

  useEffect(() => {
    // Load initial popular users
    const loadPopularUsers = async () => {
      try {
        const popularUsers = await socialService.searchUsers('');
        setUsers(popularUsers.data.slice(0, 10)); // Show top 10
      } catch (error: any) {
        console.error('Error loading popular users:', error);
        setUsers(mockUsers.slice(0, 3) as unknown as UserInfo[]);
      }
    };

    loadPopularUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const renderUser = ({item}: {item: UserInfo}) => (
    <TouchableOpacity style={styles.userItem}>
      <View style={styles.userInfo}>
        {item.avatar_url ? (
          <Image source={{uri: item.avatar_url}} style={styles.userAvatar} />
        ) : (
          <View style={styles.userAvatarPlaceholder}>
            <Text style={styles.userAvatarText}>
              {item.name.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
        <View style={styles.userDetails}>
          <Text style={styles.userName}>{item.name}</Text>
          <Text style={styles.userUsername}>{item.email}</Text>
          <View style={styles.userStats}>
            <Text style={styles.userScore}>
              {item.is_verified ? 'Doğrulanmış' : 'Kullanıcı'}
            </Text>
            <Text style={styles.mutualFriends}>
              {item.is_active ? 'Aktif' : 'Pasif'}
            </Text>
          </View>
        </View>
      </View>
      <TouchableOpacity
        style={[
          styles.followButton,
          item.is_active && styles.followButtonActive,
        ]}
        onPress={() => handleFollowUser(item.id)}>
        <Text
          style={[
            styles.followButtonText,
            item.is_active && styles.followButtonTextActive,
          ]}>
          Takip Et
        </Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="arrow-back" size={24} color="#71717A" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Kullanıcı Ara</Text>
      </View>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#6B7280" />
        <TextInput
          style={styles.searchInput}
          placeholder="İsim veya email ara..."
          placeholderTextColor="#6B7280"
          value={searchQuery}
          onChangeText={handleSearch}
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 && (
          <TouchableOpacity onPress={() => handleSearch('')}>
            <Icon name="close-circle" size={20} color="#6B7280" />
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        {isSearching ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Aranıyor...</Text>
          </View>
        ) : searchQuery.length === 0 ? (
          <View>
            <Text style={styles.sectionTitle}>Popüler Kullanıcılar</Text>
            <FlatList
              data={users}
              renderItem={renderUser}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
            />
          </View>
        ) : searchResults.length > 0 ? (
          <View>
            <Text style={styles.sectionTitle}>
              Arama Sonuçları ({searchResults.length})
            </Text>
            <FlatList
              data={searchResults}
              renderItem={renderUser}
              keyExtractor={item => item.id}
              showsVerticalScrollIndicator={false}
            />
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Icon name="search" size={64} color="#E5E7EB" />
            <Text style={styles.emptyTitle}>Sonuç Bulunamadı</Text>
            <Text style={styles.emptyDescription}>
              "{searchQuery}" için kullanıcı bulunamadı
            </Text>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
    backgroundColor: colors.white,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    flex: 1,
    textAlign: 'center',
    marginRight: 40,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 12,
    marginHorizontal: 16,
    marginBottom: 16,
    gap: 8,
  },
  searchInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
    color: '#0E0D1B',
  },
  content: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  loadingState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  usersList: {
    paddingHorizontal: 16,
  },
  userItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  userAvatarPlaceholder: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#1E13EC',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  userAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    marginBottom: 2,
  },
  userUsername: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  userStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  userScore: {
    fontSize: 12,
    color: '#1E13EC',
    fontWeight: '500',
  },
  mutualFriends: {
    fontSize: 12,
    color: '#6B7280',
  },
  followButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#1E13EC',
    backgroundColor: colors.white,
  },
  followButtonActive: {
    backgroundColor: '#1E13EC',
  },
  followButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E13EC',
  },
  followButtonTextActive: {
    color: colors.white,
  },
});
