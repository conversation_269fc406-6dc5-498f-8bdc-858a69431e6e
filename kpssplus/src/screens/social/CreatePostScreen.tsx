import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  ActivityIndicator,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';

interface Props {
  navigation?: any;
}

export const CreatePostScreen: React.FC<Props> = ({navigation}) => {
  const [postText, setPostText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCreatePost = async () => {
    if (!postText.trim()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await socialService.createPost(postText.trim());
      Alert.alert('<PERSON><PERSON><PERSON><PERSON>l<PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON> paylaşıldı.', [
        {
          text: 'Tamam',
          onPress: () => navigation?.goBack(),
        },
      ]);
    } catch (error: any) {
      console.error('Error creating post:', error);
      Alert.alert('Hata', 'Gönderi paylaşılırken bir hata oluştu.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (postText.trim()) {
      Alert.alert('Emin misiniz?', 'Yazdığınız gönderi kaybolacak.', [
        {text: 'İptal', style: 'cancel'},
        {text: 'Çık', onPress: () => navigation?.goBack()},
      ]);
    } else {
      navigation?.goBack();
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
            <Text style={styles.cancelText}>İptal</Text>
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Yeni Gönderi</Text>
          <TouchableOpacity
            style={[
              styles.shareButton,
              postText.trim().length > 0 && !isSubmitting
                ? styles.shareButtonActive
                : null,
            ]}
            onPress={handleCreatePost}
            disabled={!postText.trim() || isSubmitting}>
            {isSubmitting ? (
              <ActivityIndicator size="small" color="#1E13EC" />
            ) : (
              <Text
                style={[
                  styles.shareText,
                  postText.trim().length > 0 ? styles.shareTextActive : null,
                ]}>
                Paylaş
              </Text>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* User Info */}
          <View style={styles.userSection}>
            <View style={styles.userAvatar}>
              <Text style={styles.userAvatarText}>S</Text>
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>Sen</Text>
              <TouchableOpacity style={styles.privacyButton}>
                <Icon name="people" size={16} color="#6B7280" />
                <Text style={styles.privacyText}>Herkese açık</Text>
                <Icon name="chevron-down" size={16} color="#6B7280" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Post Input */}
          <View style={styles.postInputSection}>
            <TextInput
              style={styles.postInput}
              placeholder="Ne düşünüyorsun?"
              placeholderTextColor="#6B7280"
              multiline
              value={postText}
              onChangeText={setPostText}
              autoFocus
            />
          </View>

          {/* Post Options */}
          <View style={styles.optionsSection}>
            <Text style={styles.optionsTitle}>Gönderine ekle</Text>

            <View style={styles.optionsList}>
              <TouchableOpacity style={styles.optionItem}>
                <View style={styles.optionIcon}>
                  <Icon name="image" size={24} color="#10B981" />
                </View>
                <Text style={styles.optionText}>Fotoğraf/Video</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionItem}>
                <View style={styles.optionIcon}>
                  <Icon name="location" size={24} color="#EF4444" />
                </View>
                <Text style={styles.optionText}>Konum</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionItem}>
                <View style={styles.optionIcon}>
                  <Icon name="happy" size={24} color="#F59E0B" />
                </View>
                <Text style={styles.optionText}>Duygu/Aktivite</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.optionItem}>
                <View style={styles.optionIcon}>
                  <Icon name="pricetag" size={24} color="#8B5CF6" />
                </View>
                <Text style={styles.optionText}>Etiket</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: colors.white,
  },
  cancelButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  cancelText: {
    fontSize: 16,
    color: '#6B7280',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
  },
  shareButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#F4F4F5',
  },
  shareButtonActive: {
    backgroundColor: '#1E13EC',
  },
  shareText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280',
  },
  shareTextActive: {
    color: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E4E4E7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  userAvatarText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#71717A',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  privacyButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F4F4F5',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    alignSelf: 'flex-start',
    gap: 4,
  },
  privacyText: {
    fontSize: 14,
    color: '#6B7280',
  },
  postInputSection: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  postInput: {
    fontSize: 18,
    color: '#0E0D1B',
    lineHeight: 26,
    minHeight: 120,
    textAlignVertical: 'top',
  },
  optionsSection: {
    paddingHorizontal: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  optionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    marginBottom: 16,
  },
  optionsList: {
    gap: 12,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    gap: 16,
  },
  optionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white,
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    color: '#0E0D1B',
    fontWeight: '500',
  },
});
