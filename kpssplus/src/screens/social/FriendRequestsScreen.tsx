import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Image,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, spacing} from '../../theme';
import {socialService} from '../../services/api/socialService';
import {UserInfo} from '../../services/api/types';

interface Props {
  navigation: any;
}

interface FriendRequest {
  id: string;
  from_user: UserInfo;
  to_user: UserInfo;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
}

export const FriendRequestsScreen: React.FC<Props> = ({navigation}) => {
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [processingRequests, setProcessingRequests] = useState<Set<string>>(
    new Set(),
  );

  useEffect(() => {
    loadFriendRequests();
  }, []);

  const loadFriendRequests = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }

      // Since we don't have a specific friend requests endpoint,
      // we'll use mock data that simulates real API behavior
      const mockRequests: FriendRequest[] = [
        {
          id: '1',
          from_user: {
            id: '1',
            username: 'ahmet_kpss',
            name: 'Ahmet Yılmaz',
            first_name: 'Ahmet',
            last_name: 'Yılmaz',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: true,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          to_user: {
            id: 'current_user',
            username: 'current_user',
            name: 'Current User',
            first_name: 'Current',
            last_name: 'User',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: false,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          status: 'pending',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          from_user: {
            id: '2',
            username: 'fatma_study',
            name: 'Fatma Demir',
            first_name: 'Fatma',
            last_name: 'Demir',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: false,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          to_user: {
            id: 'current_user',
            username: 'current_user',
            name: 'Current User',
            first_name: 'Current',
            last_name: 'User',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: false,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          status: 'pending',
          created_at: '2024-01-15T11:15:00Z',
          updated_at: '2024-01-15T11:15:00Z',
        },
        {
          id: '3',
          from_user: {
            id: '3',
            username: 'mehmet_pro',
            name: 'Mehmet Kaya',
            first_name: 'Mehmet',
            last_name: 'Kaya',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: true,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          to_user: {
            id: 'current_user',
            username: 'current_user',
            name: 'Current User',
            first_name: 'Current',
            last_name: 'User',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: false,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          status: 'pending',
          created_at: '2024-01-15T12:00:00Z',
          updated_at: '2024-01-15T12:00:00Z',
        },
      ];

      setFriendRequests(mockRequests);
    } catch (error) {
      console.error('Error loading friend requests:', error);
      Alert.alert('Hata', 'Arkadaşlık istekleri yüklenirken bir hata oluştu');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleAcceptRequest = async (requestId: string, userId: string) => {
    try {
      setProcessingRequests(prev => new Set(prev).add(requestId));

      // Use the follow API as a proxy for accepting friend requests
      await socialService.followUser(userId);

      // Remove the request from the list
      setFriendRequests(prev => prev.filter(req => req.id !== requestId));

      Alert.alert('Başarılı', 'Arkadaşlık isteği kabul edildi');
    } catch (error) {
      console.error('Error accepting friend request:', error);
      Alert.alert('Hata', 'Arkadaşlık isteği kabul edilirken bir hata oluştu');
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const handleRejectRequest = async (requestId: string) => {
    try {
      setProcessingRequests(prev => new Set(prev).add(requestId));

      // Simulate API call for rejecting request
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Remove the request from the list
      setFriendRequests(prev => prev.filter(req => req.id !== requestId));

      Alert.alert('Başarılı', 'Arkadaşlık isteği reddedildi');
    } catch (error) {
      console.error('Error rejecting friend request:', error);
      Alert.alert('Hata', 'Arkadaşlık isteği reddedilirken bir hata oluştu');
    } finally {
      setProcessingRequests(prev => {
        const newSet = new Set(prev);
        newSet.delete(requestId);
        return newSet;
      });
    }
  };

  const onRefresh = () => {
    loadFriendRequests(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderFriendRequest = ({item}: {item: FriendRequest}) => {
    const isProcessing = processingRequests.has(item.id);

    return (
      <View style={styles.requestItem}>
        <TouchableOpacity
          style={styles.userInfo}
          onPress={() =>
            navigation.navigate('Profile', {userId: item.from_user.id})
          }>
          <View style={styles.avatarContainer}>
            {item.from_user.avatar_url ? (
              <Image
                source={{uri: item.from_user.avatar_url}}
                style={styles.avatar}
              />
            ) : (
              <View style={styles.avatarPlaceholder}>
                <Text style={styles.avatarText}>
                  {item.from_user.first_name?.[0] || item.from_user.username[0]}
                </Text>
              </View>
            )}
            {item.from_user.is_verified && (
              <Icon
                name="checkmark-circle"
                size={16}
                color={colors.primary}
                style={styles.verifiedIcon}
              />
            )}
          </View>

          <View style={styles.userDetails}>
            <Text style={styles.userName}>
              {item.from_user.first_name && item.from_user.last_name
                ? `${item.from_user.first_name} ${item.from_user.last_name}`
                : item.from_user.username}
            </Text>
            <Text style={styles.userHandle}>@{item.from_user.username}</Text>
            <Text style={styles.requestDate}>
              {formatDate(item.created_at)}
            </Text>
          </View>
        </TouchableOpacity>

        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={() => handleRejectRequest(item.id)}
            disabled={isProcessing}>
            {isProcessing ? (
              <ActivityIndicator size="small" color={colors.error} />
            ) : (
              <Icon name="close" size={20} color={colors.error} />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, styles.acceptButton]}
            onPress={() => handleAcceptRequest(item.id, item.from_user.id)}
            disabled={isProcessing}>
            {isProcessing ? (
              <ActivityIndicator size="small" color={colors.surface} />
            ) : (
              <Icon name="checkmark" size={20} color={colors.surface} />
            )}
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Arkadaşlık İstekleri</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>
            Arkadaşlık istekleri yükleniyor...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Arkadaşlık İstekleri</Text>
        <View style={styles.placeholder} />
      </View>

      <FlatList
        data={friendRequests}
        keyExtractor={item => item.id}
        renderItem={renderFriendRequest}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon
              name="people-outline"
              size={64}
              color={colors.text.secondary}
            />
            <Text style={styles.emptyTitle}>Arkadaşlık İsteği Yok</Text>
            <Text style={styles.emptyDescription}>
              Henüz arkadaşlık isteğiniz bulunmuyor.
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h3,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginTop: spacing[3],
  },
  listContainer: {
    padding: spacing[4],
  },
  requestItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    padding: spacing[4],
    marginBottom: spacing[3],
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    ...typography.h3,
    color: colors.surface,
    fontWeight: 'bold',
  },
  verifiedIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: colors.surface,
    borderRadius: 8,
  },
  userDetails: {
    flex: 1,
    marginLeft: spacing[3],
  },
  userName: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
  },
  userHandle: {
    ...typography.body2,
    color: colors.text.secondary,
    marginTop: spacing[1],
  },
  requestDate: {
    ...typography.caption,
    color: colors.text.secondary,
    marginTop: spacing[1],
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: spacing[2],
  },
  rejectButton: {
    backgroundColor: colors.error + '20',
  },
  acceptButton: {
    backgroundColor: colors.primary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    ...typography.h3,
    color: colors.text.primary,
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
