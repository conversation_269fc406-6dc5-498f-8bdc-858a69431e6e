import React, {useState, useEffect, useCallback, useMemo} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';
// import {UserInfo} from '../../services/api/types';

type Props = {};

interface LeaderboardUser {
  id: string;
  username: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  totalScore: number;
  rank: number;
  isCurrentUser?: boolean;
}

export const LeaderboardScreen: React.FC<Props> = () => {
  const {user, isAuthenticated} = useAuth();
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentUserRank, setCurrentUserRank] =
    useState<LeaderboardUser | null>(null);

  // Mock data - in real app this would come from API
  const mockLeaderboard: LeaderboardUser[] = useMemo(
    () => [
      {
        id: '1',
        username: 'ahmet_kpss',
        firstName: 'Ahmet',
        totalScore: 19260,
        rank: 1,
        avatar: undefined,
      },
      {
        id: '2',
        username: 'elif_study',
        firstName: 'Elif',
        totalScore: 17972,
        rank: 2,
        avatar: undefined,
      },
      {
        id: '3',
        username: 'mehmet_pro',
        firstName: 'Mehmet',
        totalScore: 11048,
        rank: 3,
        avatar: undefined,
      },
      {
        id: '4',
        username: 'fatma_winner',
        firstName: 'Fatma',
        totalScore: 10500,
        rank: 4,
        avatar: undefined,
      },
      {
        id: '5',
        username: 'ali_smart',
        firstName: 'Ali',
        totalScore: 10400,
        rank: 5,
        avatar: undefined,
      },
      {
        id: '6',
        username: 'zeynep_ace',
        firstName: 'Zeynep',
        totalScore: 10300,
        rank: 6,
        avatar: undefined,
      },
      {
        id: '7',
        username: 'emre_genius',
        firstName: 'Emre',
        totalScore: 10200,
        rank: 7,
        avatar: undefined,
      },
      {
        id: '8',
        username: 'ayse_brilliant',
        firstName: 'Ayşe',
        totalScore: 9150,
        rank: 8,
        avatar: undefined,
      },
      {
        id: '9',
        username: 'burak_master',
        firstName: 'Burak',
        totalScore: 8690,
        rank: 9,
        avatar: undefined,
      },
    ],
    [],
  );

  const loadLeaderboard = useCallback(async () => {
    setIsLoading(true);
    try {
      if (isAuthenticated) {
        // Load real leaderboard from API
        const response = await socialService.getActivityFeed();

        // Convert activity feed users to leaderboard format
        const leaderboardUsers: LeaderboardUser[] = response.data
          .map((activity, index) => ({
            id: activity.user.id,
            username: activity.user.username,
            firstName: activity.user.first_name,
            lastName: activity.user.last_name,
            avatar: activity.user.avatar_url,
            totalScore: Math.floor(Math.random() * 20000) + 1000, // Mock score
            rank: index + 1,
            isCurrentUser: activity.user.id === user?.id,
          }))
          .slice(0, 10); // Top 10

        setLeaderboard(leaderboardUsers);

        // Set current user rank if not in top 10
        const currentUserInTop10 = leaderboardUsers.find(u => u.isCurrentUser);
        if (!currentUserInTop10 && user) {
          const currentUser: LeaderboardUser = {
            id: user.id,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            totalScore: Math.floor(Math.random() * 5000) + 500,
            rank: Math.floor(Math.random() * 500) + 11,
            isCurrentUser: true,
          };
          setCurrentUserRank(currentUser);
        }
      } else {
        // Fallback to mock data for guests
        setLeaderboard(mockLeaderboard);
      }
    } catch (error) {
      console.error('Error loading leaderboard:', error);
      // Fallback to mock data on error
      setLeaderboard(mockLeaderboard);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, mockLeaderboard]);

  useEffect(() => {
    loadLeaderboard();
  }, [loadLeaderboard]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return null;
    }
  };

  const renderLeaderboardItem = (item: LeaderboardUser) => (
    <TouchableOpacity
      key={item.id}
      style={[
        styles.leaderboardItem,
        item.isCurrentUser && styles.currentUserItem,
      ]}
      onPress={() => console.log('Navigation disabled')}>
      <View style={styles.rankContainer}>
        {getRankIcon(item.rank) ? (
          <Text style={styles.rankIcon}>{getRankIcon(item.rank)}</Text>
        ) : (
          <Text style={styles.rankNumber}>{item.rank}</Text>
        )}
      </View>

      <View style={styles.userInfo}>
        <View style={styles.avatarContainer}>
          {item.avatar ? (
            <Image source={{uri: item.avatar}} style={styles.avatar} />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Icon name="person" size={20} color={colors.white} />
            </View>
          )}
        </View>

        <View style={styles.userDetails}>
          <Text
            style={[
              styles.userName,
              item.isCurrentUser && styles.currentUserText,
            ]}>
            {item.firstName || item.username}
          </Text>
          <Text style={styles.userScore}>
            {item.totalScore.toLocaleString()}
          </Text>
        </View>
      </View>

      <View style={styles.scoreContainer}>
        <Text
          style={[
            styles.scoreText,
            item.isCurrentUser && styles.currentUserText,
          ]}>
          {item.totalScore.toLocaleString()}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => console.log('Go back')}>
          <Icon name="chevron-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Liderlik Tablosu</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={isLoading} onRefresh={loadLeaderboard} />
        }>
        {/* Top 3 Podium */}
        <View style={styles.podiumContainer}>
          {leaderboard.slice(0, 3).map((podiumUser, index) => (
            <TouchableOpacity
              key={podiumUser.id}
              style={[
                styles.podiumItem,
                index === 0
                  ? styles.podium1
                  : index === 1
                  ? styles.podium2
                  : styles.podium3,
              ]}
              onPress={() => console.log('Navigation disabled')}>
              <View style={styles.podiumAvatar}>
                {podiumUser.avatar ? (
                  <Image
                    source={{uri: podiumUser.avatar}}
                    style={styles.podiumAvatarImage}
                  />
                ) : (
                  <View style={styles.podiumAvatarPlaceholder}>
                    <Icon name="person" size={24} color={colors.white} />
                  </View>
                )}
                <Text style={styles.podiumRank}>
                  {getRankIcon(podiumUser.rank)}
                </Text>
              </View>
              <Text style={styles.podiumName}>
                {podiumUser.firstName || podiumUser.username}
              </Text>
              <Text style={styles.podiumScore}>
                {podiumUser.totalScore.toLocaleString()}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Full Leaderboard */}
        <View style={styles.leaderboardContainer}>
          <Text style={styles.sectionTitle}>Tüm Sıralama</Text>
          {leaderboard.map(renderLeaderboardItem)}

          {/* Current User Rank (if not in top 10) */}
          {currentUserRank && (
            <View style={styles.currentUserSection}>
              <View style={styles.separator} />
              <Text style={styles.yourRankTitle}>Sizin Sıralamanız</Text>
              {renderLeaderboardItem(currentUserRank)}
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h5,
    color: colors.text.primary,
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  // Podium Styles
  podiumContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
    backgroundColor: colors.white,
    marginBottom: spacing[2],
  },
  podiumItem: {
    alignItems: 'center',
    marginHorizontal: spacing[2],
  },
  podium1: {
    marginBottom: spacing[4],
  },
  podium2: {
    marginBottom: spacing[2],
  },
  podium3: {
    marginBottom: 0,
  },
  podiumAvatar: {
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  podiumAvatarImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: spacing[1],
  },
  podiumAvatarPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[1],
  },
  podiumRank: {
    fontSize: 24,
  },
  podiumName: {
    ...typography.body2,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing[1],
  },
  podiumScore: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  // Leaderboard Styles
  leaderboardContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing[4],
    paddingTop: spacing[4],
  },
  sectionTitle: {
    ...typography.h6,
    color: colors.text.primary,
    marginBottom: spacing[3],
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  currentUserItem: {
    backgroundColor: colors.primaryMuted,
    marginHorizontal: -spacing[4],
    paddingHorizontal: spacing[4],
    borderRadius: 8,
  },
  rankContainer: {
    width: 40,
    alignItems: 'center',
  },
  rankIcon: {
    fontSize: 20,
  },
  rankNumber: {
    ...typography.h6,
    color: colors.text.secondary,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: spacing[3],
  },
  avatarContainer: {
    marginRight: spacing[3],
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  avatarPlaceholder: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[400],
    alignItems: 'center',
    justifyContent: 'center',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
  },
  userScore: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  currentUserText: {
    color: colors.primary,
  },
  scoreContainer: {
    alignItems: 'flex-end',
  },
  scoreText: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
  },
  // Current User Section
  currentUserSection: {
    marginTop: spacing[4],
  },
  separator: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: spacing[3],
  },
  yourRankTitle: {
    ...typography.body2,
    color: colors.text.secondary,
    marginBottom: spacing[2],
    textAlign: 'center',
  },
});
