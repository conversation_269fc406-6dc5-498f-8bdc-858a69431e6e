import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  RefreshControl,
  ActivityIndicator,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {socialService} from '../../services/api/socialService';
import {Post} from '../../services/api/types';

interface Props {
  navigation?: any;
}

export const TimelineScreen: React.FC<Props> = ({navigation}) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Mock timeline posts - fallback data
  const mockTimelinePosts = [
    {
      id: '1',
      user: {
        name: '<PERSON><PERSON>',
        avatar:
          'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
      },
      content:
        'KPSS yolculuğuma başlıyorum! Yeni başlayanlar için tavsiyeleriniz var mı?',
      timeAgo: '1g önce',
      likes: 23,
      comments: 5,
      shares: 1,
      isLiked: true,
    },
    {
      id: '2',
      user: {
        name: 'Ahmet Demir',
        avatar:
          'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
      },
      content: 'Bugün matematik testinde 85 aldım! Çok mutluyum 🎉',
      timeAgo: '2g önce',
      likes: 45,
      comments: 12,
      shares: 3,
      isLiked: false,
    },
    {
      id: '3',
      user: {
        name: 'Zeynep Yılmaz',
        avatar:
          'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
      },
      content: 'Tarih konularında zorlanıyorum. Hangi kaynakları önerirsiniz?',
      timeAgo: '3g önce',
      likes: 18,
      comments: 8,
      shares: 2,
      isLiked: false,
    },
  ];

  const loadTimelinePosts = async () => {
    try {
      setIsLoading(true);
      const timelineData = await socialService.getActivityFeed();
      setPosts(timelineData.data);
    } catch (error: any) {
      console.error('Error loading timeline posts:', error);
      // Use mock data as fallback
      setPosts(mockTimelinePosts as unknown as Post[]);
      Alert.alert(
        'Bilgi',
        'Zaman çizelgesi verileri yüklenirken bir sorun oluştu. Örnek veriler gösteriliyor.',
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTimelinePosts();
    setRefreshing(false);
  };

  const handleLikePost = async (postId: string) => {
    try {
      await socialService.likePost(postId);
      setPosts(prevPosts =>
        prevPosts.map(post =>
          post.id === postId
            ? {
                ...post,
                like_count: post.is_liked
                  ? post.like_count - 1
                  : post.like_count + 1,
                is_liked: !post.is_liked,
              }
            : post,
        ),
      );
    } catch (error: any) {
      console.error('Error liking post:', error);
      Alert.alert('Hata', 'Beğeni işlemi başarısız oldu.');
    }
  };

  useEffect(() => {
    loadTimelinePosts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Zaman Çizelgesi</Text>
          <TouchableOpacity style={styles.addButton}>
            <Icon name="add" size={24} color={colors.white} />
          </TouchableOpacity>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Zaman çizelgesi yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Zaman Çizelgesi</Text>
        <TouchableOpacity style={styles.addButton}>
          <Icon name="add" size={24} color={colors.white} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Timeline Posts */}
        {posts.map(post => (
          <View key={post.id} style={styles.postCard}>
            {/* Post Header */}
            <View style={styles.postHeader}>
              <Image
                source={{
                  uri: post.user.avatar_url || 'https://via.placeholder.com/40',
                }}
                style={styles.avatar}
              />
              <View style={styles.postUserInfo}>
                <Text style={styles.userName}>{post.user.name}</Text>
                <Text style={styles.timeAgo}>
                  {new Date(post.created_at).toLocaleDateString('tr-TR')}
                </Text>
              </View>
              <TouchableOpacity style={styles.moreButton}>
                <Icon name="ellipsis-horizontal" size={20} color="#6B7280" />
              </TouchableOpacity>
            </View>

            {/* Post Content */}
            <Text style={styles.postContent}>{post.content}</Text>

            {/* Post Actions */}
            <View style={styles.postActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() => handleLikePost(post.id)}>
                <Icon
                  name={post.is_liked ? 'heart' : 'heart-outline'}
                  size={20}
                  color={post.is_liked ? '#EF4444' : '#6B7280'}
                />
                <Text style={styles.actionText}>{post.like_count}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={() =>
                  navigation?.navigate('PostDetail', {postId: post.id})
                }>
                <Icon name="chatbubble-outline" size={20} color="#6B7280" />
                <Text style={styles.actionText}>{post.comment_count}</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.actionButton}>
                <Icon name="paper-plane-outline" size={20} color="#6B7280" />
                <Text style={styles.actionText}>{post.share_count}</Text>
              </TouchableOpacity>
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#1E13EC',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  postCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: 16,
    gap: 16,
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  postUserInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 4,
  },
  timeAgo: {
    fontSize: 14,
    color: '#6B7280',
  },
  moreButton: {
    padding: 4,
  },
  postContent: {
    fontSize: 16,
    color: '#111827',
    paddingHorizontal: 16,
    marginTop: 8,
    lineHeight: 24,
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingVertical: 4,
    marginTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
});
