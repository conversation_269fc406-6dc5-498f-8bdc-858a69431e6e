import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Mock data - mobile design'dan <PERSON><PERSON><PERSON>
const mockTimelineData = [
  {
    id: 1,
    user: {
      name: '<PERSON><PERSON>',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAoewrkmoGv3YzwjeFCPGxHtg0jKaapojudi7V-5Ckm5BxE9K2OtXqgdqiY9pOyFr1bEks6MJQ9Od4XBq2IxgGcecQAGsM89opi1Fxgq52DUghwLX25fP7lffBR32tm8bHbuJbJjI0c29T_Q9n49UlM3D5wD85F99p4YI7i2hhnPwGrsWJhUOT7dSWOyT3YjsobSn1zNsAp4Ns8GFSyS2x-2CKKBi1wu3JkrbDqQVKa3vA9BRzASl-0ahGLJZPCD8fjLoLyQix2l_4',
    },
    timeAgo: '1d ago',
    content: "I'm so excited to start my KPSS journey! Any tips for a newbie?",
    likes: 23,
    comments: 5,
    shares: 1,
    isLiked: true,
  },
  {
    id: 2,
    user: {
      name: '<PERSON><PERSON>',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuChoPjgB0n6jrMXKMHd792GM4g_ujKfsS_17Roe8zkS4gjimdM67ffxt3JkvQ_88sApFIwbYuaWvo2SFa7TQho92KyOQf-5KpJHZAZaVh0FnSQa1KfvvnGlquZMqv0by4ZKJCBfo1TtXeLNQEpKTDl-4CH7-qpQySb5Jmzvek4nIxcs7TbHx3evF-FDCG7OepJuBLk26HZ-V8PUyFCY1kjG12-pb1cVxmWR2nq35FdFdNKux4Tj5LCKOPVnirJ-9H7xPIFNRm_qDlY',
    },
    timeAgo: '2d ago',
    content: 'Just finished my first mock exam. Scored 75/100. Feeling good!',
    likes: 45,
    comments: 12,
    shares: 3,
    isLiked: false,
  },
  {
    id: 3,
    user: {
      name: 'Ayse Yilmaz',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAwcbYh0xb4xYlOytFm6-PBzdHy7Txw2x3rGN5sTevce5diXscuXX_0wgHKGy7i3kMObLlGOcLNKlVce3CxNZcg68YDNO-bIwiSBJkuTkAWbYGRhAxU4PBuqgAp8jlhajrIBugGo6gvvACN982MROPhIgYw5591D9c-EQFrtdPJ0BVgTz4lLKCqDSqtf4aqKm7lHwJGWT4SlqwlwjMzxYMFloKHJ5T8laiF3Z3htetp0sWP9RCm-cnP1bQ_KusqU5Y3zrtg0i5oaUU',
    },
    timeAgo: '3d ago',
    content: 'Anyone else struggling with the history section? Send help!',
    likes: 32,
    comments: 8,
    shares: 2,
    isLiked: false,
  },
];

export const SocialHomeScreen: React.FC = () => {
  const handleLike = (postId: number) => {
    // Handle like functionality
    console.log('Like post:', postId);
  };

  const handleComment = (postId: number) => {
    // Handle comment functionality
    console.log('Comment on post:', postId);
  };

  const handleShare = (postId: number) => {
    // Handle share functionality
    console.log('Share post:', postId);
  };

  const handleMoreOptions = (postId: number) => {
    // Handle more options
    console.log('More options for post:', postId);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Timeline Content - Mobile design'dan birebir */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {mockTimelineData.map(post => (
            <View key={post.id} style={styles.postCard}>
              {/* Post Header - Mobile design'dan birebir */}
              <View style={styles.postHeader}>
                <Image
                  source={{uri: post.user.avatar}}
                  style={styles.userAvatar}
                />
                <View style={styles.userInfo}>
                  <View style={styles.userDetails}>
                    <Text style={styles.userName}>{post.user.name}</Text>
                    <Text style={styles.timeAgo}>{post.timeAgo}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.moreButton}
                    onPress={() => handleMoreOptions(post.id)}>
                    <Icon name="more-horiz" size={24} color="#6B7280" />
                  </TouchableOpacity>
                </View>
              </View>

              {/* Post Content - Mobile design'dan birebir */}
              <Text style={styles.postContent}>{post.content}</Text>

              {/* Post Actions - Mobile design'dan birebir */}
              <View style={styles.postActions}>
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleLike(post.id)}>
                  <Icon
                    name="favorite"
                    size={20}
                    color={post.isLiked ? '#EF4444' : '#9CA3AF'}
                  />
                  <Text style={styles.actionText}>{post.likes}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleComment(post.id)}>
                  <Icon name="chat-bubble-outline" size={20} color="#6B7280" />
                  <Text style={styles.actionText}>{post.comments}</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => handleShare(post.id)}>
                  <Icon name="send" size={20} color="#6B7280" />
                  <Text style={styles.actionText}>{post.shares}</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Bottom Navigation - Mobile design'dan birebir */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItemActive}>
          <Icon name="home" size={24} color="#1e13ec" />
          <Text style={styles.navLabelActive}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="leaderboard" size={24} color="#6B7280" />
          <Text style={styles.navLabel}>Leaderboard</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="quiz" size={24} color="#6B7280" />
          <Text style={styles.navLabel}>Test</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="description" size={24} color="#6B7280" />
          <Text style={styles.navLabel}>Mock Exams</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="person" size={24} color="#6B7280" />
          <Text style={styles.navLabel}>Profile</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },

  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    gap: 16,
  },
  postCard: {
    borderRadius: 12,
    backgroundColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  postHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
    padding: 16,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'Be Vietnam Pro',
  },
  timeAgo: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Be Vietnam Pro',
  },
  moreButton: {
    padding: 4,
  },
  postContent: {
    fontSize: 16,
    color: '#111827',
    paddingHorizontal: 16,
    marginTop: 8,
    lineHeight: 24,
    fontFamily: 'Be Vietnam Pro',
  },
  postActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingVertical: 4,
    marginTop: 16,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
    fontFamily: 'Be Vietnam Pro',
  },
  bottomNav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: '#f9fafb',
    paddingTop: 8,
    paddingBottom: 12,
  },
  navItem: {
    alignItems: 'center',
    gap: 4,
  },
  navItemActive: {
    alignItems: 'center',
    gap: 4,
  },
  navLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    fontFamily: 'Be Vietnam Pro',
  },
  navLabelActive: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1e13ec',
    fontFamily: 'Be Vietnam Pro',
  },
});
