import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ImageBackground,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {Content} from '../../services/api/types';
import {contentService} from '../../services/api/contentService';

interface Props {
  route?: {
    params?: {
      contentId?: string;
    };
  };
  navigation?: any;
}

export const ContentPlayerScreen: React.FC<Props> = ({route, navigation}) => {
  const contentId = route?.params?.contentId || 'default-content';
  const [content, setContent] = useState<Content | null>(null);
  const [progress, setProgress] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const loadContent = async () => {
    try {
      setIsLoading(true);
      const response = await contentService.getContentById(contentId);
      setContent(response);

      // TODO: Load user progress from API
      // const progressResponse = await contentService.getContentProgress(contentId);
      // setProgress(progressResponse.progress_percentage);
    } catch (error) {
      console.error('Error loading content:', error);

      // Fallback to mock data
      setContent({
        id: contentId,
        title: 'Türkiye Tarihi',
        description:
          'Osmanlı İmparatorluğu dönemini kapsamlı şekilde inceleyen bu içerik...',
        subject: 'Tarih',
        difficulty_level: 'intermediate',
        duration: 45,
        content_type: 'text',
        thumbnail_url:
          'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=400',
        view_count: 1500,
        like_count: 230,
        is_premium: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadContent();
  }, [contentId]); // eslint-disable-line react-hooks/exhaustive-deps

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="arrow-back" size={24} color="#111018" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {content?.title || 'İçerik Yükleniyor...'}
        </Text>
        <TouchableOpacity
          style={styles.headerButton}
          onPress={() => setIsBookmarked(!isBookmarked)}>
          <Icon
            name={isBookmarked ? 'bookmark' : 'bookmark-outline'}
            size={24}
            color="#111018"
          />
        </TouchableOpacity>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View style={[styles.progressFill, {width: `${progress}%`}]} />
        </View>
      </View>

      {/* Loading State */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>İçerik yükleniyor...</Text>
        </View>
      )}

      {/* Content */}
      {!isLoading && content && (
        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}>
          {/* Video/Image Player */}
          <View style={styles.playerContainer}>
            <ImageBackground
              source={{
                uri:
                  content.thumbnail_url ||
                  'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=400',
              }}
              style={styles.playerBackground}
              imageStyle={styles.playerBackgroundStyle}>
              <View style={styles.playerOverlay}>
                <TouchableOpacity style={styles.playButton}>
                  <Icon name="play" size={32} color={colors.white} />
                </TouchableOpacity>
              </View>
            </ImageBackground>
          </View>

          {/* Content Info */}
          <View style={styles.contentContainer}>
            <Text style={styles.chapterTitle}>{content.subject}</Text>
            <Text style={styles.paragraph}>{content.description}</Text>

            {/* Content Stats */}
            <View style={styles.statsContainer}>
              <View style={styles.statItem}>
                <Icon name="time-outline" size={16} color="#6B7280" />
                <Text style={styles.statText}>{content.duration} dk</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="eye-outline" size={16} color="#6B7280" />
                <Text style={styles.statText}>{content.view_count}</Text>
              </View>
              <View style={styles.statItem}>
                <Icon name="heart-outline" size={16} color="#6B7280" />
                <Text style={styles.statText}>{content.like_count}</Text>
              </View>
            </View>
          </View>
        </ScrollView>
      )}

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <View style={styles.progressInfo}>
          <Text style={styles.progressText}>{progress}% Tamamlandı</Text>
        </View>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.completeButton}
            onPress={() => setProgress(100)}>
            <Text style={styles.completeButtonText}>
              Tamamlandı Olarak İşaretle
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.noteButton}>
            <Text style={styles.noteButtonText}>Not Ekle</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111018',
    textAlign: 'center',
    flex: 1,
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: colors.white,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#EAEAF1',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#413BB0',
    borderRadius: 4,
  },
  scrollView: {
    flex: 1,
  },
  playerContainer: {
    aspectRatio: 16 / 9,
    backgroundColor: '#413BB0',
  },
  playerBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerBackgroundStyle: {
    resizeMode: 'cover',
  },
  playerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  contentContainer: {
    padding: 16,
    gap: 16,
  },
  chapterTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111018',
  },
  paragraph: {
    fontSize: 16,
    color: '#6B7280',
    lineHeight: 24,
  },
  bottomActions: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  progressInfo: {
    marginBottom: 16,
  },
  progressText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111018',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 16,
  },
  completeButton: {
    flex: 1,
    backgroundColor: '#413BB0',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#6366F1',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  completeButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.white,
  },
  noteButton: {
    flex: 1,
    backgroundColor: '#EAEAF1',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noteButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111018',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  statText: {
    marginLeft: 4,
    fontSize: 14,
    color: '#6B7280',
  },
});
