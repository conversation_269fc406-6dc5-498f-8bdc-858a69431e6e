import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  TextInput,
  ImageBackground,
  ActivityIndicator,
  Alert,
  RefreshControl,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {contentService} from '../../services/api/contentService';
import {Content} from '../../services/api/types';

// Categories for filtering
const categories = ['All', 'Math', 'Turkish', 'History', 'Geography'];

export const ContentHomeScreen: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchText, setSearchText] = useState('');
  const [recommendedContent, setRecommendedContent] = useState<Content[]>([]);
  const [popularContent, setPopularContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load content data

  const loadContent = useCallback(async () => {
    try {
      setError(null);
      setIsLoading(true);

      // Load popular content - if fails, set empty array
      try {
        const popularResponse = await contentService.getPopularContent(1, 10);
        setPopularContent(popularResponse.data || []);
      } catch (popularError) {
        console.warn('Popular content could not be loaded:', popularError);
        setPopularContent([]);
      }

      // Load recommended content - if fails, set empty array
      try {
        const recommendedResponse = await contentService.getRecommendedContent(
          1,
          5,
        );
        setRecommendedContent(recommendedResponse.data || []);
      } catch (recommendedError) {
        console.warn(
          'Recommended content could not be loaded:',
          recommendedError,
        );
        setRecommendedContent([]);
      }
    } catch (loadError) {
      console.error('Error loading content:', loadError);
      // Don't show error message, just set empty arrays
      setPopularContent([]);
      setRecommendedContent([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadContent();
  }, [loadContent]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadContent();
    setRefreshing(false);
  }, [loadContent]);

  const handleSearch = (text: string) => {
    setSearchText(text);
    // TODO: Implement search functionality
  };

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    // TODO: Filter content by category
  };

  const handleContentAdd = async (contentId: string) => {
    try {
      await contentService.addToLibrary(contentId);
      Alert.alert('Başarılı', 'İçerik kütüphanenize eklendi');
    } catch (addError) {
      console.error('Error adding to library:', addError);
      Alert.alert('Hata', 'İçerik eklenirken bir hata oluştu');
    }
  };

  const handleContentPress = (contentId: string) => {
    console.log('Open content:', contentId);
  };

  // Show loading state
  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer]}>
        <ActivityIndicator size="large" color="#6366F1" />
        <Text style={styles.loadingText}>İçerikler yükleniyor...</Text>
      </View>
    );
  }

  // Show error state
  if (error) {
    return (
      <View style={[styles.container, styles.errorContainer]}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={loadContent}>
          <Text style={styles.retryButtonText}>Tekrar Dene</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Search Bar - Mobile design'dan birebir */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon
            name="search"
            size={20}
            color="#64748B"
            style={styles.searchIcon}
          />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for content"
            placeholderTextColor="#64748B"
            value={searchText}
            onChangeText={handleSearch}
          />
        </View>
      </View>

      {/* Category Filter - Mobile design'dan birebir */}
      <View style={styles.categoryContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.categoryScroll}>
          {categories.map(category => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryButton,
                selectedCategory === category && styles.categoryButtonActive,
              ]}
              onPress={() => handleCategorySelect(category)}>
              <Text
                style={[
                  styles.categoryText,
                  selectedCategory === category && styles.categoryTextActive,
                ]}>
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Recommended Section - Mobile design'dan birebir */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recommended</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.contentScroll}>
            {recommendedContent.map(content => (
              <TouchableOpacity
                key={content.id}
                style={styles.contentCard}
                onPress={() => handleContentPress(content.id)}>
                <ImageBackground
                  source={{
                    uri:
                      content.thumbnail_url ||
                      'https://lh3.googleusercontent.com/aida-public/AB6AXuDIqlo1hz4tEAbtsqhS9s6G8894lSFg-WF2wz74AqVgl9OUhUY66ImI6Yv2ia5CA7EhddzWCb1uO_QMs-wr5QpL8Gc59eAGyvHP7mvKVbMNSKv_W-I3hkrq1iJ_Ai2B0QT29BoB3JbHmnNYptD6y86-XXrlMf6YFNzRffFw06tmS9WwxQvLvi4-AU6eyoLPoEeEYy0cy4dPdNqgPDTN75WTRfgu3xprVA6Lk-gfNAAj_Y1Qub1xaxkdtdsPASki98jJanhiSirxF4Y',
                  }}
                  style={styles.contentImage}
                />
                <View style={styles.contentInfo}>
                  <View style={styles.contentDetails}>
                    <Text style={styles.contentTitle}>{content.title}</Text>
                    <Text style={styles.contentSubtitle}>
                      {content.description || content.subject}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => handleContentAdd(content.id)}>
                    <Icon name="add" size={20} color="#64748B" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Popular Section - Mobile design'dan birebir */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popular</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.contentScroll}>
            {popularContent.map(content => (
              <TouchableOpacity
                key={content.id}
                style={styles.contentCard}
                onPress={() => handleContentPress(content.id)}>
                <ImageBackground
                  source={{
                    uri:
                      content.thumbnail_url ||
                      'https://lh3.googleusercontent.com/aida-public/AB6AXuBLpAphln1u18t-JiKjR-RzrEr8jML68tMi-e4vpq5i7YWqphQ3fqICt6ErKTTHJ-4UVShODw1vO_QMs-wr5QpL8Gc59eAGyvHP7mvKVbMNSKv_W-I3hkrq1iJ_Ai2B0QT29BoB3JbHmnNYptD6y86-XXrlMf6YFNzRffFw06tmS9WwxQvLvi4-AU6eyoLPoEeEYy0cy4dPdNqgPDTN75WTRfgu3xprVA6Lk-gfNAAj_Y1Qub1xaxkdtdsPASki98jJanhiSirxF4Y',
                  }}
                  style={styles.contentImage}
                />
                <View style={styles.contentInfo}>
                  <View style={styles.contentDetails}>
                    <Text style={styles.contentTitle}>{content.title}</Text>
                    <Text style={styles.contentSubtitle}>
                      {content.description || content.subject}
                    </Text>
                  </View>
                  <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => handleContentAdd(content.id)}>
                    <Icon name="add" size={20} color="#64748B" />
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748B',
    fontFamily: 'Lexend',
  },
  errorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorText: {
    fontSize: 16,
    color: '#EF4444',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'Lexend',
  },
  retryButton: {
    backgroundColor: '#6366F1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },

  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchInputContainer: {
    position: 'relative',
  },
  searchIcon: {
    position: 'absolute',
    left: 12,
    top: 12,
    zIndex: 1,
  },
  searchInput: {
    width: '100%',
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    backgroundColor: '#F8FAFC',
    paddingVertical: 12,
    paddingLeft: 44,
    paddingRight: 16,
    color: '#1E293B',
    fontSize: 16,
    fontFamily: 'Lexend',
  },
  categoryContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  categoryScroll: {
    flexDirection: 'row',
  },
  categoryButton: {
    height: 40,
    paddingHorizontal: 20,
    borderRadius: 20,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  categoryButtonActive: {
    backgroundColor: '#6366F1',
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#475569',
    fontFamily: 'Lexend',
  },
  categoryTextActive: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingVertical: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1E293B',
    paddingHorizontal: 16,
    paddingBottom: 12,
    fontFamily: 'Lexend',
  },
  contentScroll: {
    paddingLeft: 16,
  },
  contentCard: {
    width: 288,
    marginRight: 16,
  },
  contentImage: {
    width: '100%',
    height: 160,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 12,
  },
  contentInfo: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 8,
  },
  contentDetails: {
    flex: 1,
  },
  contentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    fontFamily: 'Lexend',
  },
  contentSubtitle: {
    fontSize: 14,
    color: '#64748B',
    marginTop: 2,
    fontFamily: 'Lexend',
  },
  addButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F1F5F9',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 4,
  },
});
