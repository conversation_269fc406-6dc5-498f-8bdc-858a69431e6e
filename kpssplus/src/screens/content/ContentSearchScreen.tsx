import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  FlatList,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Content} from '../../services/api/types';
import {contentService} from '../../services/api/contentService';
import {colors} from '../../theme';

interface Props {
  navigation?: any;
}

export const ContentSearchScreen: React.FC<Props> = ({navigation}) => {
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [recentSearches, setRecentSearches] = useState(['<PERSON><PERSON><PERSON>', 'Coğrafya']);

  const suggestedTags = [
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
  ];

  const performSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsLoading(true);
      const response = await contentService.searchContent(query.trim(), {
        page: 1,
        limit: 20,
      });
      setSearchResults(response.data);

      // Add to recent searches
      if (!recentSearches.includes(query.trim())) {
        setRecentSearches(prev => [query.trim(), ...prev.slice(0, 4)]);
      }
    } catch (error) {
      console.error('Error searching content:', error);
      setSearchResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  const removeRecentSearch = (index: number) => {
    const newSearches = recentSearches.filter((_, i) => i !== index);
    setRecentSearches(newSearches);
  };

  const handleSearch = () => {
    performSearch(searchText);
  };

  const handleTagPress = (tag: string) => {
    setSearchText(tag);
    performSearch(tag);
  };

  const handleRecentSearchPress = (search: string) => {
    setSearchText(search);
    performSearch(search);
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="arrow-back" size={24} color="#475569" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ara</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Icon
              name="search"
              size={20}
              color="#64748B"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="İçerik ara..."
              placeholderTextColor="#64748B"
              value={searchText}
              onChangeText={setSearchText}
              onSubmitEditing={handleSearch}
              returnKeyType="search"
            />
          </View>
        </View>

        {/* Search Results */}
        {searchText.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Arama Sonuçları ({searchResults.length})
            </Text>

            {isLoading && (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Aranıyor...</Text>
              </View>
            )}

            {!isLoading &&
              searchResults.length === 0 &&
              searchText.length > 0 && (
                <View style={styles.emptyContainer}>
                  <Icon name="search" size={48} color="#D1D5DB" />
                  <Text style={styles.emptyText}>Sonuç bulunamadı</Text>
                  <Text style={styles.emptySubtext}>
                    Farklı anahtar kelimeler deneyin
                  </Text>
                </View>
              )}

            {!isLoading && searchResults.length > 0 && (
              <FlatList
                data={searchResults}
                keyExtractor={item => item.id}
                renderItem={({item}) => (
                  <TouchableOpacity
                    style={styles.resultItem}
                    onPress={() =>
                      navigation?.navigate('ContentDetail', {
                        contentId: item.id,
                      })
                    }>
                    <Image
                      source={{
                        uri:
                          item.thumbnail_url ||
                          'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=400',
                      }}
                      style={styles.resultImage}
                    />
                    <View style={styles.resultContent}>
                      <Text style={styles.resultTitle}>{item.title}</Text>
                      <Text style={styles.resultSubject}>{item.subject}</Text>
                      <View style={styles.resultStats}>
                        <Text style={styles.resultStat}>
                          {item.duration} dk
                        </Text>
                        <Text style={styles.resultStat}>
                          {item.view_count} görüntülenme
                        </Text>
                      </View>
                    </View>
                  </TouchableOpacity>
                )}
                scrollEnabled={false}
              />
            )}
          </View>
        )}

        {/* Recent Searches */}
        {recentSearches.length > 0 && searchText.length === 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Son Aramalar</Text>
            <View style={styles.tagsContainer}>
              {recentSearches.map((search, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.recentSearchTag}
                  onPress={() => handleRecentSearchPress(search)}>
                  <Text style={styles.tagText}>{search}</Text>
                  <TouchableOpacity
                    onPress={() => removeRecentSearch(index)}
                    style={styles.removeButton}>
                    <Icon name="close" size={16} color="#64748B" />
                  </TouchableOpacity>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        {/* Suggested Tags */}
        {searchText.length === 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Önerilen Etiketler</Text>
            <View style={styles.tagsContainer}>
              {suggestedTags.map((tag, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.suggestedTag}
                  onPress={() => handleTagPress(tag)}>
                  <Text style={styles.tagText}>{tag}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F9FB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
  },
  backButton: {
    width: 32,
    height: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0F172A',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  searchBar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(226, 232, 240, 0.6)',
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#0F172A',
  },
  section: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingTop: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748B',
    marginBottom: 12,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  recentSearchTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(226, 232, 240, 0.6)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 8,
  },
  suggestedTag: {
    backgroundColor: 'rgba(226, 232, 240, 0.6)',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  tagText: {
    fontSize: 14,
    color: '#374151',
  },
  removeButton: {
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 16,
    color: '#6B7280',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    marginTop: 16,
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
  },
  emptySubtext: {
    marginTop: 4,
    fontSize: 14,
    color: '#6B7280',
  },
  resultItem: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  resultImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  resultContent: {
    flex: 1,
    marginLeft: 12,
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  resultSubject: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  resultStats: {
    flexDirection: 'row',
  },
  resultStat: {
    fontSize: 12,
    color: '#9CA3AF',
    marginRight: 16,
  },
});
