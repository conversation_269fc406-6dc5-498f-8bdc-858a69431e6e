import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  TextInput,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';

import {Card} from '../../components/ui/Card';
import {Button} from '../../components/ui/Button';
import {Icon} from '../../components/ui';
import {colors} from '../../theme/colors';
import {typography} from '../../theme/typography';
import {spacing} from '../../theme/spacing';
import {useAuth, useGuestMode} from '../../hooks';
import {StyleSheet} from 'react-native';
import {contentService} from '../../services/api/contentService';
import {Content} from '../../services/api/types';

type Props = {};

interface StudyTopic {
  id: string;
  title: string;
  subject: string;
  description: string;
  questionCount: number;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number; // in minutes
  completionRate: number; // percentage
  isCompleted: boolean;
  tags: string[];
}

export const StudyTopicsScreen: React.FC<Props> = () => {
  const {isAuthenticated, isGuest} = useAuth();
  const {checkContentLimit} = useGuestMode();

  const [topics, setTopics] = useState<StudyTopic[]>([]);
  const [filteredTopics, setFilteredTopics] = useState<StudyTopic[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedDifficulty] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [, setIsLoading] = useState(true);

  const subjects = [
    'all',
    'Türkçe',
    'Tarih',
    'Coğrafya',
    'Vatandaşlık',
    'Matematik',
  ];
  // const difficulties = ['all', 'easy', 'medium', 'hard'];

  const loadTopics = useCallback(async () => {
    setIsLoading(true);
    try {
      if (isGuest) {
        // Load demo topics for guest users
        const demoTopics = await getMockTopics(true);
        setTopics(demoTopics);
      } else {
        // Load real topics from API for authenticated users
        try {
          const response = await contentService.getContentList();

          // Convert Content to StudyTopic format
          const apiTopics: StudyTopic[] = response.data.map(
            (content: Content) => ({
              id: content.id,
              title: content.title,
              subject: content.subject || 'Genel',
              description: content.description || '',
              questionCount: Math.floor(Math.random() * 30) + 10, // Mock question count
              difficulty: (['easy', 'medium', 'hard'] as const)[
                Math.floor(Math.random() * 3)
              ],
              estimatedTime: Math.floor(Math.random() * 40) + 20, // Mock estimated time
              completionRate: Math.floor(Math.random() * 100), // Mock completion rate
              isCompleted: Math.random() > 0.7, // Mock completion status
              tags: content.tags || [],
            }),
          );

          setTopics(apiTopics);
        } catch (apiError) {
          console.error('API Error, falling back to mock data:', apiError);
          // Fallback to mock data if API fails
          const allTopics = await getMockTopics(false);
          setTopics(allTopics);
        }
      }
    } catch (error) {
      console.error('Error loading topics:', error);
      // Final fallback to mock data
      const fallbackTopics = await getMockTopics(isGuest);
      setTopics(fallbackTopics);
    } finally {
      setIsLoading(false);
    }
  }, [isGuest, setIsLoading]);

  const getMockTopics = async (isGuestMode: boolean): Promise<StudyTopic[]> => {
    const allTopics: StudyTopic[] = [
      {
        id: '1',
        title: 'Cümle Bilgisi',
        subject: 'Türkçe',
        description:
          'Cümlenin öğeleri, cümle türleri ve yapısı hakkında temel bilgiler',
        questionCount: 25,
        difficulty: 'easy',
        estimatedTime: 30,
        completionRate: 85,
        isCompleted: false,
        tags: ['temel', 'dilbilgisi'],
      },
      {
        id: '2',
        title: 'Osmanlı Devleti Kuruluş Dönemi',
        subject: 'Tarih',
        description: 'Osmanlı Devletinin kuruluşu ve ilk dönem gelişmeleri',
        questionCount: 30,
        difficulty: 'medium',
        estimatedTime: 45,
        completionRate: 60,
        isCompleted: false,
        tags: ['osmanlı', 'kuruluş'],
      },
      {
        id: '3',
        title: "Türkiye'nin Fiziki Coğrafyası",
        subject: 'Coğrafya',
        description: "Türkiye'nin yer şekilleri, iklimi ve doğal kaynakları",
        questionCount: 20,
        difficulty: 'medium',
        estimatedTime: 35,
        completionRate: 70,
        isCompleted: true,
        tags: ['fiziki', 'türkiye'],
      },
      {
        id: '4',
        title: 'Anayasa ve Temel Haklar',
        subject: 'Vatandaşlık',
        description: 'Türkiye Cumhuriyeti Anayasası ve vatandaş hakları',
        questionCount: 18,
        difficulty: 'easy',
        estimatedTime: 25,
        completionRate: 90,
        isCompleted: false,
        tags: ['anayasa', 'haklar'],
      },
      {
        id: '5',
        title: 'Sayılar ve İşlemler',
        subject: 'Matematik',
        description: 'Temel matematik işlemleri ve sayı sistemleri',
        questionCount: 35,
        difficulty: 'easy',
        estimatedTime: 40,
        completionRate: 45,
        isCompleted: false,
        tags: ['temel', 'işlemler'],
      },
      // Additional topics for authenticated users
      {
        id: '6',
        title: 'Paragraf ve Anlam',
        subject: 'Türkçe',
        description: 'Paragraf yapısı, ana fikir ve yardımcı fikirler',
        questionCount: 22,
        difficulty: 'medium',
        estimatedTime: 35,
        completionRate: 55,
        isCompleted: false,
        tags: ['paragraf', 'anlam'],
      },
      {
        id: '7',
        title: 'Cumhuriyet Dönemi',
        subject: 'Tarih',
        description: "Türkiye Cumhuriyeti'nin kuruluşu ve Atatürk dönemi",
        questionCount: 28,
        difficulty: 'hard',
        estimatedTime: 50,
        completionRate: 40,
        isCompleted: false,
        tags: ['cumhuriyet', 'atatürk'],
      },
      {
        id: '8',
        title: 'Dünya Coğrafyası',
        subject: 'Coğrafya',
        description: 'Dünya kıtaları, okyanuslar ve önemli coğrafi oluşumlar',
        questionCount: 26,
        difficulty: 'hard',
        estimatedTime: 45,
        completionRate: 30,
        isCompleted: false,
        tags: ['dünya', 'kıtalar'],
      },
    ];

    // Return limited topics for guest users
    return isGuestMode ? allTopics.slice(0, 5) : allTopics;
  };

  const filterTopics = useCallback(() => {
    let filtered = topics;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(
        topic =>
          topic.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          topic.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          topic.tags.some(tag =>
            tag.toLowerCase().includes(searchQuery.toLowerCase()),
          ),
      );
    }

    // Filter by subject
    if (selectedSubject !== 'all') {
      filtered = filtered.filter(topic => topic.subject === selectedSubject);
    }

    // Filter by difficulty
    if (selectedDifficulty !== 'all') {
      filtered = filtered.filter(
        topic => topic.difficulty === selectedDifficulty,
      );
    }

    setFilteredTopics(filtered);
  }, [topics, searchQuery, selectedSubject, selectedDifficulty]);

  useEffect(() => {
    loadTopics();
  }, [isAuthenticated, loadTopics]);

  useEffect(() => {
    filterTopics();
  }, [filterTopics]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTopics();
    setRefreshing(false);
  };

  const handleTopicPress = (topic: StudyTopic) => {
    if (isGuest && checkContentLimit()) {
      console.log('Navigation disabled');
      return;
    }

    console.log('Navigate to TopicDetail', topic.id, topic.title);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return colors.success;
      case 'medium':
        return colors.warning;
      case 'hard':
        return colors.error;
      default:
        return colors.text.secondary;
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'Kolay';
      case 'medium':
        return 'Orta';
      case 'hard':
        return 'Zor';
      default:
        return difficulty;
    }
  };

  const renderTopicCard = (topic: StudyTopic) => (
    <Card
      key={topic.id}
      style={styles.topicCard}
      variant="elevated"
      onPress={() => handleTopicPress(topic)}>
      <View style={styles.topicHeader}>
        <View style={styles.topicInfo}>
          <Text style={styles.topicTitle}>{topic.title}</Text>
          <Text style={styles.topicSubject}>{topic.subject}</Text>
        </View>
        <View style={styles.topicBadges}>
          <View
            style={[
              styles.difficultyBadge,
              {backgroundColor: getDifficultyColor(topic.difficulty) + '20'},
            ]}>
            <Text
              style={[
                styles.difficultyText,
                {color: getDifficultyColor(topic.difficulty)},
              ]}>
              {getDifficultyText(topic.difficulty)}
            </Text>
          </View>
          {topic.isCompleted && (
            <View style={styles.completedBadge}>
              <Icon name="checkmark-circle" size={16} color={colors.success} />
            </View>
          )}
        </View>
      </View>

      <Text style={styles.topicDescription}>{topic.description}</Text>

      <View style={styles.topicStats}>
        <View style={styles.statItem}>
          <Icon
            name="help-circle-outline"
            size={16}
            color={colors.text.secondary}
          />
          <Text style={styles.statText}>{topic.questionCount} soru</Text>
        </View>
        <View style={styles.statItem}>
          <Icon name="time-outline" size={16} color={colors.text.secondary} />
          <Text style={styles.statText}>{topic.estimatedTime} dk</Text>
        </View>
        <View style={styles.statItem}>
          <Icon
            name="trending-up-outline"
            size={16}
            color={colors.text.secondary}
          />
          <Text style={styles.statText}>%{topic.completionRate} tamamlama</Text>
        </View>
      </View>

      <View style={styles.topicTags}>
        {topic.tags.map(tag => (
          <View key={tag} style={styles.tag}>
            <Text style={styles.tagText}>{tag}</Text>
          </View>
        ))}
      </View>
    </Card>
  );

  if (isGuest) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Konu Çalış</Text>
          <Text style={styles.headerSubtitle}>
            Konuları detaylı şekilde çalışın ve öğrenin
          </Text>
        </View>

        <ScrollView
          style={styles.scrollView}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }>
          {/* Guest Banner */}
          <Card style={styles.guestBanner} variant="outlined">
            <Icon name="book-outline" size={48} color={colors.primary} />
            <Text style={styles.guestTitle}>Demo Konular</Text>
            <Text style={styles.guestDescription}>
              Sınırlı sayıda konu görüntüleyebilirsiniz. Tüm konulara erişmek
              için giriş yapın.
            </Text>
            <Button
              title="Giriş Yap"
              onPress={() => console.log('Navigation disabled')}
              style={styles.loginButton}
            />
          </Card>

          {/* Demo Topics */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Demo Konular ({topics.length})
            </Text>
            {topics.map(renderTopicCard)}
          </View>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Konu Çalış</Text>
        <Text style={styles.headerSubtitle}>
          Konuları detaylı şekilde çalışın ve öğrenin
        </Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }>
        {/* Search and Filters */}
        <View style={styles.filtersContainer}>
          <View style={styles.searchContainer}>
            <Icon
              name="search-outline"
              size={20}
              color={colors.text.secondary}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Konu ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              placeholderTextColor={colors.text.secondary}
            />
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.filterScroll}>
            {/* Subject Filter */}
            {subjects.map(subject => (
              <TouchableOpacity
                key={subject}
                style={[
                  styles.filterChip,
                  selectedSubject === subject && styles.activeFilterChip,
                ]}
                onPress={() => setSelectedSubject(subject)}>
                <Text
                  style={[
                    styles.filterChipText,
                    selectedSubject === subject && styles.activeFilterChipText,
                  ]}>
                  {subject === 'all' ? 'Tümü' : subject}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Topics List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Konular ({filteredTopics.length})
          </Text>
          {filteredTopics.length === 0 ? (
            <Card style={styles.emptyCard} variant="outlined">
              <Icon name="search-outline" size={48} color={colors.gray[400]} />
              <Text style={styles.emptyTitle}>Konu bulunamadı</Text>
              <Text style={styles.emptyDescription}>
                Arama kriterlerinizi değiştirerek tekrar deneyin
              </Text>
            </Card>
          ) : (
            filteredTopics.map(renderTopicCard)
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: spacing[4],
    paddingBottom: spacing[2],
  },
  headerTitle: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  headerSubtitle: {
    ...typography.body1,
    color: colors.text.secondary,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  guestBanner: {
    alignItems: 'center',
    padding: spacing[6],
    marginBottom: spacing[4],
  },
  guestTitle: {
    ...typography.h5,
    marginTop: spacing[3],
    marginBottom: spacing[2],
  },
  guestDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[4],
  },
  loginButton: {
    minWidth: 120,
  },
  filtersContainer: {
    marginBottom: spacing[4],
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    marginBottom: spacing[3],
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  searchInput: {
    flex: 1,
    marginLeft: spacing[2],
    ...typography.body1,
    color: colors.text.primary,
  },
  filterScroll: {
    flexGrow: 0,
  },
  filterChip: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 20,
    marginRight: spacing[2],
  },
  activeFilterChip: {
    backgroundColor: colors.primary,
  },
  filterChipText: {
    ...typography.caption,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  activeFilterChipText: {
    color: colors.white,
  },
  section: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  topicCard: {
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  topicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  topicInfo: {
    flex: 1,
    marginRight: spacing[3],
  },
  topicTitle: {
    ...typography.h6,
    marginBottom: spacing[1],
  },
  topicSubject: {
    ...typography.body2,
    color: colors.primary,
    fontWeight: '600',
  },
  topicBadges: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
  difficultyBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },
  difficultyText: {
    ...typography.caption,
    fontWeight: '600',
  },
  completedBadge: {
    padding: spacing[1],
  },
  topicDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    marginBottom: spacing[3],
    lineHeight: 20,
  },
  topicStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[3],
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  statText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  topicTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[1],
  },
  tag: {
    backgroundColor: colors.primaryMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[0.5],
    borderRadius: 8,
  },
  tagText: {
    ...typography.caption,
    color: colors.primary,
    fontSize: 10,
  },
  emptyCard: {
    alignItems: 'center',
    padding: spacing[6],
  },
  emptyTitle: {
    ...typography.h6,
    marginTop: spacing[3],
    marginBottom: spacing[1],
  },
  emptyDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
  },
});
