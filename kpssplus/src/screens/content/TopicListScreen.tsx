import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {ContentStackParamList} from '../../navigation/types';
import {colors, typography, spacing} from '../../theme';
// import {Button} from '../../components/ui';
import {useAuth, useTokens} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';
import {contentService} from '../../services/api/contentService';
import {Content} from '../../services/api/types';

type Props = StackScreenProps<ContentStackParamList, 'TopicList'>;

interface TopicItem {
  id: string;
  title: string;
  description: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  duration: number;
  contentCount: number;
  tokenCost: number;
}

export const TopicListScreen: React.FC<Props> = ({navigation, route}) => {
  const {subjectId, subjectName} = route.params || {};
  const {isGuest} = useAuth();
  const {balance: tokenBalance, spendTokens, checkTokens} = useTokens();

  const [topics, setTopics] = useState<TopicItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTopics();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subjectId]);

  const loadTopics = async () => {
    try {
      setError(null);

      if (!isGuest) {
        // Load real topics from API
        const response = await contentService.getContentList({
          subject: subjectName || 'Türkçe',
          content_type: 'text',
        });

        // Convert Content to TopicItem
        const topicItems: TopicItem[] = response.data.map(
          (content: Content) => ({
            id: content.id,
            title: content.title,
            description: content.description || 'Açıklama bulunmuyor',
            subject: content.subject,
            difficulty: content.difficulty_level as 'easy' | 'medium' | 'hard',
            duration: content.duration || 30,
            contentCount: Math.floor(Math.random() * 20) + 5, // Mock content count
            tokenCost: content.is_premium ? 2 : 1,
          }),
        );

        setTopics(topicItems);
      } else {
        // Fallback to mock data for guests
        loadMockTopics();
      }
    } catch (err) {
      console.error('Error loading topics:', err);
      setError('Konular yüklenirken bir hata oluştu');
      // Fallback to mock data
      loadMockTopics();
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const loadMockTopics = () => {
    const mockTopics: TopicItem[] = [
      {
        id: '1',
        title: 'Cümle Bilgisi',
        description:
          'Cümlenin öğeleri, cümle türleri ve yapısı hakkında detaylı bilgiler.',
        subject: 'Türkçe',
        difficulty: 'easy',
        duration: 45,
        contentCount: 12,
        tokenCost: 1,
      },
      {
        id: '2',
        title: 'Kelime Bilgisi',
        description:
          'Kelimelerin yapısı, türleri ve anlamları hakkında kapsamlı bilgiler.',
        subject: 'Türkçe',
        difficulty: 'medium',
        duration: 60,
        contentCount: 18,
        tokenCost: 1,
      },
      {
        id: '3',
        title: 'Paragraf Bilgisi',
        description:
          'Paragraf yapısı, ana fikir ve yardımcı fikirler hakkında bilgiler.',
        subject: 'Türkçe',
        difficulty: 'hard',
        duration: 75,
        contentCount: 15,
        tokenCost: 2,
      },
      {
        id: '4',
        title: 'Yazım Kuralları',
        description: 'Türkçe yazım kuralları ve noktalama işaretleri.',
        subject: 'Türkçe',
        difficulty: 'easy',
        duration: 30,
        contentCount: 10,
        tokenCost: 1,
      },
    ];
    setTopics(mockTopics);
  };

  const onRefresh = () => {
    setIsRefreshing(true);
    loadTopics();
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return colors.success;
      case 'medium':
        return colors.warning;
      case 'hard':
        return colors.error;
      default:
        return colors.text.secondary;
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'Kolay';
      case 'medium':
        return 'Orta';
      case 'hard':
        return 'Zor';
      default:
        return 'Bilinmiyor';
    }
  };

  const handleTopicPress = async (topic: TopicItem) => {
    if (isGuest) {
      Alert.alert(
        'Giriş Gerekli',
        'Konulara erişmek için giriş yapmanız gerekiyor.',
        [
          {text: 'İptal', style: 'cancel'},
          {
            text: 'Giriş Yap',
            onPress: () => {
              // navigation.navigate('Auth', {screen: 'Login'});
              console.log('Navigate to login');
            },
          },
        ],
      );
      return;
    }

    // Check if user has enough tokens for premium content
    if (topic.tokenCost > 1 && !checkTokens(topic.tokenCost)) {
      Alert.alert(
        'Yetersiz Token',
        `Bu premium konu için ${topic.tokenCost} token gerekiyor. Mevcut bakiyeniz: ${tokenBalance}`,
        [{text: 'Tamam'}],
      );
      return;
    }

    // Navigate to topic detail
    try {
      if (topic.tokenCost > 1) {
        await spendTokens(topic.tokenCost, 'Premium konu erişimi');
      }
      navigation.navigate('TopicDetail', {topicId: topic.id});
    } catch (err) {
      Alert.alert('Hata', 'Konuya erişim sağlanamadı.');
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="chevron-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Konular</Text>
          <View style={styles.headerRight} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Konular yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="chevron-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{subjectName || 'Konular'}</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }>
        {error && (
          <View style={styles.errorContainer}>
            <Icon name="alert-circle-outline" size={48} color={colors.error} />
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {/* Topics */}
        <View style={styles.content}>
          {topics.map(topic => (
            <TouchableOpacity
              key={topic.id}
              style={styles.topicCard}
              onPress={() => handleTopicPress(topic)}>
              <View style={styles.topicHeader}>
                <View style={styles.topicTitleContainer}>
                  <Text style={styles.topicTitle}>{topic.title}</Text>
                  <Text style={styles.topicDescription} numberOfLines={2}>
                    {topic.description}
                  </Text>
                </View>
                {topic.tokenCost > 1 && (
                  <View style={styles.premiumBadge}>
                    <Icon name="diamond" size={14} color={colors.warning} />
                    <Text style={styles.premiumText}>Premium</Text>
                  </View>
                )}
              </View>

              <View style={styles.topicDetails}>
                <View style={styles.detailItem}>
                  <Icon
                    name="time-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.detailText}>{topic.duration} dk</Text>
                </View>
                <View style={styles.detailItem}>
                  <Icon
                    name="document-text-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.detailText}>
                    {topic.contentCount} içerik
                  </Text>
                </View>
                <View style={styles.detailItem}>
                  <View
                    style={[
                      styles.difficultyDot,
                      {backgroundColor: getDifficultyColor(topic.difficulty)},
                    ]}
                  />
                  <Text style={styles.detailText}>
                    {getDifficultyText(topic.difficulty)}
                  </Text>
                </View>
              </View>

              <View style={styles.topicFooter}>
                <View style={styles.tokenCost}>
                  <Icon name="diamond" size={16} color={colors.primary} />
                  <Text style={styles.tokenText}>{topic.tokenCost} Token</Text>
                </View>
                <Icon
                  name="chevron-forward"
                  size={20}
                  color={colors.text.secondary}
                />
              </View>
            </TouchableOpacity>
          ))}

          {topics.length === 0 && !isLoading && (
            <View style={styles.emptyContainer}>
              <Icon
                name="book-outline"
                size={64}
                color={colors.text.secondary}
              />
              <Text style={styles.emptyTitle}>Konu Bulunamadı</Text>
              <Text style={styles.emptyDescription}>
                Bu kategoride henüz konu bulunmuyor.
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h4,
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: spacing[2],
  },
  headerRight: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[4],
  },
  loadingText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginTop: spacing[3],
  },
  errorContainer: {
    alignItems: 'center',
    padding: spacing[4],
    margin: spacing[4],
    backgroundColor: colors.surface,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.error,
  },
  errorText: {
    ...typography.body1,
    color: colors.error,
    marginTop: spacing[2],
    textAlign: 'center',
  },
  content: {
    padding: spacing[4],
  },
  topicCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: spacing[4],
    marginBottom: spacing[3],
    borderWidth: 1,
    borderColor: colors.border,
  },
  topicHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  topicTitleContainer: {
    flex: 1,
    marginRight: spacing[2],
  },
  topicTitle: {
    ...typography.h6,
    color: colors.text.primary,
    marginBottom: spacing[1],
  },
  topicDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    lineHeight: 20,
  },
  premiumBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.warning + '20',
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
    gap: spacing[1],
  },
  premiumText: {
    ...typography.caption,
    color: colors.warning,
    fontWeight: '600',
  },
  topicDetails: {
    flexDirection: 'row',
    gap: spacing[4],
    marginBottom: spacing[3],
  },
  topicFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  tokenCost: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  tokenText: {
    ...typography.caption,
    color: colors.text.secondary,
    fontWeight: '600',
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  detailText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  difficultyDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  emptyContainer: {
    alignItems: 'center',
    padding: spacing[8],
  },
  emptyTitle: {
    ...typography.h5,
    marginTop: spacing[3],
    marginBottom: spacing[2],
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
