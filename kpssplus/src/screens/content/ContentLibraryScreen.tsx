import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON>
const mockLibraryData = {
  inProgress: [
    {
      id: 1,
      title: 'General Knowledge Test',
      progress: '10/20 Questions',
      progressPercent: 50,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuC2Nx2uspDLX5AUv-3rYptb55d63hXegNOfzEebQNsTfn0ILnV27U4xY5Ogl8lKLR4wl2TUvhHg8eqwoLEaN1qrqlJVgqh2evQ6_llR3Xw1IgscMp1_CAfPIo1mtm2-Tj4P3qSw3pZCEGJ-AhAzT01b0my7Dydx21Ccvz-FgCfkWMVNJpEEpjfw3p_RyflnbE3umQ1EXYLjGFWoHvVhbM45pidDTOTbSKwclLy6-oaMfjGyxKwgC5juUFYegzzaK3V0P7WuUHOkDnY',
    },
    {
      id: 2,
      title: 'History Quiz',
      progress: '5/15 Questions',
      progressPercent: 33,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAiTBfYJs6DfBVMYXSYr9anQXn5sI5ivA22P1jPUC1nLu6_NYPzXADMcC99Csse0Of71WWHdHZhyc8emG9v3Cp-5KsY90LTMNpdixzkwN1YjhmSKpl4BNIL6bsmRgIdZ-QNfdngys7g63lrEQRYSwmrYr7zm77ZUCsDtprUZex-h6mquvHddR633Qa6Hazs5UDj6pIRsU6XnVmUqYf-rRJMKmerGbht58KD5ZXj9dV0fyoOMGz5qc59Z9f_y-WqvXQRSAlnqG6LARQ',
    },
    {
      id: 3,
      title: 'Geography Challenge',
      progress: '2/10 Questions',
      progressPercent: 20,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuDnFP1FAEDPXAOQnqILbotiULIrjNbWGUjWxkonuGPVavi8tOUgIx9qh9QO0yio1bA3YHBOFG5Eqq1TKqZZCPROKJexpSuQpYFc5yb-VNwxnsqIGA_cDfwLyUDWMTD8QKa82WFfmFgTSCbwzTkV-b0Hm-3-N-1Rn0XqxJyYs5yRW_WoOcQzxsqHD98y-qGcrNzfOBDFY3zQHuss3wax-2fyiZfXddqPN-ByPaId8eT7RBLUe6FqgOJRJflv-HzU_OajQhwdJycLLFE',
    },
  ],
  completed: [
    {
      id: 4,
      title: 'Math Fundamentals',
      progress: '20/20 Questions',
      progressPercent: 100,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuC2Nx2uspDLX5AUv-3rYptb55d63hXegNOfzEebQNsTfn0ILnV27U4xY5Ogl8lKLR4wl2TUvhHg8eqwoLEaN1qrqlJVgqh2evQ6_llR3Xw1IgscMp1_CAfPIo1mtm2-Tj4P3qSw3pZCEGJ-AhAzT01b0my7Dydx21Ccvz-FgCfkWMVNJpEEpjfw3p_RyflnbE3umQ1EXYLjGFWoHvVhbM45pidDTOTbSKwclLy6-oaMfjGyxKwgC5juUFYegzzaK3V0P7WuUHOkDnY',
    },
    {
      id: 5,
      title: 'Turkish Literature',
      progress: '15/15 Questions',
      progressPercent: 100,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAiTBfYJs6DfBVMYXSYr9anQXn5sI5ivA22P1jPUC1nLu6_NYPzXADMcC99Csse0Of71WWHdHZhyc8emG9v3Cp-5KsY90LTMNpdixzkwN1YjhmSKpl4BNIL6bsmRgIdZ-QNfdngys7g63lrEQRYSwmrYr7zm77ZUCsDtprUZex-h6mquvHddR633Qa6Hazs5UDj6pIRsU6XnVmUqYf-rRJMKmerGbht58KD5ZXj9dV0fyoOMGz5qc59Z9f_y-WqvXQRSAlnqG6LARQ',
    },
  ],
};

export const ContentLibraryScreen: React.FC = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'inProgress' | 'completed'>(
    'inProgress',
  );

  const handleBack = () => {
    navigation.goBack();
  };

  const handleTabChange = (tab: 'inProgress' | 'completed') => {
    setActiveTab(tab);
  };

  const handleContentPress = (contentId: number) => {
    console.log('Open content:', contentId);
  };

  const currentData =
    activeTab === 'inProgress'
      ? mockLibraryData.inProgress
      : mockLibraryData.completed;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Icon name="arrow-back-ios" size={20} color="#111018" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Content Library</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Tab Navigation - Mobile design'dan birebir */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'inProgress' && styles.tabActive]}
            onPress={() => handleTabChange('inProgress')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'inProgress' && styles.tabTextActive,
              ]}>
              In Progress
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'completed' && styles.tabActive]}
            onPress={() => handleTabChange('completed')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'completed' && styles.tabTextActive,
              ]}>
              Completed
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Content List - Mobile design'dan birebir */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {currentData.map(content => (
            <TouchableOpacity
              key={content.id}
              style={styles.contentCard}
              onPress={() => handleContentPress(content.id)}>
              <View style={styles.contentInfo}>
                <Image
                  source={{uri: content.image}}
                  style={styles.contentImage}
                />
                <View style={styles.contentDetails}>
                  <Text style={styles.contentTitle}>{content.title}</Text>
                  <Text style={styles.contentProgress}>{content.progress}</Text>
                </View>
              </View>
              <View style={styles.progressContainer}>
                <View style={styles.progressBar}>
                  <View
                    style={[
                      styles.progressFill,
                      {width: `${content.progressPercent}%`},
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {content.progressPercent}%
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9F9FB',
  },
  header: {
    backgroundColor: '#F9F9FB',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111018',
    textAlign: 'center',
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 32,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    paddingHorizontal: 4,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabActive: {
    borderBottomColor: '#413BB0',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#5E5C8A',
    fontFamily: 'Lexend',
  },
  tabTextActive: {
    color: '#413BB0',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    gap: 16,
  },
  contentCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  contentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  contentImage: {
    width: 64,
    height: 64,
    borderRadius: 8,
  },
  contentDetails: {
    flex: 1,
  },
  contentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111018',
    fontFamily: 'Lexend',
  },
  contentProgress: {
    fontSize: 14,
    color: '#5E5C8A',
    marginTop: 2,
    fontFamily: 'Lexend',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    width: 64,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E7EB',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
    backgroundColor: '#413BB0',
  },
  progressText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#111018',
    fontFamily: 'Lexend',
  },
});
