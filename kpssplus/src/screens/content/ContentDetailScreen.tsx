import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  ImageBackground,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON><PERSON>
const mockContentDetail = {
  id: 1,
  title: 'Mastering the Art of Effective Study',
  duration: '15 min',
  difficulty: 'Medium',
  category: 'History',
  tags: ['Study Tips', 'Exam Prep', 'History'],
  description:
    'Unlock the secrets to effective studying with our comprehensive guide. Learn proven techniques to maximize your study sessions, retain information, and achieve academic success. This guide covers everything from time management to active learning strategies.',
  image:
    'https://lh3.googleusercontent.com/aida-public/AB6AXuDVP0d7BpdcEEapqITuL-hKVexLKzqPwiTh-JA-04U4YtGLKAAumswCl51eRGDI5pCmrSE07LjcvU_2CGCH8ExW17LPjH0rll2DqICca87iClpnfji4io01L1W-k_zhrdioAjIx7Bl8GpgNiksh9HdGSkrqC-KI3DD64AFx_CrXUiaMX_X5XC6K5UqueItMm6I2H7Szla41Scrr5sxI3QMA8nWWa3In5AKpt1cCcbzn4zSgEr0uKFQnmgi0cby6IBTexKJmUH517_U',
  stats: {
    views: '1.2K',
    likes: '350',
  },
  similarContent: [
    {
      id: 2,
      title: 'Ace Your Exams with Proven Strategies',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuBQeXrKLZW7u92kLHg_4_TCrH7gYml4bXcu7EZ9kEmH33c-tLtuH-XxMCuu0FwjYQcvxHxUKNqvVwbT7CbQfemSEHoN5CIowqQ8eeiSt9vp_ClfR6tjDITggslYIz0Pra6OViFdYZDugjBuhD6BxMJw5ggc2i6OMpFQA80zEiOgZYxLqNI0OBqpYINcz3UNYzjZiXSsSOhptJSp-vhfBUbjFBNs8KG5WIQtnlTpvEpzls5qfMxhLQmwzsxbx1uGziIkrcQBjKoyNcE',
    },
    {
      id: 3,
      title: 'The Ultimate Guide to Time Management',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuBds-TOOQUXRSFbNEMG6-cz5SNhOaSWgYsjGlegHyZ56BQLzHE7A6hAkOv7F5LX_0NqAFq5LCrXRyPY9e9ixVqLswyRnlSms1E254Wk7WgHSXMp8C-puN9L2dbdGo5nQQ926JcCXYxtnp7uj-NN_oXOHGOxau3s9ANNKgG8umGcS6qzQIFtWhijMIdrPKyij-RlfJsRAqobindckcDQlZt8CiBcBUqhNou5NSYvr9sfaVkJ7ToDwTyDnB-1j2Mq_NtDbonTCgg0sMs',
    },
    {
      id: 4,
      title: 'Effective Note-Taking for Better Retention',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuBBu3YVyMh8bMlqB8haOp_lrVyweTc5jxWcaAzDfqrqwZQPoNMcBpKJ2DynxH8QmvTpZqp5ypae1xY99D2Lojy3bNqnQ3rZzO1XpNgRwfXqERaYrb3HWRr2uPeKjaPyor67c4WqkH8jFf8mosNX31O9VwDuoXM4TFtHuOh9Gts3tkhQtTHngLu3wAXkz2aYWA37bliOGMS43qB0HYOD6ox6EA6oO4NTdHpSINZEww26WvvfuDF14Y6jP0y0vFgYVvFT_eTGMNi5WqI',
    },
  ],
};

export const ContentDetailScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  const handleBookmark = () => {
    console.log('Bookmark content');
  };

  const handleShare = () => {
    console.log('Share content');
  };

  const handleStartReading = () => {
    console.log('Start reading');
  };

  const handleSimilarContentPress = (contentId: number) => {
    console.log('Open similar content:', contentId);
  };

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Content Detail</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Hero Image - Mobile design'dan birebir */}
        <View style={styles.heroContainer}>
          <ImageBackground
            source={{uri: mockContentDetail.image}}
            style={styles.heroImage}>
            <View style={styles.heroOverlay} />
          </ImageBackground>
        </View>

        {/* Content Info - Mobile design'dan birebir */}
        <View style={styles.contentContainer}>
          <Text style={styles.contentTitle}>{mockContentDetail.title}</Text>

          <View style={styles.metaContainer}>
            <Text style={styles.metaText}>
              <Text style={styles.metaBold}>{mockContentDetail.duration}</Text>
            </Text>
            <View style={styles.metaDot} />
            <Text style={styles.metaText}>{mockContentDetail.difficulty}</Text>
            <View style={styles.metaDot} />
            <Text style={styles.metaText}>{mockContentDetail.category}</Text>
          </View>

          {/* Tags - Mobile design'dan birebir */}
          <View style={styles.tagsContainer}>
            {mockContentDetail.tags.map((tag, index) => (
              <View key={index} style={styles.tag}>
                <Text style={styles.tagText}>{tag}</Text>
              </View>
            ))}
          </View>

          {/* Description - Mobile design'dan birebir */}
          <Text style={styles.description}>
            {mockContentDetail.description}
          </Text>

          {/* Stats Grid - Mobile design'dan birebir */}
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Views</Text>
              <Text style={styles.statValue}>
                {mockContentDetail.stats.views}
              </Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Likes</Text>
              <Text style={styles.statValue}>
                {mockContentDetail.stats.likes}
              </Text>
            </View>
          </View>

          {/* Similar Content - Mobile design'dan birebir */}
          <View style={styles.similarSection}>
            <Text style={styles.similarTitle}>Similar Content</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.similarScroll}>
              {mockContentDetail.similarContent.map(content => (
                <TouchableOpacity
                  key={content.id}
                  style={styles.similarCard}
                  onPress={() => handleSimilarContentPress(content.id)}>
                  <Image
                    source={{uri: content.image}}
                    style={styles.similarImage}
                  />
                  <Text style={styles.similarText}>{content.title}</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Bar - Mobile design'dan birebir */}
      <View style={styles.bottomBar}>
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleBookmark}>
            <Icon name="bookmark-border" size={20} color="#374151" />
            <Text style={styles.secondaryButtonText}>Bookmark</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.secondaryButton}
            onPress={handleShare}>
            <Icon name="share" size={20} color="#374151" />
            <Text style={styles.secondaryButtonText}>Share</Text>
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleStartReading}>
          <Text style={styles.primaryButtonText}>Start Reading</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  heroContainer: {
    position: 'relative',
    height: 240,
    width: '100%',
  },
  heroImage: {
    width: '100%',
    height: '100%',
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  contentContainer: {
    padding: 20,
    paddingBottom: 120,
  },
  contentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'Lexend',
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 16,
  },
  metaText: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Lexend',
  },
  metaBold: {
    fontWeight: '500',
  },
  metaDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#9CA3AF',
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 16,
  },
  tag: {
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 12,
    paddingVertical: 4,
  },
  tagText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Lexend',
  },
  description: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
    marginTop: 16,
    fontFamily: 'Lexend',
  },
  statsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginTop: 24,
  },
  statCard: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
    padding: 16,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    fontFamily: 'Lexend',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 4,
    fontFamily: 'Lexend',
  },
  similarSection: {
    marginTop: 32,
  },
  similarTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    fontFamily: 'Lexend',
  },
  similarScroll: {
    flexDirection: 'row',
  },
  similarCard: {
    width: 160,
    marginRight: 16,
  },
  similarImage: {
    width: '100%',
    height: 208,
    borderRadius: 8,
  },
  similarText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
    marginTop: 8,
    fontFamily: 'Lexend',
  },
  bottomBar: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    height: 44,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Lexend',
  },
  primaryButton: {
    height: 48,
    borderRadius: 8,
    backgroundColor: '#3B82F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
});
