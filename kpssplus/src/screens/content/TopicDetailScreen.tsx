import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, spacing} from '../../theme';
import {contentService} from '../../services/api/contentService';
import {Content} from '../../services/api/types';

interface Props {
  navigation: any;
  route: any;
}

export const TopicDetailScreen: React.FC<Props> = ({navigation, route}) => {
  const {topicId} = route.params as {topicId: string};
  const [topic, setTopic] = useState<Content | null>(null);
  const [relatedContent, setRelatedContent] = useState<Content[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadTopicDetail();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topicId]);

  const loadTopicDetail = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load topic detail
      const topicData = await contentService.getContent(topicId);
      setTopic(topicData);

      // Load related content
      try {
        const relatedResponse = await contentService.getContentList({
          subject: topicData.subject,
        });
        setRelatedContent(
          relatedResponse.data.filter(item => item.id !== topicId).slice(0, 5),
        );
      } catch (relatedError) {
        console.error('Error loading related content:', relatedError);
        // Continue without related content
      }
    } catch (err) {
      console.error('Error loading topic detail:', err);
      setError('Konu detayı yüklenirken bir hata oluştu');

      // Fallback to mock data
      const mockTopic: Content = {
        id: topicId,
        title: 'Cümle Bilgisi',
        description:
          'Cümlenin öğeleri, cümle türleri ve yapısı hakkında detaylı bilgiler. Bu konuda temel dilbilgisi kurallarını öğreneceksiniz.',
        content_type: 'text',
        subject: 'Türkçe',
        difficulty_level: 'beginner',
        duration: 45,
        thumbnail_url: undefined,

        is_premium: false,

        view_count: 1250,
        like_count: 89,
        tags: ['dilbilgisi', 'cümle', 'temel'],
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      };
      setTopic(mockTopic);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartStudy = () => {
    if (topic) {
      navigation.navigate('ContentPlayer', {contentId: topic.id});
    }
  };

  const handleRelatedContentPress = (contentId: string) => {
    navigation.navigate('ContentDetail', {contentId});
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Konu Detayı</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Konu detayı yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!topic) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Konu Detayı</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.errorContainer}>
          <Icon name="alert-circle-outline" size={64} color={colors.error} />
          <Text style={styles.errorTitle}>Konu Bulunamadı</Text>
          <Text style={styles.errorDescription}>
            {error || 'İstenen konu bulunamadı.'}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Konu Detayı</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Icon name="share-outline" size={24} color={colors.text.primary} />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Topic Header */}
        <View style={styles.topicHeader}>
          {topic.thumbnail_url ? (
            <Image
              source={{uri: topic.thumbnail_url}}
              style={styles.topicImage}
            />
          ) : (
            <View style={styles.topicImagePlaceholder}>
              <Icon name="book-outline" size={48} color={colors.primary} />
            </View>
          )}

          <View style={styles.topicInfo}>
            <Text style={styles.topicTitle}>{topic.title}</Text>
            <Text style={styles.topicSubject}>{topic.subject}</Text>

            <View style={styles.topicStats}>
              <View style={styles.statItem}>
                <Icon
                  name="time-outline"
                  size={16}
                  color={colors.text.secondary}
                />
                <Text style={styles.statText}>{topic.duration} dk</Text>
              </View>
              <View style={styles.statItem}>
                <Icon
                  name="eye-outline"
                  size={16}
                  color={colors.text.secondary}
                />
                <Text style={styles.statText}>
                  {topic.view_count} görüntülenme
                </Text>
              </View>
              <View style={styles.statItem}>
                <Icon
                  name="heart-outline"
                  size={16}
                  color={colors.text.secondary}
                />
                <Text style={styles.statText}>{topic.like_count} beğeni</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Topic Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Konu Açıklaması</Text>
          <Text style={styles.topicDescription}>{topic.description}</Text>
        </View>

        {/* Tags */}
        {topic.tags && topic.tags.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Etiketler</Text>
            <View style={styles.tagsContainer}>
              {topic.tags.map((tag, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>{tag}</Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Related Content */}
        {relatedContent.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>İlgili Konular</Text>
            {relatedContent.map(item => (
              <TouchableOpacity
                key={item.id}
                style={styles.relatedItem}
                onPress={() => handleRelatedContentPress(item.id)}>
                <View style={styles.relatedImagePlaceholder}>
                  <Icon
                    name="document-text-outline"
                    size={24}
                    color={colors.primary}
                  />
                </View>
                <View style={styles.relatedInfo}>
                  <Text style={styles.relatedTitle} numberOfLines={2}>
                    {item.title}
                  </Text>
                  <Text style={styles.relatedSubject}>{item.subject}</Text>
                </View>
                <Icon
                  name="chevron-forward"
                  size={20}
                  color={colors.text.secondary}
                />
              </TouchableOpacity>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Start Study Button */}
      <View style={styles.bottomContainer}>
        <TouchableOpacity style={styles.startButton} onPress={handleStartStudy}>
          <Icon name="play" size={20} color={colors.surface} />
          <Text style={styles.startButtonText}>Çalışmaya Başla</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h4,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: spacing[2],
  },
  shareButton: {
    padding: spacing[2],
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[4],
  },
  loadingText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginTop: spacing[3],
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: spacing[4],
  },
  errorTitle: {
    ...typography.h4,
    marginTop: spacing[3],
    marginBottom: spacing[2],
  },
  errorDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  scrollView: {
    flex: 1,
  },
  topicHeader: {
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  topicImage: {
    width: '100%',
    height: 200,
    borderRadius: 12,
    marginBottom: spacing[3],
  },
  topicImagePlaceholder: {
    width: '100%',
    height: 200,
    backgroundColor: colors.surface,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing[3],
    borderWidth: 1,
    borderColor: colors.border,
  },
  topicInfo: {
    gap: spacing[2],
  },
  topicTitle: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  topicSubject: {
    ...typography.body1,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: spacing[2],
  },
  topicStats: {
    flexDirection: 'row',
    gap: spacing[4],
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[1],
  },
  statText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  section: {
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[3],
  },
  topicDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    lineHeight: 24,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing[2],
  },
  tag: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[1],
    borderRadius: 16,
    borderWidth: 1,
    borderColor: colors.border,
  },
  tagText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  relatedItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[3],
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginBottom: spacing[2],
    borderWidth: 1,
    borderColor: colors.border,
  },
  relatedImagePlaceholder: {
    width: 48,
    height: 48,
    backgroundColor: colors.background,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: spacing[3],
  },
  relatedInfo: {
    flex: 1,
    gap: spacing[1],
  },
  relatedTitle: {
    ...typography.body2,
    fontWeight: '600',
  },
  relatedSubject: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  bottomContainer: {
    padding: spacing[4],
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  startButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing[3],
    borderRadius: 12,
    gap: spacing[2],
  },
  startButtonText: {
    ...typography.button,
    color: colors.surface,
  },
});
