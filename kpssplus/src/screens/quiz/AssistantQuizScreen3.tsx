import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {quizService} from '../../services/api/quizService';
import {QuizResult} from '../../services/api/types';

interface Props {
  route?: {
    params?: {
      quizId?: string;
      sessionId?: string;
    };
  };
  navigation?: any;
}

export const AssistantQuizScreen3: React.FC<Props> = ({route, navigation}) => {
  const {sessionId} = route?.params || {};
  const [quizCompleted, setQuizCompleted] = useState(false);
  const [quizResult, setQuizResult] = useState<QuizResult | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [scaleAnimation] = useState(new Animated.Value(0));
  const [fadeAnimation] = useState(new Animated.Value(0));

  const loadQuizResult = async () => {
    if (!sessionId) {
      Alert.alert('Hata', 'Quiz oturum ID bulunamadı.');
      navigation?.goBack();
      return;
    }

    try {
      setIsLoading(true);
      const result = await quizService.getQuizResult(sessionId);
      setQuizResult(result);
      setQuizCompleted(true);
    } catch (error: any) {
      console.error('Error loading quiz result:', error);
      Alert.alert(
        'Hata',
        error.message || 'Quiz sonuçları yüklenirken bir hata oluştu.',
      );
      navigation?.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadQuizResult();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const performance = {
    subjects: [
      {name: 'Matematik', score: 90, color: '#10B981'},
      {name: 'Türkçe', score: 85, color: '#1E13EC'},
      {name: 'Tarih', score: 80, color: '#F59E0B'},
      {name: 'Coğrafya', score: 75, color: '#EF4444'},
    ],
    achievements: [
      {
        id: '1',
        name: 'Hızlı Çözücü',
        description: "Quiz'i 15 dakikadan kısa sürede tamamladın",
        icon: 'flash',
        color: '#F59E0B',
      },
      {
        id: '2',
        name: 'Matematik Ustası',
        description: "Matematik sorularının %90'ını doğru cevapladın",
        icon: 'calculator',
        color: '#10B981',
      },
    ],
  };

  useEffect(() => {
    if (quizCompleted) {
      Animated.parallel([
        Animated.spring(scaleAnimation, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnimation, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [quizCompleted, scaleAnimation, fadeAnimation]);

  const getScoreColor = (score: number) => {
    if (score >= 80) {
      return '#10B981';
    }
    if (score >= 60) {
      return '#F59E0B';
    }
    return '#EF4444';
  };

  const getScoreMessage = (score: number) => {
    if (score >= 90) {
      return 'Mükemmel! 🎉';
    }
    if (score >= 80) {
      return 'Harika! 👏';
    }
    if (score >= 70) {
      return 'İyi! 👍';
    }
    if (score >= 60) {
      return 'Fena değil! 😊';
    }
    return 'Daha çok çalışmalısın! 💪';
  };

  if (isLoading || !quizCompleted) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Quiz sonuçları hesaplanıyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="close" size={24} color="#71717A" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quiz Tamamlandı</Text>
        <TouchableOpacity style={styles.shareButton}>
          <Icon name="share-social" size={20} color="#1E13EC" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Score Card */}
        <Animated.View
          style={[
            styles.scoreCard,
            {
              transform: [{scale: scaleAnimation}],
              opacity: fadeAnimation,
            },
          ]}>
          <View style={styles.scoreCircle}>
            <Text
              style={[
                styles.scoreText,
                {color: getScoreColor(quizResult?.score || 0)},
              ]}>
              {quizResult?.score || 0}
            </Text>
            <Text style={styles.scoreLabel}>PUAN</Text>
          </View>
          <Text style={styles.scoreMessage}>
            {getScoreMessage(quizResult?.score || 0)}
          </Text>
          <View style={styles.scoreDetails}>
            <View style={styles.scoreDetailItem}>
              <Text style={styles.scoreDetailValue}>
                {quizResult?.correct_answers || 0}/
                {quizResult?.total_questions || 0}
              </Text>
              <Text style={styles.scoreDetailLabel}>Doğru Cevap</Text>
            </View>
            <View style={styles.scoreDetailItem}>
              <Text style={styles.scoreDetailValue}>
                {quizResult?.time_taken
                  ? `${Math.floor(quizResult.time_taken / 60)}:${String(
                      quizResult.time_taken % 60,
                    ).padStart(2, '0')}`
                  : '0:00'}
              </Text>
              <Text style={styles.scoreDetailLabel}>Süre</Text>
            </View>
          </View>
        </Animated.View>

        {/* Subject Performance */}
        <Animated.View
          style={[styles.performanceCard, {opacity: fadeAnimation}]}>
          <Text style={styles.cardTitle}>Konu Bazında Performans</Text>
          {performance.subjects.map((subject, index) => (
            <View key={index} style={styles.subjectItem}>
              <Text style={styles.subjectName}>{subject.name}</Text>
              <View style={styles.subjectScoreContainer}>
                <View style={styles.subjectProgressBar}>
                  <View
                    style={[
                      styles.subjectProgressFill,
                      {
                        width: `${subject.score}%`,
                        backgroundColor: subject.color,
                      },
                    ]}
                  />
                </View>
                <Text style={[styles.subjectScore, {color: subject.color}]}>
                  {subject.score}%
                </Text>
              </View>
            </View>
          ))}
        </Animated.View>

        {/* Achievements */}
        <Animated.View
          style={[styles.achievementsCard, {opacity: fadeAnimation}]}>
          <Text style={styles.cardTitle}>Kazanılan Rozetler</Text>
          {performance.achievements.map(achievement => (
            <View key={achievement.id} style={styles.achievementItem}>
              <View
                style={[
                  styles.achievementIcon,
                  {backgroundColor: achievement.color},
                ]}>
                <Icon name={achievement.icon} size={24} color={colors.white} />
              </View>
              <View style={styles.achievementInfo}>
                <Text style={styles.achievementName}>{achievement.name}</Text>
                <Text style={styles.achievementDescription}>
                  {achievement.description}
                </Text>
              </View>
            </View>
          ))}
        </Animated.View>

        {/* Recommendations */}
        <Animated.View
          style={[styles.recommendationsCard, {opacity: fadeAnimation}]}>
          <Text style={styles.cardTitle}>Öneriler</Text>
          <View style={styles.recommendationItem}>
            <Icon name="book" size={20} color="#1E13EC" />
            <Text style={styles.recommendationText}>
              Coğrafya konularında daha fazla çalışma yapmanı öneriyoruz
            </Text>
          </View>
          <View style={styles.recommendationItem}>
            <Icon name="time" size={20} color="#F59E0B" />
            <Text style={styles.recommendationText}>
              Günlük 30 dakika quiz çözerek performansını artırabilirsin
            </Text>
          </View>
        </Animated.View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity style={styles.secondaryButton}>
          <Text style={styles.secondaryButtonText}>Tekrar Çöz</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.primaryButton}>
          <Text style={styles.primaryButtonText}>Ana Sayfa</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingSpinner: {
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
  },
  shareButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
  },
  scoreCard: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 16,
    backgroundColor: colors.white,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  scoreCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 8,
    borderColor: '#E5E7EB',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  scoreText: {
    fontSize: 36,
    fontWeight: 'bold',
  },
  scoreLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6B7280',
    marginTop: 4,
  },
  scoreMessage: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 24,
  },
  scoreDetails: {
    flexDirection: 'row',
    gap: 32,
  },
  scoreDetailItem: {
    alignItems: 'center',
  },
  scoreDetailValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
  },
  scoreDetailLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  performanceCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
    borderRadius: 16,
    backgroundColor: colors.white,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
  },
  subjectItem: {
    marginBottom: 16,
  },
  subjectName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0E0D1B',
    marginBottom: 8,
  },
  subjectScoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  subjectProgressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
  },
  subjectProgressFill: {
    height: '100%',
    borderRadius: 4,
  },
  subjectScore: {
    fontSize: 14,
    fontWeight: 'bold',
    minWidth: 40,
    textAlign: 'right',
  },
  achievementsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
    borderRadius: 16,
    backgroundColor: colors.white,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 12,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  achievementInfo: {
    flex: 1,
  },
  achievementName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  recommendationsCard: {
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 20,
    borderRadius: 16,
    backgroundColor: colors.white,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  recommendationItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 12,
  },
  recommendationText: {
    flex: 1,
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 12,
  },
  secondaryButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    backgroundColor: colors.white,
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
  },
  primaryButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    backgroundColor: '#1E13EC',
  },
  primaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
