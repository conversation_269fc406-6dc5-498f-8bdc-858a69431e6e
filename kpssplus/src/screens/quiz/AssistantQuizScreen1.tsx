import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {quizService} from '../../services/api/quizService';
import {Quiz} from '../../services/api/types';

interface QuizQuestion {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank';
  options: Array<{
    id: string;
    option_text: string;
    is_correct: boolean;
  }>;
  correct_answer: string;
  explanation?: string;
  points: number;
  order_index: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  subject: string;
  time_limit: number;
}

interface Props {
  route?: {
    params?: {
      quizId?: string;
    };
  };
  navigation?: any;
}

export const AssistantQuizScreen1: React.FC<Props> = ({route, navigation}) => {
  const quizId = route?.params?.quizId;
  const [isListening, setIsListening] = useState(false);
  const [isReading, setIsReading] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [_quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [micAnimation] = useState(new Animated.Value(1));

  const question = questions[currentQuestion - 1] || {
    id: '1',
    question_text: "Türkiye Cumhuriyeti'nin kurucusu kimdir?",
    options: [
      {id: '1', option_text: 'Mustafa Kemal Atatürk', is_correct: true},
      {id: '2', option_text: 'İsmet İnönü', is_correct: false},
      {id: '3', option_text: 'Celal Bayar', is_correct: false},
      {id: '4', option_text: 'Fevzi Çakmak', is_correct: false},
    ],
    difficulty_level: 'intermediate',
    subject: 'Tarih',
    explanation: '',
    time_limit: 60,
  };

  const loadQuizData = async () => {
    if (!quizId) {
      Alert.alert('Hata', 'Quiz ID bulunamadı.');
      navigation?.goBack();
      return;
    }

    try {
      setIsLoading(true);
      const [_quizData, questionsData] = await Promise.all([
        quizService.getQuizById(quizId),
        quizService.getQuizQuestions(quizId),
      ]);

      setQuiz(_quizData);
      setQuestions(questionsData as unknown as QuizQuestion[]);
    } catch (error: any) {
      console.error('Error loading quiz data:', error);
      Alert.alert(
        'Hata',
        error.message || 'Quiz verileri yüklenirken bir hata oluştu.',
      );
      navigation?.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadQuizData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quizId]);

  useEffect(() => {
    if (isListening) {
      const animation = Animated.loop(
        Animated.sequence([
          Animated.timing(micAnimation, {
            toValue: 1.2,
            duration: 500,
            useNativeDriver: true,
          }),
          Animated.timing(micAnimation, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }),
        ]),
      );
      animation.start();
      return () => animation.stop();
    }
  }, [isListening, micAnimation]);

  const handleVoiceAnswer = () => {
    setIsListening(true);
    // Simulate voice recognition
    setTimeout(() => {
      setIsListening(false);
      // Process answer
    }, 3000);
  };

  const handleReadAgain = () => {
    setIsReading(true);
    // Simulate TTS reading
    setTimeout(() => {
      setIsReading(false);
    }, 2000);
  };

  const handleSkip = () => {
    if (currentQuestion < questions.length) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Quiz yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="close" size={24} color="#71717A" />
        </TouchableOpacity>
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            {currentQuestion}/{questions.length || 10}
          </Text>
          <View style={styles.progressBar}>
            <View
              style={[
                styles.progressFill,
                {
                  width: `${
                    (currentQuestion / (questions.length || 10)) * 100
                  }%`,
                },
              ]}
            />
          </View>
        </View>
      </View>

      {/* Assistant Avatar */}
      <View style={styles.assistantContainer}>
        <View
          style={[
            styles.assistantAvatar,
            isReading && styles.assistantAvatarActive,
          ]}>
          <Icon name="person" size={48} color={colors.white} />
        </View>
        <Text style={styles.assistantName}>KPSS Asistanı</Text>
        <Text style={styles.assistantStatus}>
          {isReading
            ? 'Soruyu okuyor...'
            : isListening
            ? 'Cevabınızı dinliyor...'
            : 'Hazır'}
        </Text>
      </View>

      {/* Question */}
      <View style={styles.questionContainer}>
        <Text style={styles.questionNumber}>Soru {currentQuestion}</Text>
        <Text style={styles.questionText}>{question.question_text}</Text>

        <View style={styles.optionsContainer}>
          {question.options.map((option, index) => (
            <View key={option.id} style={styles.optionItem}>
              <Text style={styles.optionLetter}>
                {String.fromCharCode(65 + index)}
              </Text>
              <Text style={styles.optionText}>{option.option_text}</Text>
            </View>
          ))}
        </View>
      </View>

      {/* Voice Controls */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleReadAgain}
          disabled={isReading}>
          <Icon name="volume-high" size={20} color="#1E13EC" />
          <Text style={styles.secondaryButtonText}>
            {isReading ? 'Okuyor...' : 'Tekrar Oku'}
          </Text>
        </TouchableOpacity>

        <Animated.View style={{transform: [{scale: micAnimation}]}}>
          <TouchableOpacity
            style={[styles.micButton, isListening && styles.micButtonActive]}
            onPress={handleVoiceAnswer}
            disabled={isListening || isReading}>
            <Icon
              name={isListening ? 'mic' : 'mic-outline'}
              size={32}
              color={colors.white}
            />
          </TouchableOpacity>
        </Animated.View>

        <TouchableOpacity style={styles.secondaryButton} onPress={handleSkip}>
          <Icon name="play-skip-forward" size={20} color="#6B7280" />
          <Text style={styles.secondaryButtonText}>Atla</Text>
        </TouchableOpacity>
      </View>

      {/* Instructions */}
      <View style={styles.instructionsContainer}>
        <Text style={styles.instructionsText}>
          {isListening
            ? 'Cevabınızı söyleyin (A, B, C veya D)'
            : 'Mikrofon butonuna basarak sesli cevap verin'}
        </Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
    gap: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    flex: 1,
    alignItems: 'center',
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    marginBottom: 8,
  },
  progressBar: {
    width: '100%',
    height: 4,
    backgroundColor: '#E5E7EB',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1E13EC',
    borderRadius: 2,
  },
  assistantContainer: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  assistantAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#1E13EC',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  assistantAvatarActive: {
    backgroundColor: '#10B981',
  },
  assistantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 4,
  },
  assistantStatus: {
    fontSize: 14,
    color: '#6B7280',
  },
  questionContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  questionNumber: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E13EC',
    marginBottom: 8,
  },
  questionText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#0E0D1B',
    lineHeight: 28,
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 16,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  optionLetter: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    textAlign: 'center',
    lineHeight: 32,
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B7280',
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: '#0E0D1B',
    lineHeight: 24,
  },
  controlsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 24,
  },
  secondaryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20,
    backgroundColor: '#F9FAFB',
    gap: 8,
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1E13EC',
  },
  micButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#1E13EC',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  micButtonActive: {
    backgroundColor: '#EF4444',
  },
  instructionsContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  instructionsText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
  },
});
