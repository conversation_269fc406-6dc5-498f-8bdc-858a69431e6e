import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {quizService} from '../../services/api/quizService';
import {Quiz} from '../../services/api/types';

// Mock data - mobile design'dan alınan
const mockQuizData = {
  popular: [
    {
      id: 1,
      title: 'General Knowledge Championship',
      questions: 10,
      duration: 15,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuCGBTLgXq9745QibAN0-zRwgsskIsEaS4Tiiz8UG7tcfYBLwn9d6xG1pIXkQxbQc6J0sQSXJJxCbBLpQpuxeW6GRozXj0204_LjufwXhZGzOvshpnmq4l9RDUWFbng4RZXe0hOWoPvAgWsDWf79LlJmleIEhvDPluXXBWrT47-FOLXCsej0F8FudpOWyMeQ_A4wQvXjrcvZ1-glkTcw2xyJnwabndwSIVeb0vZJm5FboKLqpafnG-4FO30iIoqLHlX-AjlZh0PTOSQ',
    },
    {
      id: 2,
      title: 'Current Affairs Challenge',
      questions: 12,
      duration: 20,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuDYjn5j_KBmeSTeAWH_PZ54xtSThQhOOjjqRDVLQWRAwiDZ_xmA4gCwyDhnl5r9jd_RVFYXzXBR_F-ikuQgxm4nPcclmiQrvxcBtC5a4L9v2fghRpE_k-odkBOmtKHSl06YXIzFmPiPlnhYDER_WguP9Q37GrFHwy61NHLWBwHYSvdwqedyxhzne0TKk9sM1kTxDTocSR7qnmBKhXzGMXJwFFRWQMd7DlopLDLl5dDj_lKVICXYhgOgzEFUPoWCWEDGwc7SKM_Shwk',
    },
  ],
  recommended: [
    {
      id: 3,
      title: 'Turkish Language Basics',
      questions: 15,
      duration: 25,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuDcwJudkhGHhqeWBI7hXHT8-Mjd_LElCRyYqACRlh9Mjc8DlDkgRNlQPbF6TwBKl0ZftR0SVtUv4SHUPgM95mmhfkSfEbUjPWWPvPIpv9Q6H9ArObmreV3Qjj6qEwPFpBnYDf1cgO5xLvU27uQBZpQJeZG_U9dRgP6QhAezs5wsu-E1mdMWsbNiADT3XzhCtLf78gT09j4InMOXAawboVeSeWYyg976jkYFE2FOe6-m7D2qy35F7F0D80DiXgIb7xo3CwP5UgbhkQk',
    },
    {
      id: 4,
      title: 'Mathematics Fundamentals',
      questions: 20,
      duration: 30,
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuApHOS0U2AO64riHWUW_MsMnmcBEqAYOUJse6qoFfu4vuxVUqRZVb-k4_ZixD4_bqKmWrdN73yvcrQGJ3HjDFkSVYqHznkokBNgwUkrWtJYo9Ookfa0h9Zc7z0M1yJ-yNcW0OMJ25JGdzV3NW-qzm8Dti71R0RED5C9TVNPwLZf-Ut_emCLRivNwvNKGynNXyIXhv3PUKw2R5qDBZ7RWYo0GWhjqK_3oR0ZQos113oKmBz7CR_7ObfFc72__3ox9L-zHOMorzhnR0c',
    },
  ],
};

export const QuizHomeScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const renderQuizCard = (quiz: any) => (
    <View key={quiz.id} style={styles.quizCard}>
      <Image source={{uri: quiz.image}} style={styles.quizImage} />
      <View style={styles.quizInfo}>
        <Text style={styles.quizTitle} numberOfLines={1}>
          {quiz.title}
        </Text>
        <Text style={styles.quizMeta}>
          {quiz.questions} Questions • {quiz.duration} min
        </Text>
      </View>
      <View style={styles.quizActions}>
        <TouchableOpacity style={styles.startButton}>
          <Text style={styles.startButtonText}>Start</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.detailsButton}>
          <Text style={styles.detailsButtonText}>Details</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Search Bar - Mobile design'dan birebir */}
      <View style={styles.searchContainer}>
        <View style={styles.searchBar}>
          <View style={styles.searchIcon}>
            <Icon name="search" size={24} color="#6B7280" />
          </View>
          <TextInput
            style={styles.searchInput}
            placeholder="Search quizzes..."
            placeholderTextColor="#6B7280"
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Filter Buttons - Mobile design'dan birebir */}
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.filterContainer}>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Category</Text>
          <Icon name="expand-more" size={20} color="#6B7280" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Difficulty</Text>
          <Icon name="expand-more" size={20} color="#6B7280" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterButton}>
          <Text style={styles.filterButtonText}>Duration</Text>
          <Icon name="expand-more" size={20} color="#6B7280" />
        </TouchableOpacity>
      </ScrollView>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Guest Alert - Mobile design'dan birebir */}
        <View style={styles.guestAlert}>
          <Icon
            name="info"
            size={24}
            color="#1D4ED8"
            style={styles.alertIcon}
          />
          <View style={styles.alertContent}>
            <Text style={styles.alertTitle}>Guest User</Text>
            <Text style={styles.alertText}>
              You have 2 free quizzes left.{' '}
              <Text style={styles.alertLink}>Sign up</Text> for unlimited
              access.
            </Text>
          </View>
        </View>

        {/* Popular Section - Mobile design'dan birebir */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Popular</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See all</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.quizList}>
            {mockQuizData.popular.map(renderQuizCard)}
          </ScrollView>
        </View>

        {/* Recommended Section - Mobile design'dan birebir */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recommended For You</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See all</Text>
            </TouchableOpacity>
          </View>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.quizList}>
            {mockQuizData.recommended.map(renderQuizCard)}
          </ScrollView>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },

  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  searchBar: {
    flexDirection: 'row',
    height: 48,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  searchIcon: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 16,
    borderTopLeftRadius: 12,
    borderBottomLeftRadius: 12,
    backgroundColor: '#FFFFFF',
  },
  searchInput: {
    flex: 1,
    paddingHorizontal: 16,
    fontSize: 16,
    color: '#374151',
    backgroundColor: '#FFFFFF',
    borderTopRightRadius: 12,
    borderBottomRightRadius: 12,
    fontFamily: 'Lexend',
  },
  filterContainer: {
    paddingHorizontal: 16,
    paddingBottom: 4,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 40,
    paddingLeft: 16,
    paddingRight: 12,
    marginRight: 12,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginRight: 8,
    fontFamily: 'Lexend',
  },
  scrollView: {
    flex: 1,
  },
  guestAlert: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#DBEAFE',
    borderLeftWidth: 4,
    borderLeftColor: '#3B82F6',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 8,
    marginBottom: 16,
    borderRadius: 6,
  },
  alertIcon: {
    marginRight: 12,
  },
  alertContent: {
    flex: 1,
  },
  alertTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1E40AF',
    fontFamily: 'Lexend',
  },
  alertText: {
    fontSize: 14,
    color: '#1E40AF',
    fontFamily: 'Lexend',
  },
  alertLink: {
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
  section: {
    paddingVertical: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 8,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111018',
    fontFamily: 'Lexend',
  },
  seeAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#413bb0',
    fontFamily: 'Lexend',
  },
  quizList: {
    paddingLeft: 16,
    paddingRight: 4,
  },
  quizCard: {
    width: 256,
    backgroundColor: '#FFFFFF',
    padding: 12,
    borderRadius: 12,
    marginRight: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: '#F3F4F6',
  },
  quizImage: {
    width: '100%',
    height: 128,
    borderRadius: 8,
    marginBottom: 12,
  },
  quizInfo: {
    marginBottom: 12,
  },
  quizTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#111018',
    fontFamily: 'Lexend',
  },
  quizMeta: {
    fontSize: 14,
    color: '#6B7280',
    fontFamily: 'Lexend',
  },
  quizActions: {
    flexDirection: 'row',
    gap: 8,
  },
  startButton: {
    flex: 1,
    height: 40,
    backgroundColor: '#413bb0',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontFamily: 'Lexend',
  },
  detailsButton: {
    height: 40,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
  },
  detailsButtonText: {
    color: '#374151',
    fontWeight: '500',
    fontFamily: 'Lexend',
  },
});
