import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  ImageBackground,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'da<PERSON> <PERSON><PERSON>
const mockQuizData = {
  title: 'General Knowledge Quiz',
  questions: 10,
  duration: 15,
  difficulty: 'Medium',
  description:
    'Test your knowledge on current events, history, and geography. This quiz is designed to challenge your understanding of general knowledge topics.',
  image:
    'https://lh3.googleusercontent.com/aida-public/AB6AXuDkgfZlFx_FwBS-2oNKcv1-lUFuoxLKtCmMpVdjv0djgzAVe3XZ1xuEtnNkMC9n21wICenTdznzMqQ4plAxHUjYbQ9SpgSbQnYMM88M1YldzhIrgU-LfYILKL1K0f98wiHKyYJMAqs4bmEls39dMU58-KJXFccAgAKPH5kvIVIu8AtDcvhRHgviFsiqZpNrIlS_xc1fpwTjciuHzZtLKRYXaysPuL3Uw0HSrzxgwpb0Z0ruSZueZpopZWOQV-P-GKBjeNvefV2Ydow',
  statistics: {
    attempts: 125,
    averageScore: '75%',
  },
  leaderboard: [
    {
      id: 1,
      rank: 1,
      name: 'Liam Carter',
      score: '90%',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuBCVtZbWlZT25TgSjXuwYyaDR9NcvNTlvhUAudOINC6QhZcZ0FYqKJGbRZhdmf2_U-XghiMxo8ZyOkagugwH1NOePi_f3deUkwv-tpjKSNXes4bH5uwZibi8X9e3NY3BM5hgJHNp7MfyQpUEQyVFS1BePHLdn_-IpJ29QD5RlvDoELJnDSWSW0qHhWi0lXhTrMoMvUAc3kJSxNCWUEsAFTJUV0MnWrjf0HwUoXH5RwCOz7Je4RTwmv81xfHRvYHTQcaRhpaVvSUQcA',
    },
    {
      id: 2,
      rank: 2,
      name: 'Olivia Bennett',
      score: '85%',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuBPUSz0pc46GUAVHT-3o7Kfh2dz0tVpDeszgI2Wp3HvWOyArj-4EGGsbM8wESMyZbhuFBgKF2A9OShhJH7TeN_mdhh5Bdv21482TefmlO7jeI5Lsbr5cGJ-7K1Fqi8LbAcEgVlC_Ka4c3kuF0kKfAcgFc9Xd3TdbeV4I2VaTi6lvD8GwE8gbQK0XX2ApicqRUryJ6nUUlEvSwEzKdDACCREylpY9XjGd6q1xbZIzt-mYs4186gnJ9x0Dk8eV254DnfBnm9gp0IHsvU',
    },
    {
      id: 3,
      rank: 3,
      name: 'Ethan Harper',
      score: '80%',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuCXrzEBzu1OWLJzw6gSFWbr5rof-fHdzMzRZglaW4-g2UEYrz_yFLI3NLDbvIZYaHqb_yo4vsH9nTCVSuPEUqmPCavv48MylkCVkHDg4nwY1XkDBBdMerq4XH2ctnJmZCC5eWfuVnCjWKFaO4u8Qm7tV44cSnOYc3htc6-XSMwoWsgCjsaMGO1ZcJxkmtBFzdZ_UBSGw3syxj8JnlPQW8BhunTEYjQId_OJ7JwNZ1fTGoPBseZWxHSxYnxysJN4RFl8l6k6Pn15Zvk',
    },
  ],
  similarQuizzes: [
    {
      id: 1,
      title: 'History Quiz',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAgC6tTdqmMFUgbyeXhtgPHq4gzIEaLymoN5_scPO1EHRmU_QQ2jOSFhDgjWfVq5JOR3R4aVuD_e99g2KMfgjNMBxBKfKFpm4iOb7CZdQDe_qJDTsaMnpmgQ22QistnWBmHhTSWWgVsgtqXbDAwo8I_jLCj9K5NpvjuqvlgYg6jtse9FFRzfEp2XmjWDrbSjv4v5B3sZE68rxEPkWdVbCmKR93UaONueiS7e4hbrNUHaLH_6UIfceh2gojqPr_hd8C1RFqw1cBEJ68',
    },
    {
      id: 2,
      title: 'Geography Quiz',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuCxKSgAY_p5b5zpWfNplaac7p6n3rAcyG4HP4JmfuBwcYGQakZyPmfOjrw_AID75YPtFa7FUexANgbhRjH2bFcGQqQBvJ60FR1_7C_kRnJ3dmSyxX5LQHffZIB8Vj3H5FHDA_6DUc5Xgf8S3gs7NRwLDbRMHkv3phajKBnkq7DinLe4j0w-nAt-_5rELbdt3B_lhcy8hKTw5gxRRc2bPWCAR68VdmjrQPhQ2Mj5YIYe-yRO3jOYomT7szu4EtKTEksi1dMEeC2nv9A',
    },
    {
      id: 3,
      title: 'Current Events Quiz',
      image:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuCqsC-ZzVAUx7wITAlNN4nur7ImnjDSVQ3V_AfftQQpnMmXBVBxAf1laxmFrosxSL2PscfwYgccmLQpk1m2hJTe8kJC2CnEtp-Q7e173UjjUYeU94Xwz-ZMOuIPiAFO_-RnXqmBG-jmc0fw--QBmjinQuO2jOPuH2A8jJb6lBuE45Y7GAffPgkTU0NJOHU-JXaWmSOtLd9nv5nKAGoUPYkwCIZTZ3S2eT6nYCoKNgrJGjOZTvrWq8C1FWFGmhbLrkw-s-VaLb6yoC8',
    },
  ],
};

export const QuizDetailScreen: React.FC = () => {
  const navigation = useNavigation();

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#EAB308'; // text-yellow-500
      case 2:
        return '#9CA3AF'; // text-gray-400
      case 3:
        return '#B45309'; // text-amber-700
      default:
        return '#9CA3AF';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#111018" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quiz Details</Text>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Hero Image Section - Mobile design'dan birebir */}
        <View style={styles.heroContainer}>
          <ImageBackground
            source={{uri: mockQuizData.image}}
            style={styles.heroImage}>
            <View style={styles.heroOverlay} />
            <View style={styles.heroContent}>
              <Text style={styles.heroTitle}>{mockQuizData.title}</Text>
              <Text style={styles.heroMeta}>
                {mockQuizData.questions} Questions · {mockQuizData.duration}{' '}
                Minutes · {mockQuizData.difficulty}
              </Text>
            </View>
          </ImageBackground>
        </View>

        <View style={styles.content}>
          {/* Description - Mobile design'dan birebir */}
          <Text style={styles.description}>{mockQuizData.description}</Text>

          {/* Statistics Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Statistics</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Attempts</Text>
                <Text style={styles.statValue}>
                  {mockQuizData.statistics.attempts}
                </Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statLabel}>Average Score</Text>
                <Text style={styles.statValue}>
                  {mockQuizData.statistics.averageScore}
                </Text>
              </View>
            </View>
          </View>

          {/* Leaderboard Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Leaderboard</Text>
              <TouchableOpacity>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>
            <View style={styles.leaderboardList}>
              {mockQuizData.leaderboard.map(player => (
                <View key={player.id} style={styles.leaderboardItem}>
                  <Text
                    style={[
                      styles.rankText,
                      {color: getRankColor(player.rank)},
                    ]}>
                    {player.rank}
                  </Text>
                  <Image
                    source={{uri: player.avatar}}
                    style={styles.playerAvatar}
                  />
                  <View style={styles.playerInfo}>
                    <Text style={styles.playerName}>{player.name}</Text>
                    <Text style={styles.playerScore}>
                      Score: {player.score}
                    </Text>
                  </View>
                  {player.rank === 1 && (
                    <Icon name="military-tech" size={24} color="#EAB308" />
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Similar Quizzes Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Similar Quizzes</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={styles.similarQuizList}>
              {mockQuizData.similarQuizzes.map(quiz => (
                <View key={quiz.id} style={styles.similarQuizCard}>
                  <Image
                    source={{uri: quiz.image}}
                    style={styles.similarQuizImage}
                  />
                  <Text style={styles.similarQuizTitle} numberOfLines={1}>
                    {quiz.title}
                  </Text>
                </View>
              ))}
            </ScrollView>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Bar - Mobile design'dan birebir */}
      <View style={styles.bottomBar}>
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.startButton}>
            <Text style={styles.startButtonText}>Start Quiz</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.challengeButton}>
            <Text style={styles.challengeButtonText}>Challenge</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#f9f9fb', // bg-[#f9f9fb]
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF', // bg-white
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111018', // text-[#111018]
    textAlign: 'center',
    flex: 1,
    paddingRight: 40, // pr-10 equivalent
    fontFamily: 'Lexend',
  },
  scrollView: {
    flex: 1,
  },
  // Hero Section - Mobile design'dan birebir
  heroContainer: {
    position: 'relative',
    minHeight: 250, // min-h-[250px]
  },
  heroImage: {
    width: '100%',
    height: 250,
    justifyContent: 'flex-end',
  },
  heroOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)', // bg-gradient-to-t from-black/60 to-transparent
  },
  heroContent: {
    padding: 16, // p-4
    zIndex: 1,
  },
  heroTitle: {
    fontSize: 24, // text-2xl
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
  heroMeta: {
    fontSize: 14,
    color: '#FFFFFF',
    marginTop: 4, // pt-1
    fontFamily: 'Lexend',
  },
  content: {
    padding: 16, // p-4
    gap: 24, // space-y-6
  },
  // Description - Mobile design'dan birebir
  description: {
    fontSize: 16,
    color: '#5e5c8a', // text-[#5e5c8a]
    lineHeight: 24, // leading-relaxed
    fontFamily: 'Lexend',
  },
  // Section - Mobile design'dan birebir
  section: {
    gap: 12, // mb-3
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12, // mb-3
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#111018', // text-[#111018]
    fontFamily: 'Lexend',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#413bb0', // text-[var(--primary-color)]
    fontFamily: 'Lexend',
  },
  // Statistics Grid - Mobile design'dan birebir
  statsGrid: {
    flexDirection: 'row',
    gap: 16, // gap-4
  },
  statCard: {
    flex: 1,
    flexDirection: 'column',
    gap: 8, // gap-2
    borderRadius: 12, // rounded-xl
    padding: 16, // p-4
    backgroundColor: '#FFFFFF', // bg-white
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(229, 231, 235, 0.8)', // border-gray-200/80
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#5e5c8a', // text-[#5e5c8a]
    fontFamily: 'Lexend',
  },
  statValue: {
    fontSize: 30, // text-3xl
    fontWeight: 'bold',
    color: '#111018', // text-[#111018]
    fontFamily: 'Lexend',
  },
  // Leaderboard - Mobile design'dan birebir
  leaderboardList: {
    gap: 12, // space-y-3
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16, // gap-4
    backgroundColor: '#FFFFFF', // bg-white
    padding: 12, // p-3
    borderRadius: 12, // rounded-xl
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: 'rgba(229, 231, 235, 0.8)', // border-gray-200/80
  },
  rankText: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Lexend',
  },
  playerAvatar: {
    width: 48, // h-12 w-12
    height: 48,
    borderRadius: 24,
  },
  playerInfo: {
    flex: 1, // flex-grow
  },
  playerName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111018', // text-[#111018]
    fontFamily: 'Lexend',
  },
  playerScore: {
    fontSize: 14,
    color: '#5e5c8a', // text-[#5e5c8a]
    fontFamily: 'Lexend',
  },
  // Similar Quizzes - Mobile design'dan birebir
  similarQuizList: {
    marginHorizontal: -16, // -mx-4
    paddingHorizontal: 16, // px-4
  },
  similarQuizCard: {
    width: 160, // w-40
    marginRight: 16, // gap-4
    gap: 8, // gap-2
  },
  similarQuizImage: {
    width: '100%',
    aspectRatio: 1, // aspect-w-1 aspect-h-1
    borderRadius: 8, // rounded-lg
  },
  similarQuizTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111018', // text-[#111018]
    fontFamily: 'Lexend',
  },
  // Bottom Action Bar - Mobile design'dan birebir
  bottomBar: {
    backgroundColor: '#FFFFFF', // bg-white
    shadowColor: '#000',
    shadowOffset: {width: 0, height: -2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: 16, // px-4
    paddingVertical: 12, // py-3
    gap: 12, // gap-3
  },
  startButton: {
    flex: 1,
    height: 48, // h-12
    backgroundColor: '#413bb0', // bg-[var(--primary-color)]
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12, // rounded-xl
    shadowColor: '#4F46E5',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  startButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Lexend',
  },
  challengeButton: {
    height: 48, // h-12
    paddingHorizontal: 16, // px-4
    backgroundColor: '#eaeaf1', // bg-[#eaeaf1]
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12, // rounded-xl
  },
  challengeButtonText: {
    color: '#111018', // text-[#111018]
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Lexend',
  },
});
