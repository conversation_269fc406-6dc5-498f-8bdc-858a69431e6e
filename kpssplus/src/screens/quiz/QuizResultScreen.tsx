import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';
import Svg, {Circle} from 'react-native-svg';

// Mock data - mobile design'dan <PERSON><PERSON>nan
const mockResultData = {
  score: 75, // percentage
  correct: 15,
  incorrect: 5,
  timeSpent: '25m',
  questions: [
    {id: 1, title: 'Question 1', isCorrect: true},
    {id: 2, title: 'Question 2', isCorrect: false},
    {id: 3, title: 'Question 3', isCorrect: true},
    {id: 4, title: 'Question 4', isCorrect: true},
    {id: 5, title: 'Question 5', isCorrect: false},
  ],
};

export const QuizResultScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleClose = () => {
    navigation.goBack();
  };

  const handleReviewSolutions = () => {
    // Navigate to solutions review
    console.log('Review solutions');
  };

  const handleShare = () => {
    // Handle share functionality
    console.log('Share score');
  };

  const handleGoToLeaderboard = () => {
    // Navigate to leaderboard
    console.log('Go to leaderboard');
  };

  // Calculate circle progress for SVG
  const radius = 45;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset =
    circumference - (mockResultData.score / 100) * circumference;

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
          <Icon name="close" size={24} color="#0e0d1b" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quiz Result</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          {/* Score Circle - Mobile design'dan birebir */}
          <View style={styles.scoreSection}>
            <View style={styles.circleContainer}>
              <Svg width={192} height={192} viewBox="0 0 100 100">
                {/* Background circle */}
                <Circle
                  cx="50"
                  cy="50"
                  r={radius}
                  stroke="#e8e7f3"
                  strokeWidth="10"
                  fill="transparent"
                />
                {/* Progress circle */}
                <Circle
                  cx="50"
                  cy="50"
                  r={radius}
                  stroke="#1e13ec"
                  strokeWidth="10"
                  fill="transparent"
                  strokeDasharray={circumference}
                  strokeDashoffset={strokeDashoffset}
                  strokeLinecap="round"
                  transform="rotate(-90 50 50)"
                />
              </Svg>
              <View style={styles.scoreOverlay}>
                <Text style={styles.scoreText}>{mockResultData.score}%</Text>
                <Text style={styles.scoreLabel}>Score</Text>
              </View>
            </View>

            {/* Stats Grid - Mobile design'dan birebir */}
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statValueCorrect}>
                  {mockResultData.correct}
                </Text>
                <Text style={styles.statLabel}>Correct</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValueIncorrect}>
                  {mockResultData.incorrect}
                </Text>
                <Text style={styles.statLabel}>Incorrect</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValueTime}>
                  {mockResultData.timeSpent}
                </Text>
                <Text style={styles.statLabel}>Time</Text>
              </View>
            </View>
          </View>

          {/* Questions List - Mobile design'dan birebir */}
          <View style={styles.questionsSection}>
            {mockResultData.questions.map(question => (
              <View key={question.id} style={styles.questionItem}>
                <Text style={styles.questionTitle}>{question.title}</Text>
                <Icon
                  name={question.isCorrect ? 'check-circle' : 'cancel'}
                  size={24}
                  color={question.isCorrect ? '#10B981' : '#EF4444'}
                />
              </View>
            ))}
          </View>

          {/* Review Solutions Button - Mobile design'dan birebir */}
          <TouchableOpacity
            style={styles.reviewButton}
            onPress={handleReviewSolutions}>
            <Text style={styles.reviewButtonText}>Review Solutions</Text>
          </TouchableOpacity>

          {/* Share Section - Mobile design'dan birebir */}
          <View style={styles.shareSection}>
            <View style={styles.shareContent}>
              <Text style={styles.shareTitle}>Share your score</Text>
              <Text style={styles.shareSubtitle}>
                Let your friends know how you did!
              </Text>
            </View>
            <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
              <Icon name="share" size={20} color="#0e0d1b" />
              <Text style={styles.shareButtonText}>Share</Text>
            </TouchableOpacity>
          </View>

          {/* Leaderboard Button - Mobile design'dan birebir */}
          <TouchableOpacity
            style={styles.leaderboardButton}
            onPress={handleGoToLeaderboard}>
            <Text style={styles.leaderboardButtonText}>Go to Leaderboard</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Bottom Navigation - Mobile design'dan birebir */}
      <View style={styles.bottomNav}>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="home" size={24} color="#504c9a" />
          <Text style={styles.navLabel}>Home</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="leaderboard" size={24} color="#504c9a" />
          <Text style={styles.navLabel}>Leaderboard</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItemActive}>
          <View style={styles.activeNavIcon}>
            <Icon name="science" size={30} color="#FFFFFF" />
          </View>
          <Text style={styles.navLabelActive}>Test</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="article" size={24} color="#504c9a" />
          <Text style={styles.navLabel}>Mock Exams</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.navItem}>
          <Icon name="person" size={24} color="#504c9a" />
          <Text style={styles.navLabel}>Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  // Container - Mobile design'dan birebir
  container: {
    flex: 1,
    backgroundColor: '#f8f8fc', // bg-[var(--background-color)]
  },
  // Header - Mobile design'dan birebir
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 8,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[var(--text-color)]
    textAlign: 'center',
    flex: 1,
    paddingRight: 40,
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  // Score Section - Mobile design'dan birebir
  scoreSection: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  circleContainer: {
    position: 'relative',
    width: 192,
    height: 192,
    marginBottom: 24,
  },
  scoreOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scoreText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[var(--text-color)]
    fontFamily: 'Lexend',
  },
  scoreLabel: {
    fontSize: 14,
    color: '#504c9a', // text-[var(--secondary-color)]
    fontFamily: 'Lexend',
  },
  // Stats Grid - Mobile design'dan birebir
  statsGrid: {
    flexDirection: 'row',
    width: '100%',
    gap: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValueCorrect: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#10B981', // text-green-500
    fontFamily: 'Lexend',
  },
  statValueIncorrect: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#EF4444', // text-red-500
    fontFamily: 'Lexend',
  },
  statValueTime: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[var(--text-color)]
    fontFamily: 'Lexend',
  },
  statLabel: {
    fontSize: 14,
    color: '#504c9a', // text-[var(--secondary-color)]
    fontFamily: 'Lexend',
  },
  // Questions Section - Mobile design'dan birebir
  questionsSection: {
    gap: 8,
    marginBottom: 24,
  },
  questionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF', // bg-white
    padding: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  questionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0e0d1b', // text-[var(--text-color)]
    fontFamily: 'Lexend',
  },
  // Review Button - Mobile design'dan birebir
  reviewButton: {
    height: 48,
    backgroundColor: '#1e13ec', // bg-[var(--primary-color)]
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginBottom: 24,
  },
  reviewButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
  // Share Section - Mobile design'dan birebir
  shareSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF', // bg-white
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  shareContent: {
    flex: 1,
  },
  shareTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0e0d1b', // text-[var(--text-color)]
    fontFamily: 'Lexend',
  },
  shareSubtitle: {
    fontSize: 14,
    color: '#504c9a', // text-[var(--secondary-color)]
    fontFamily: 'Lexend',
  },
  shareButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    height: 40,
    paddingHorizontal: 16,
    backgroundColor: '#e8e7f3', // bg-[var(--border-color)]
    borderRadius: 8,
  },
  shareButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0e0d1b', // text-[var(--text-color)]
    fontFamily: 'Lexend',
  },
  // Leaderboard Button - Mobile design'dan birebir
  leaderboardButton: {
    height: 48,
    borderWidth: 2,
    borderColor: '#1e13ec', // border-[var(--primary-color)]
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  leaderboardButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e13ec', // text-[var(--primary-color)]
    fontFamily: 'Lexend',
  },
  // Bottom Navigation - Mobile design'dan birebir
  bottomNav: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    backgroundColor: '#f8f8fc', // bg-[var(--background-color)]
    borderTopWidth: 1,
    borderTopColor: '#e8e7f3', // border-[var(--border-color)]
    paddingTop: 8,
    paddingBottom: 12,
  },
  navItem: {
    alignItems: 'center',
    gap: 4,
  },
  navItemActive: {
    alignItems: 'center',
    gap: 4,
  },
  activeNavIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: '#1e13ec', // bg-[var(--primary-color)]
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: -32,
  },
  navLabel: {
    fontSize: 12,
    fontWeight: '500',
    color: '#504c9a', // text-[var(--secondary-color)]
    fontFamily: 'Lexend',
  },
  navLabelActive: {
    fontSize: 12,
    fontWeight: '500',
    color: '#1e13ec', // text-[var(--primary-color)]
    marginTop: 4,
    fontFamily: 'Lexend',
  },
});
