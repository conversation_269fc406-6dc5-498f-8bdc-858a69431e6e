import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {colors} from '../../theme';
import Icon from 'react-native-vector-icons/Ionicons';
import {quizService} from '../../services/api/quizService';
import {Quiz} from '../../services/api/types';

interface QuizQuestion {
  id: string;
  quiz_id: string;
  question_text: string;
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank';
  options: Array<{
    id: string;
    option_text: string;
    is_correct: boolean;
  }>;
  correct_answer: string;
  explanation?: string;
  points: number;
  order_index: number;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  subject: string;
  time_limit: number;
}

interface Props {
  route?: {
    params?: {
      quizId?: string;
    };
  };
  navigation?: any;
}

export const AssistantQuizScreen2: React.FC<Props> = ({route, navigation}) => {
  const quizId = route?.params?.quizId;
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [_quiz, setQuiz] = useState<Quiz | null>(null);
  const [questions, setQuestions] = useState<QuizQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [score, setScore] = useState(0);

  const question = questions[currentQuestion - 1] || {
    id: '2',
    question_text: "Aşağıdakilerden hangisi Türkiye'nin en büyük gölüdür?",
    options: [
      {id: '1', option_text: 'Van Gölü', is_correct: true},
      {id: '2', option_text: 'Tuz Gölü', is_correct: false},
      {id: '3', option_text: 'Beyşehir Gölü', is_correct: false},
      {id: '4', option_text: 'Eğirdir Gölü', is_correct: false},
    ],
    difficulty_level: 'intermediate',
    subject: 'Coğrafya',
    explanation:
      "Van Gölü, Türkiye'nin en büyük gölüdür ve aynı zamanda dünyanın en büyük soda göllerinden biridir.",
    time_limit: 60,
  };

  const loadQuizData = async () => {
    if (!quizId) {
      Alert.alert('Hata', 'Quiz ID bulunamadı.');
      navigation?.goBack();
      return;
    }

    try {
      setIsLoading(true);
      const [_quizData, questionsData] = await Promise.all([
        quizService.getQuizById(quizId),
        quizService.getQuizQuestions(quizId),
      ]);

      setQuiz(_quizData);
      setQuestions(questionsData as unknown as QuizQuestion[]);
    } catch (error: any) {
      console.error('Error loading quiz data:', error);
      Alert.alert(
        'Hata',
        error.message || 'Quiz verileri yüklenirken bir hata oluştu.',
      );
      navigation?.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadQuizData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quizId]);

  const handleAnswerSelect = (answerIndex: number) => {
    if (showResult) {
      return;
    }
    setSelectedAnswer(answerIndex);
  };

  const handleSubmitAnswer = () => {
    if (selectedAnswer === null) {
      return;
    }

    setShowResult(true);
    const correctAnswerIndex = question.options.findIndex(
      option => option.is_correct,
    );
    if (selectedAnswer === correctAnswerIndex) {
      setScore(prev => prev + 10);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestion < questions.length) {
      setCurrentQuestion(prev => prev + 1);
      setSelectedAnswer(null);
      setShowResult(false);
    }
  };

  const handleHint = () => {
    // Show hint logic
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Quiz yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => navigation?.goBack()}>
          <Icon name="close" size={24} color="#71717A" />
        </TouchableOpacity>
        <View style={styles.scoreContainer}>
          <Text style={styles.scoreText}>{score} Puan</Text>
        </View>
        <TouchableOpacity style={styles.hintButton} onPress={handleHint}>
          <Icon name="bulb" size={20} color="#F59E0B" />
        </TouchableOpacity>
      </View>

      {/* Progress */}
      <View style={styles.progressContainer}>
        <Text style={styles.progressText}>
          Soru {currentQuestion} / {questions.length || 10}
        </Text>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              {width: `${(currentQuestion / (questions.length || 10)) * 100}%`},
            ]}
          />
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Question */}
        <View style={styles.questionContainer}>
          <Text style={styles.questionText}>{question.question_text}</Text>
        </View>

        {/* Options */}
        <View style={styles.optionsContainer}>
          {question.options.map((option, index) => {
            let optionStyle: any = styles.optionButton;
            let textStyle: any = styles.optionText;
            let letterStyle: any = styles.optionLetter;

            const correctAnswerIndex = question.options.findIndex(
              opt => opt.is_correct,
            );

            if (showResult) {
              if (index === correctAnswerIndex) {
                optionStyle = [styles.optionButton, styles.optionButtonCorrect];
                textStyle = [styles.optionText, styles.optionTextCorrect];
                letterStyle = [styles.optionLetter, styles.optionLetterCorrect];
              } else if (
                index === selectedAnswer &&
                selectedAnswer !== correctAnswerIndex
              ) {
                optionStyle = [styles.optionButton, styles.optionButtonWrong];
                textStyle = [styles.optionText, styles.optionTextWrong];
                letterStyle = [styles.optionLetter, styles.optionLetterWrong];
              }
            } else if (selectedAnswer === index) {
              optionStyle = [styles.optionButton, styles.optionButtonSelected];
              textStyle = [styles.optionText, styles.optionTextSelected];
              letterStyle = [styles.optionLetter, styles.optionLetterSelected];
            }

            return (
              <TouchableOpacity
                key={option.id}
                style={optionStyle}
                onPress={() => handleAnswerSelect(index)}
                disabled={showResult}>
                <View style={letterStyle}>
                  <Text style={styles.optionLetterText}>
                    {String.fromCharCode(65 + index)}
                  </Text>
                </View>
                <Text style={textStyle}>{option.option_text}</Text>
                {showResult && index === correctAnswerIndex && (
                  <Icon name="checkmark-circle" size={24} color="#10B981" />
                )}
                {showResult &&
                  index === selectedAnswer &&
                  selectedAnswer !== correctAnswerIndex && (
                    <Icon name="close-circle" size={24} color="#EF4444" />
                  )}
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Explanation */}
        {showResult && (
          <View style={styles.explanationContainer}>
            <View style={styles.explanationHeader}>
              <Icon name="information-circle" size={20} color="#1E13EC" />
              <Text style={styles.explanationTitle}>Açıklama</Text>
            </View>
            <Text style={styles.explanationText}>{question.explanation}</Text>
          </View>
        )}
      </ScrollView>

      {/* Action Button */}
      <View style={styles.actionContainer}>
        {!showResult ? (
          <TouchableOpacity
            style={[
              styles.actionButton,
              selectedAnswer === null && styles.actionButtonDisabled,
            ]}
            onPress={handleSubmitAnswer}
            disabled={selectedAnswer === null}>
            <Text style={styles.actionButtonText}>Cevapla</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleNextQuestion}>
            <Text style={styles.actionButtonText}>
              {currentQuestion < questions.length
                ? 'Sonraki Soru'
                : 'Testi Bitir'}
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreContainer: {
    backgroundColor: '#1E13EC',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  scoreText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white,
  },
  hintButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#FEF3C7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  progressText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0E0D1B',
    textAlign: 'center',
    marginBottom: 12,
  },
  progressBar: {
    width: '100%',
    height: 6,
    backgroundColor: '#E5E7EB',
    borderRadius: 3,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#1E13EC',
    borderRadius: 3,
  },
  scrollView: {
    flex: 1,
  },
  questionContainer: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  questionText: {
    fontSize: 22,
    fontWeight: '600',
    color: '#0E0D1B',
    lineHeight: 32,
    textAlign: 'center',
  },
  optionsContainer: {
    paddingHorizontal: 24,
    gap: 16,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: colors.white,
    gap: 12,
  },
  optionButtonSelected: {
    borderColor: '#1E13EC',
    backgroundColor: '#EEF2FF',
  },
  optionButtonCorrect: {
    borderColor: '#10B981',
    backgroundColor: '#ECFDF5',
  },
  optionButtonWrong: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  optionLetter: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
  },
  optionLetterSelected: {
    backgroundColor: '#1E13EC',
  },
  optionLetterCorrect: {
    backgroundColor: '#10B981',
  },
  optionLetterWrong: {
    backgroundColor: '#EF4444',
  },
  optionLetterText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6B7280',
  },
  optionText: {
    flex: 1,
    fontSize: 16,
    color: '#0E0D1B',
    lineHeight: 24,
  },
  optionTextSelected: {
    color: '#1E13EC',
    fontWeight: '500',
  },
  optionTextCorrect: {
    color: '#10B981',
    fontWeight: '500',
  },
  optionTextWrong: {
    color: '#EF4444',
    fontWeight: '500',
  },
  explanationContainer: {
    margin: 24,
    padding: 16,
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    borderLeftWidth: 4,
    borderLeftColor: '#1E13EC',
  },
  explanationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  explanationTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E13EC',
  },
  explanationText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  actionContainer: {
    padding: 24,
  },
  actionButton: {
    backgroundColor: '#1E13EC',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  actionButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.white,
  },
});
