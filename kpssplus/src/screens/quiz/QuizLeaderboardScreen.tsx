import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, spacing} from '../../theme';
import {quizService} from '../../services/api/quizService';
import {QuizLeaderboard} from '../../services/api/types';

interface Props {
  navigation: any;
  route: any;
}

export const QuizLeaderboardScreen: React.FC<Props> = ({navigation, route}) => {
  const {quizId} = route.params as {quizId: string};
  const [leaderboard, setLeaderboard] = useState<QuizLeaderboard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadLeaderboard = async (refresh = false) => {
    try {
      if (refresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const response = await quizService.getQuizLeaderboard(quizId);
      setLeaderboard(response.data);
    } catch (err) {
      console.error('Error loading leaderboard:', err);
      setError('Liderlik tablosu yüklenirken bir hata oluştu');

      // Fallback to mock data
      const mockLeaderboard: QuizLeaderboard[] = [
        {
          id: '1',
          user_id: '1',
          quiz_id: quizId,
          user: {
            id: '1',
            username: 'ahmet_kpss',
            name: 'Ahmet Yılmaz',
            first_name: 'Ahmet',
            last_name: 'Yılmaz',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: true,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          score: 95,
          completion_time: 1200,
          rank: 1,
          completed_at: '2024-01-15T10:30:00Z',
        },
        {
          id: '2',
          user_id: '2',
          quiz_id: quizId,
          user: {
            id: '2',
            username: 'fatma_study',
            name: 'Fatma Demir',
            first_name: 'Fatma',
            last_name: 'Demir',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: false,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          score: 88,
          completion_time: 1350,
          rank: 2,
          completed_at: '2024-01-15T11:15:00Z',
        },
        {
          id: '3',
          user_id: '3',
          quiz_id: quizId,
          user: {
            id: '3',
            username: 'mehmet_pro',
            name: 'Mehmet Kaya',
            first_name: 'Mehmet',
            last_name: 'Kaya',
            email: '<EMAIL>',
            avatar_url: undefined,
            is_verified: true,
            is_active: true,
            is_admin: false,
            push_notification_enabled: true,
            email_notification_enabled: true,
            created_at: '2024-01-01T00:00:00Z',
            updated_at: '2024-01-01T00:00:00Z',
          },
          score: 82,
          completion_time: 1450,
          rank: 3,
          completed_at: '2024-01-15T12:00:00Z',
        },
      ];
      setLeaderboard(mockLeaderboard);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    loadLeaderboard();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [quizId]);

  const onRefresh = () => {
    loadLeaderboard(true);
  };

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return '🥇';
      case 2:
        return '🥈';
      case 3:
        return '🥉';
      default:
        return `${rank}`;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return '#FFD700';
      case 2:
        return '#C0C0C0';
      case 3:
        return '#CD7F32';
      default:
        return colors.text.secondary;
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const renderLeaderboardItem = ({item}: {item: QuizLeaderboard}) => (
    <TouchableOpacity style={styles.leaderboardItem}>
      <View style={styles.rankContainer}>
        <Text style={[styles.rankText, {color: getRankColor(item.rank)}]}>
          {getRankIcon(item.rank)}
        </Text>
      </View>

      <View style={styles.avatarContainer}>
        {item.user.avatar_url ? (
          <Image source={{uri: item.user.avatar_url}} style={styles.avatar} />
        ) : (
          <View style={styles.avatarPlaceholder}>
            <Text style={styles.avatarText}>
              {item.user.first_name?.[0] || item.user.username[0]}
            </Text>
          </View>
        )}
        {item.user.is_verified && (
          <Icon
            name="checkmark-circle"
            size={16}
            color={colors.primary}
            style={styles.verifiedIcon}
          />
        )}
      </View>

      <View style={styles.userInfo}>
        <Text style={styles.userName}>
          {item.user.first_name && item.user.last_name
            ? `${item.user.first_name} ${item.user.last_name}`
            : item.user.username}
        </Text>
        <Text style={styles.userStats}>
          {formatTime(item.completion_time)} • {item.score} puan
        </Text>
      </View>

      <View style={styles.scoreContainer}>
        <Text style={styles.scoreText}>{item.score}</Text>
        <Text style={styles.scoreLabel}>puan</Text>
      </View>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={colors.text.primary} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Liderlik Tablosu</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Liderlik tablosu yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Liderlik Tablosu</Text>
        <View style={styles.placeholder} />
      </View>

      {error && (
        <View style={styles.errorContainer}>
          <Icon name="warning-outline" size={24} color={colors.error} />
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}

      <FlatList
        data={leaderboard}
        keyExtractor={item => item.id}
        renderItem={renderLeaderboardItem}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Icon
              name="trophy-outline"
              size={64}
              color={colors.text.secondary}
            />
            <Text style={styles.emptyTitle}>Henüz Sonuç Yok</Text>
            <Text style={styles.emptyDescription}>
              Bu quiz için henüz kimse sonuç kaydetmemiş.
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h3,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginTop: spacing[3],
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.error + '20',
    padding: spacing[3],
    marginHorizontal: spacing[4],
    marginVertical: spacing[2],
    borderRadius: 8,
  },
  errorText: {
    ...typography.body2,
    color: colors.error,
    marginLeft: spacing[2],
    flex: 1,
  },
  listContainer: {
    padding: spacing[4],
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    padding: spacing[4],
    marginBottom: spacing[3],
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  rankContainer: {
    width: 40,
    alignItems: 'center',
  },
  rankText: {
    ...typography.h3,
    fontWeight: 'bold',
  },
  avatarContainer: {
    position: 'relative',
    marginLeft: spacing[3],
  },
  avatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
  },
  avatarPlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    ...typography.h3,
    color: colors.surface,
    fontWeight: 'bold',
  },
  verifiedIcon: {
    position: 'absolute',
    bottom: -2,
    right: -2,
    backgroundColor: colors.surface,
    borderRadius: 8,
  },
  userInfo: {
    flex: 1,
    marginLeft: spacing[3],
  },
  userName: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
  },
  userStats: {
    ...typography.body2,
    color: colors.text.secondary,
    marginTop: spacing[1],
  },
  scoreContainer: {
    alignItems: 'center',
  },
  scoreText: {
    ...typography.h3,
    color: colors.primary,
    fontWeight: 'bold',
  },
  scoreLabel: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    ...typography.h3,
    color: colors.text.primary,
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});
