import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Image,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {colors, typography, spacing} from '../../theme';
import {quizService} from '../../services/api/quizService';
import {Quiz, QuizCategory} from '../../services/api/types';

interface Props {
  navigation: any;
}

export const QuizSearchScreen: React.FC<Props> = ({navigation}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [categories, setCategories] = useState<QuizCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [_isLoading, _setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    if (searchQuery.trim().length > 2) {
      searchQuizzes();
    } else {
      setQuizzes([]);
      setIsSearching(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchQuery, selectedCategory]);

  const loadCategories = async () => {
    try {
      const categoriesData = await quizService.getQuizCategories();
      setCategories(categoriesData);
    } catch (error) {
      console.error('Error loading categories:', error);
      // Fallback to mock categories
      const mockCategories: QuizCategory[] = [
        {
          id: '1',
          name: 'Genel Kültür',
          description: 'Genel kültür soruları',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Matematik',
          description: 'Matematik soruları',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: '3',
          name: 'Türkçe',
          description: 'Türkçe soruları',
          is_active: true,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ];
      setCategories(mockCategories);
    }
  };

  const searchQuizzes = async () => {
    try {
      setIsSearching(true);
      const filters = {
        search: searchQuery,
        category_id: selectedCategory,
      };

      const response = await quizService.getQuizzes(1, 20, filters);
      setQuizzes(response.data);
    } catch (error) {
      console.error('Error searching quizzes:', error);
      // Fallback to mock data
      const mockQuizzes: Quiz[] = [
        {
          id: '1',
          title: 'KPSS Genel Kültür Testi',
          description: 'KPSS sınavına hazırlık için genel kültür soruları',
          subject: 'Genel Kültür',
          difficulty_level: 'intermediate' as const,
          difficulty: 'medium' as const,
          question_count: 20,
          time_limit: 1800,
          duration: 30,
          points: 100,
          category_id: '1',
          thumbnail_url: undefined,
          is_public: true,
          is_active: true,
          created_by: 'admin',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: '2',
          title: 'Matematik Temel Kavramlar',
          description: 'Temel matematik kavramları ve işlemler',
          subject: 'Matematik',
          difficulty_level: 'beginner' as const,
          difficulty: 'easy' as const,
          question_count: 15,
          time_limit: 1200,
          duration: 20,
          points: 75,
          category_id: '2',
          thumbnail_url: undefined,
          is_public: true,
          is_active: true,
          created_by: 'admin',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ].filter(
        quiz =>
          quiz.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          quiz.subject.toLowerCase().includes(searchQuery.toLowerCase()),
      );
      setQuizzes(mockQuizzes);
    } finally {
      setIsSearching(false);
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return '#10B981';
      case 'medium':
        return '#F59E0B';
      case 'hard':
        return '#EF4444';
      default:
        return colors.text.secondary;
    }
  };

  const getDifficultyText = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy':
        return 'Kolay';
      case 'medium':
        return 'Orta';
      case 'hard':
        return 'Zor';
      default:
        return 'Bilinmiyor';
    }
  };

  const renderCategoryItem = ({item}: {item: QuizCategory}) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory === item.id && styles.categoryItemSelected,
      ]}
      onPress={() =>
        setSelectedCategory(selectedCategory === item.id ? null : item.id)
      }>
      <Text
        style={[
          styles.categoryText,
          selectedCategory === item.id && styles.categoryTextSelected,
        ]}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderQuizItem = ({item}: {item: Quiz}) => (
    <TouchableOpacity
      style={styles.quizItem}
      onPress={() => navigation.navigate('QuizDetail', {quizId: item.id})}>
      <View style={styles.quizHeader}>
        {item.thumbnail_url ? (
          <Image source={{uri: item.thumbnail_url}} style={styles.quizImage} />
        ) : (
          <View style={styles.quizImagePlaceholder}>
            <Icon name="school-outline" size={24} color={colors.primary} />
          </View>
        )}
        <View style={styles.quizInfo}>
          <Text style={styles.quizTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.quizSubject}>{item.subject}</Text>
        </View>
      </View>

      <Text style={styles.quizDescription} numberOfLines={2}>
        {item.description}
      </Text>

      <View style={styles.quizStats}>
        <View style={styles.statItem}>
          <Icon
            name="help-circle-outline"
            size={16}
            color={colors.text.secondary}
          />
          <Text style={styles.statText}>{item.question_count} soru</Text>
        </View>
        <View style={styles.statItem}>
          <Icon name="time-outline" size={16} color={colors.text.secondary} />
          <Text style={styles.statText}>{item.duration || 30} dk</Text>
        </View>
        <View style={styles.statItem}>
          <View
            style={[
              styles.difficultyBadge,
              {backgroundColor: getDifficultyColor(item.difficulty)},
            ]}>
            <Text style={styles.difficultyText}>
              {getDifficultyText(item.difficulty)}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Quiz Ara</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Icon name="search" size={20} color={colors.text.secondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Quiz ara..."
            placeholderTextColor={colors.text.secondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoCapitalize="none"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Icon
                name="close-circle"
                size={20}
                color={colors.text.secondary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {categories.length > 0 && (
        <View style={styles.categoriesContainer}>
          <Text style={styles.categoriesTitle}>Kategoriler</Text>
          <FlatList
            data={categories}
            keyExtractor={item => item.id}
            renderItem={renderCategoryItem}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>
      )}

      {isSearching ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Quizler aranıyor...</Text>
        </View>
      ) : (
        <FlatList
          data={quizzes}
          keyExtractor={item => item.id}
          renderItem={renderQuizItem}
          contentContainerStyle={styles.listContainer}
          ListEmptyComponent={
            searchQuery.trim().length > 2 ? (
              <View style={styles.emptyContainer}>
                <Icon
                  name="search-outline"
                  size={64}
                  color={colors.text.secondary}
                />
                <Text style={styles.emptyTitle}>Sonuç Bulunamadı</Text>
                <Text style={styles.emptyDescription}>
                  "{searchQuery}" için quiz bulunamadı. Farklı anahtar kelimeler
                  deneyin.
                </Text>
              </View>
            ) : (
              <View style={styles.emptyContainer}>
                <Icon
                  name="search-outline"
                  size={64}
                  color={colors.text.secondary}
                />
                <Text style={styles.emptyTitle}>Quiz Ara</Text>
                <Text style={styles.emptyDescription}>
                  Aramak istediğiniz quiz adını yazın.
                </Text>
              </View>
            )
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    padding: spacing[2],
  },
  headerTitle: {
    ...typography.h3,
    color: colors.text.primary,
  },
  placeholder: {
    width: 40,
  },
  searchContainer: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[3],
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: 12,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
  },
  searchInput: {
    flex: 1,
    ...typography.body1,
    color: colors.text.primary,
    marginLeft: spacing[2],
    paddingVertical: spacing[2],
  },
  categoriesContainer: {
    paddingVertical: spacing[3],
  },
  categoriesTitle: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
    paddingHorizontal: spacing[4],
    marginBottom: spacing[2],
  },
  categoriesList: {
    paddingHorizontal: spacing[4],
  },
  categoryItem: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 20,
    marginRight: spacing[2],
    borderWidth: 1,
    borderColor: colors.border,
  },
  categoryItemSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  categoryText: {
    ...typography.body2,
    color: colors.text.primary,
  },
  categoryTextSelected: {
    color: colors.surface,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    ...typography.body1,
    color: colors.text.secondary,
    marginTop: spacing[3],
  },
  listContainer: {
    padding: spacing[4],
  },
  quizItem: {
    backgroundColor: colors.surface,
    padding: spacing[4],
    marginBottom: spacing[3],
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  quizHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  quizImage: {
    width: 48,
    height: 48,
    borderRadius: 8,
  },
  quizImagePlaceholder: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: colors.primary + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quizInfo: {
    flex: 1,
    marginLeft: spacing[3],
  },
  quizTitle: {
    ...typography.body1,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: spacing[1],
  },
  quizSubject: {
    ...typography.body2,
    color: colors.primary,
  },
  quizDescription: {
    ...typography.body2,
    color: colors.text.secondary,
    lineHeight: 20,
    marginBottom: spacing[3],
  },
  quizStats: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  difficultyBadge: {
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 12,
  },
  difficultyText: {
    ...typography.caption,
    color: colors.surface,
    fontWeight: '600',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing[8],
  },
  emptyTitle: {
    ...typography.h3,
    color: colors.text.primary,
    marginTop: spacing[4],
    marginBottom: spacing[2],
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: spacing[4],
  },
});
