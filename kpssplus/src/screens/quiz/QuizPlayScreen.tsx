import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON><PERSON><PERSON>
const mockQuizData = {
  title: 'History Quiz',
  currentQuestion: 1,
  totalQuestions: 20,
  timeRemaining: 25, // seconds
  progress: 5, // percentage
  question:
    "Which of the following is NOT a characteristic of the Ottoman Empire's administrative system?",
  options: [
    'Centralized Bureaucracy',
    'Devshirme System',
    'Autonomous Provinces',
    'Military Feudalism',
  ],
};

export const QuizPlayScreen: React.FC = () => {
  const navigation = useNavigation();
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [timeRemaining, setTimeRemaining] = useState(
    mockQuizData.timeRemaining,
  );
  const [showExitModal, setShowExitModal] = useState(false);

  // Timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          // Auto-submit when time runs out
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const handleOptionSelect = (index: number) => {
    setSelectedOption(index);
  };

  const handleNextQuestion = () => {
    if (selectedOption !== null) {
      // Handle answer submission
      console.log('Selected option:', selectedOption);
      // Navigate to next question or results
    }
  };

  const handleExit = () => {
    setShowExitModal(true);
  };

  const confirmExit = () => {
    setShowExitModal(false);
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity style={styles.closeButton} onPress={handleExit}>
            <Icon name="close" size={24} color="#6B7280" />
          </TouchableOpacity>
          <View style={styles.headerCenter}>
            <Text style={styles.headerTitle}>{mockQuizData.title}</Text>
          </View>
          <View style={styles.headerSpacer} />
        </View>

        {/* Progress Section - Mobile design'dan birebir */}
        <View style={styles.progressSection}>
          <View style={styles.progressInfo}>
            <Text style={styles.questionCounter}>
              Question {mockQuizData.currentQuestion}/
              {mockQuizData.totalQuestions}
            </Text>
            <View style={styles.timerContainer}>
              <Icon name="timer" size={20} color="#5850E6" />
              <Text style={styles.timerText}>{formatTime(timeRemaining)}</Text>
            </View>
          </View>
          <View style={styles.progressBar}>
            <View style={[styles.progressFill, styles.progressWidth]} />
          </View>
        </View>
      </View>

      {/* Main Content - Mobile design'dan birebir */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <Text style={styles.questionText}>{mockQuizData.question}</Text>

          <View style={styles.optionsContainer}>
            {mockQuizData.options.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.optionButton,
                  selectedOption === index && styles.optionButtonSelected,
                ]}
                onPress={() => handleOptionSelect(index)}>
                <View
                  style={[
                    styles.optionCircle,
                    selectedOption === index && styles.optionCircleSelected,
                  ]}>
                  {selectedOption === index && (
                    <Icon name="check" size={18} color="#FFFFFF" />
                  )}
                </View>
                <Text
                  style={[
                    styles.optionText,
                    selectedOption === index && styles.optionTextSelected,
                  ]}>
                  {option}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Bar - Mobile design'dan birebir */}
      <View style={styles.bottomBar}>
        <TouchableOpacity
          style={[
            styles.nextButton,
            selectedOption === null && styles.nextButtonDisabled,
          ]}
          onPress={handleNextQuestion}
          disabled={selectedOption === null}>
          <Text
            style={[
              styles.nextButtonText,
              selectedOption === null && styles.nextButtonTextDisabled,
            ]}>
            Next Question
          </Text>
        </TouchableOpacity>
      </View>

      {/* Exit Confirmation Modal - Mobile design'dan birebir */}
      <Modal
        visible={showExitModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowExitModal(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Are you sure?</Text>
            <Text style={styles.modalText}>
              If you exit now, your progress for this quiz will be lost.
            </Text>
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.stayButton}
                onPress={() => setShowExitModal(false)}>
                <Text style={styles.stayButtonText}>Stay</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.exitButton} onPress={confirmExit}>
                <Text style={styles.exitButtonText}>Exit</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  closeButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 20,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 40,
  },
  progressSection: {
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  questionCounter: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
    fontFamily: 'Lexend',
  },
  timerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  timerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#5850E6',
    fontFamily: 'Lexend',
  },
  progressBar: {
    height: 10,
    borderRadius: 5,
    backgroundColor: '#E5E7EB',
  },
  progressFill: {
    height: 10,
    borderRadius: 5,
    backgroundColor: '#5850E6',
  },
  progressWidth: {
    width: '5%',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 24,
  },
  questionText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 32,
    lineHeight: 32,
    fontFamily: 'Lexend',
  },
  optionsContainer: {
    gap: 16,
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  optionButtonSelected: {
    borderColor: '#5850E6',
    backgroundColor: '#F0F0FF',
  },
  optionCircle: {
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  optionCircleSelected: {
    borderColor: '#5850E6',
    backgroundColor: '#5850E6',
  },
  optionText: {
    flex: 1,
    fontSize: 18,
    color: '#374151',
    fontFamily: 'Lexend',
  },
  optionTextSelected: {
    color: '#374151',
  },
  bottomBar: {
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 24,
  },
  nextButton: {
    height: 56,
    borderRadius: 16,
    backgroundColor: '#5850E6',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#5850E6',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.4,
    shadowRadius: 8,
    elevation: 8,
  },
  nextButtonDisabled: {
    backgroundColor: '#D1D5DB',
    shadowOpacity: 0,
    elevation: 0,
  },
  nextButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
  nextButtonTextDisabled: {
    color: '#9CA3AF',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  modalContent: {
    width: '100%',
    maxWidth: 384,
    borderRadius: 16,
    backgroundColor: '#FFFFFF',
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 10},
    shadowOpacity: 0.25,
    shadowRadius: 20,
    elevation: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    fontFamily: 'Lexend',
  },
  modalText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24,
    fontFamily: 'Lexend',
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 16,
    width: '100%',
  },
  stayButton: {
    flex: 1,
    height: 48,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  stayButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    fontFamily: 'Lexend',
  },
  exitButton: {
    flex: 1,
    height: 48,
    borderRadius: 12,
    backgroundColor: '#DC2626',
    alignItems: 'center',
    justifyContent: 'center',
  },
  exitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
});
