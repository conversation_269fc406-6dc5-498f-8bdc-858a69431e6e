import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Image,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON>
const mockChallengeData = {
  sent: [
    {
      id: 1,
      name: '<PERSON>',
      quiz: 'Quiz: Mathematics Fundamentals',
      status: 'Pending',
      statusColor: '#EAB308',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuA2IP_8BO4WmnGQvKdDupzR7YGJXNuVFmAbYzacj6iCRSTsGrvPpGbGjb-_qWLQXrTQaPOMzbCjqkusFPCcPRAhWn-zuFTfG7xl0I3QlQ_y-_KSniJLRFregq0_6_50SFIebjuC04mvTI3BwQL5nssY2dhA_PKvby6DgYLNKqzxAP3ENKMD3cacDqzvs3jly1iiMsjYwrR8KIMj-o82ixKehdcSVaybY9x211pC8P-bLWcrGNKwpHphiVNj7ufbPviSkKWRvcsoYQk',
    },
    {
      id: 2,
      name: 'Mike Chen',
      quiz: 'Quiz: Turkish Literature',
      status: 'Completed',
      statusColor: '#16A34A',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAUSmGNCsaQTcgJ335YCgwVn08xj8P2IL0XmlrtxXiAs11CjEoB-CMtJ0HpoID0m14sWH-nHfkwMqtfmCov3ac96n1RG3Bjn9jzOIGxoQizel4pzNZCQc6Ri2rXnO78td2hNKXXhIdk46gCh3HqmqbYvBBzW-NNSb3s7u4DDt7l15CeJLNRo2HTN6GIylfBL1-ntODiOWDB7XoQdbhNbqgWUtDOYjXuMi9Ol5pzsnMMYTnM-fmOtKE9dNA7xI6633_iMpGB14paBP4',
    },
  ],
  received: [
    {
      id: 3,
      name: 'Alex',
      quiz: 'Quiz: History of the Ottoman Empire',
      status: 'New',
      statusColor: '#16A34A',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuA2IP_8BO4WmnGQvKdDupzR7YGJXNuVFmAbYzacj6iCRSTsGrvPpGbGjb-_qWLQXrTQaPOMzbCjqkusFPCcPRAhWn-zuFTfG7xl0I3QlQ_y-_KSniJLRFregq0_6_50SFIebjuC04mvTI3BwQL5nssY2dhA_PKvby6DgYLNKqzxAP3ENKMD3cacDqzvs3jly1iiMsjYwrR8KIMj-o82ixKehdcSVaybY9x211pC8P-bLWcrGNKwpHphiVNj7ufbPviSkKWRvcsoYQk',
    },
    {
      id: 4,
      name: 'Jordan',
      quiz: 'Quiz: Geography of Turkey',
      status: 'Expired',
      statusColor: '#DC2626',
      avatar:
        'https://lh3.googleusercontent.com/aida-public/AB6AXuAUSmGNCsaQTcgJ335YCgwVn08xj8P2IL0XmlrtxXiAs11CjEoB-CMtJ0HpoID0m14sWH-nHfkwMqtfmCov3ac96n1RG3Bjn9jzOIGxoQizel4pzNZCQc6Ri2rXnO78td2hNKXXhIdk46gCh3HqmqbYvBBzW-NNSb3s7u4DDt7l15CeJLNRo2HTN6GIylfBL1-ntODiOWDB7XoQdbhNbqgWUtDOYjXuMi9Ol5pzsnMMYTnM-fmOtKE9dNA7xI6633_iMpGB14paBP4',
    },
  ],
};

export const FriendChallengeScreen: React.FC = () => {
  const navigation = useNavigation();
  const [activeTab, setActiveTab] = useState<'sent' | 'received'>('received');

  const handleBack = () => {
    navigation.goBack();
  };

  const handleTabChange = (tab: 'sent' | 'received') => {
    setActiveTab(tab);
  };

  const handleAccept = (challengeId: number) => {
    console.log('Accept challenge:', challengeId);
  };

  const handleDecline = (challengeId: number) => {
    console.log('Decline challenge:', challengeId);
  };

  const handleResend = (challengeId: number) => {
    console.log('Resend challenge:', challengeId);
  };

  const handleViewResults = (challengeId: number) => {
    console.log('View results:', challengeId);
  };

  const currentData =
    activeTab === 'sent' ? mockChallengeData.sent : mockChallengeData.received;

  return (
    <SafeAreaView style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity style={styles.backButton} onPress={handleBack}>
            <Icon name="arrow-back" size={24} color="#0E0D1B" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Friend Challenges</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Tab Navigation - Mobile design'dan birebir */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'sent' && styles.tabActive]}
            onPress={() => handleTabChange('sent')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'sent' && styles.tabTextActive,
              ]}>
              Sent
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'received' && styles.tabActive]}
            onPress={() => handleTabChange('received')}>
            <Text
              style={[
                styles.tabText,
                activeTab === 'received' && styles.tabTextActive,
              ]}>
              Received
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Challenge List - Mobile design'dan birebir */}
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {currentData.map(challenge => (
            <View key={challenge.id} style={styles.challengeCard}>
              <View style={styles.challengeHeader}>
                <View style={styles.challengeInfo}>
                  <View
                    style={[
                      styles.statusBadge,
                      {backgroundColor: getStatusBgColor(challenge.status)},
                    ]}>
                    <Text
                      style={[
                        styles.statusText,
                        {color: challenge.statusColor},
                      ]}>
                      {challenge.status}
                    </Text>
                  </View>
                  <Text style={styles.challengeTitle}>
                    Challenge from {challenge.name}
                  </Text>
                  <Text style={styles.challengeQuiz}>{challenge.quiz}</Text>
                </View>
                <Image source={{uri: challenge.avatar}} style={styles.avatar} />
              </View>

              {/* Action Buttons - Mobile design'dan birebir */}
              <View style={styles.actionButtons}>
                {activeTab === 'received' ? (
                  challenge.status === 'New' ? (
                    <>
                      <TouchableOpacity
                        style={styles.primaryButton}
                        onPress={() => handleAccept(challenge.id)}>
                        <Text style={styles.primaryButtonText}>Accept</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.secondaryButton}
                        onPress={() => handleDecline(challenge.id)}>
                        <Text style={styles.secondaryButtonText}>Decline</Text>
                      </TouchableOpacity>
                    </>
                  ) : (
                    <>
                      <TouchableOpacity
                        style={[styles.secondaryButton, styles.disabledButton]}
                        disabled={true}>
                        <Text style={styles.disabledButtonText}>Resend</Text>
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={styles.secondaryButton}
                        onPress={() => handleViewResults(challenge.id)}>
                        <Text style={styles.secondaryButtonText}>
                          View Results
                        </Text>
                      </TouchableOpacity>
                    </>
                  )
                ) : (
                  <>
                    <TouchableOpacity
                      style={styles.secondaryButton}
                      onPress={() => handleResend(challenge.id)}>
                      <Text style={styles.secondaryButtonText}>Resend</Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={styles.secondaryButton}
                      onPress={() => handleViewResults(challenge.id)}>
                      <Text style={styles.secondaryButtonText}>
                        View Results
                      </Text>
                    </TouchableOpacity>
                  </>
                )}
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const getStatusBgColor = (status: string) => {
  switch (status) {
    case 'New':
      return '#DCFCE7';
    case 'Expired':
      return '#FEE2E2';
    case 'Pending':
      return '#FEF3C7';
    case 'Completed':
      return '#DCFCE7';
    default:
      return '#F3F4F6';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F8FC',
  },
  header: {
    backgroundColor: '#F8F8FC',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  headerTop: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  backButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    textAlign: 'center',
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 32,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E7F3',
    paddingHorizontal: 16,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabActive: {
    borderBottomColor: '#1E13EC',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#504C9A',
    fontFamily: 'Lexend',
  },
  tabTextActive: {
    color: '#1E13EC',
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    gap: 16,
  },
  challengeCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  challengeHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  challengeInfo: {
    flex: 1,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    borderRadius: 20,
    paddingHorizontal: 10,
    paddingVertical: 2,
    marginBottom: 8,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    fontFamily: 'Lexend',
  },
  challengeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 4,
    fontFamily: 'Lexend',
  },
  challengeQuiz: {
    fontSize: 14,
    color: '#504C9A',
    fontFamily: 'Lexend',
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginLeft: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  primaryButton: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1E13EC',
    alignItems: 'center',
    justifyContent: 'center',
  },
  primaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
  secondaryButton: {
    flex: 1,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#504C9A',
    fontFamily: 'Lexend',
  },
  disabledButton: {
    backgroundColor: '#D1D5DB',
  },
  disabledButtonText: {
    color: '#9CA3AF',
  },
});
