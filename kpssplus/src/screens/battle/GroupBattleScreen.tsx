import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import {useNavigation} from '@react-navigation/native';

// Mock data - mobile design'dan <PERSON><PERSON><PERSON>
const mockGroupBattleData = {
  activeBattles: [
    {
      id: 1,
      title: 'Battle of Wits',
      status: 'Waiting for participants',
      statusColor: '#EAB308',
      participants: '2/4',
    },
    {
      id: 2,
      title: 'Knowledge Clash',
      status: 'Active',
      statusColor: '#16A34A',
      participants: '4/4',
    },
  ],
  upcomingBattles: [
    {
      id: 3,
      title: 'Quiz Duel',
      status: 'Waiting for participants',
      statusColor: '#EAB308',
      participants: '1/4',
    },
    {
      id: 4,
      title: 'Trivia Showdown',
      status: 'Waiting for participants',
      statusColor: '#EAB308',
      participants: '0/4',
    },
    {
      id: 5,
      title: 'Mind Challenge',
      status: 'Waiting for participants',
      statusColor: '#EAB308',
      participants: '3/4',
    },
  ],
};

export const GroupBattleScreen: React.FC = () => {
  const navigation = useNavigation();

  const handleBack = () => {
    navigation.goBack();
  };

  const handleBattlePress = (battleId: number) => {
    console.log('Open battle:', battleId);
  };

  const handleCreateBattle = () => {
    console.log('Create new battle');
  };

  return (
    <View style={styles.container}>
      {/* Header - Mobile design'dan birebir */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="arrow-back" size={30} color="#1F2937" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Group Battles</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {/* Active Battles Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Active Battles</Text>
            <View style={styles.battleList}>
              {mockGroupBattleData.activeBattles.map(battle => (
                <TouchableOpacity
                  key={battle.id}
                  style={styles.battleCard}
                  onPress={() => handleBattlePress(battle.id)}>
                  <View style={styles.battleIcon}>
                    <Icon
                      name="sports-martial-arts"
                      size={30}
                      color="#6366F1"
                    />
                  </View>
                  <View style={styles.battleInfo}>
                    <Text style={styles.battleTitle}>{battle.title}</Text>
                    <Text
                      style={[
                        styles.battleStatus,
                        {color: battle.statusColor},
                      ]}>
                      {battle.status}
                    </Text>
                  </View>
                  <View style={styles.participantsContainer}>
                    <Icon name="group" size={20} color="#6B7280" />
                    <Text style={styles.participantsText}>
                      {battle.participants}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Upcoming Battles Section - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Upcoming Battles</Text>
            <View style={styles.battleList}>
              {mockGroupBattleData.upcomingBattles.map(battle => (
                <TouchableOpacity
                  key={battle.id}
                  style={styles.battleCard}
                  onPress={() => handleBattlePress(battle.id)}>
                  <View style={styles.battleIcon}>
                    <Icon
                      name="sports-martial-arts"
                      size={30}
                      color="#6366F1"
                    />
                  </View>
                  <View style={styles.battleInfo}>
                    <Text style={styles.battleTitle}>{battle.title}</Text>
                    <Text
                      style={[
                        styles.battleStatus,
                        {color: battle.statusColor},
                      ]}>
                      {battle.status}
                    </Text>
                  </View>
                  <View style={styles.participantsContainer}>
                    <Icon name="group" size={20} color="#6B7280" />
                    <Text style={styles.participantsText}>
                      {battle.participants}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Button - Mobile design'dan birebir */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={styles.createButton}
          onPress={handleCreateBattle}>
          <Icon name="add" size={24} color="#FFFFFF" />
          <Text style={styles.createButtonText}>Create New Battle</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    flex: 1,
    textAlign: 'center',
    fontFamily: 'Lexend',
  },
  headerSpacer: {
    width: 32,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 24,
    paddingBottom: 100,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 16,
    fontFamily: 'Lexend',
  },
  battleList: {
    gap: 16,
  },
  battleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  battleIcon: {
    width: 56,
    height: 56,
    borderRadius: 12,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  battleInfo: {
    flex: 1,
  },
  battleTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    fontFamily: 'Lexend',
  },
  battleStatus: {
    fontSize: 14,
    marginTop: 2,
    fontFamily: 'Lexend',
  },
  participantsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  participantsText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    fontFamily: 'Lexend',
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    paddingHorizontal: 24,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  createButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#6366F1',
  },
  createButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
});
