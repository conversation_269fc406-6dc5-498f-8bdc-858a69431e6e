import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Mock data - mobile design'dan <PERSON><PERSON><PERSON>
const mockBattleData = {
  stats: {
    totalBattles: '120',
    winRate: '75%',
    averageScore: '88',
    bestRank: '#12',
  },
  activeGroupBattles: [
    {
      id: 1,
      title: 'Math Challenge',
      opponent: 'vs. Team Alpha',
    },
    {
      id: 2,
      title: 'History Quiz',
      opponent: 'vs. Team Beta',
    },
  ],
  friendChallenges: [
    {
      id: 3,
      name: '<PERSON>',
      challenge: 'Geography Quiz',
    },
    {
      id: 4,
      name: '<PERSON>',
      challenge: 'Science Test',
    },
  ],
};

export const BattleHomeScreen: React.FC = () => {
  const handleGroupBattlePress = (battleId: number) => {
    console.log('Open group battle:', battleId);
  };

  const handleFriendChallengePress = (challengeId: number) => {
    console.log('Open friend challenge:', challengeId);
  };

  const handleCreateBattle = () => {
    console.log('Create new battle');
  };

  const handleJoinBattle = () => {
    console.log('Join battle');
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        <View style={styles.contentContainer}>
          {/* Stats Grid - Mobile design'dan birebir */}
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Total Battles</Text>
              <Text style={styles.statValue}>
                {mockBattleData.stats.totalBattles}
              </Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Win Rate</Text>
              <Text style={styles.statValue}>
                {mockBattleData.stats.winRate}
              </Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Average Score</Text>
              <Text style={styles.statValue}>
                {mockBattleData.stats.averageScore}
              </Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statLabel}>Best Rank</Text>
              <Text style={styles.statValue}>
                {mockBattleData.stats.bestRank}
              </Text>
            </View>
          </View>

          {/* Active Group Battles - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Active Group Battles</Text>
            <View style={styles.battleList}>
              {mockBattleData.activeGroupBattles.map(battle => (
                <TouchableOpacity
                  key={battle.id}
                  style={styles.battleCard}
                  onPress={() => handleGroupBattlePress(battle.id)}>
                  <View style={styles.battleIcon}>
                    <Icon name="groups" size={24} color="#71717A" />
                  </View>
                  <View style={styles.battleInfo}>
                    <Text style={styles.battleTitle}>{battle.title}</Text>
                    <Text style={styles.battleOpponent}>{battle.opponent}</Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Friend Challenges - Mobile design'dan birebir */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Friend Challenges</Text>
            <View style={styles.battleList}>
              {mockBattleData.friendChallenges.map(challenge => (
                <TouchableOpacity
                  key={challenge.id}
                  style={styles.battleCard}
                  onPress={() => handleFriendChallengePress(challenge.id)}>
                  <View style={styles.battleIcon}>
                    <Icon name="person" size={24} color="#71717A" />
                  </View>
                  <View style={styles.battleInfo}>
                    <Text style={styles.battleTitle}>{challenge.name}</Text>
                    <Text style={styles.battleOpponent}>
                      {challenge.challenge}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Bottom Action Buttons - Mobile design'dan birebir */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={handleCreateBattle}>
          <Text style={styles.actionButtonText}>Create Battle</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton]}
          onPress={handleJoinBattle}>
          <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
            Join Battle
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },

  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 100,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 16,
    marginBottom: 32,
  },
  statCard: {
    width: '47%',
    backgroundColor: '#F4F4F5',
    borderRadius: 12,
    padding: 16,
    gap: 8,
  },
  statLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#71717A',
    fontFamily: 'Lexend',
  },
  statValue: {
    fontSize: 30,
    fontWeight: 'bold',
    color: '#0E0D1B',
    fontFamily: 'Lexend',
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0E0D1B',
    marginBottom: 16,
    fontFamily: 'Lexend',
  },
  battleList: {
    gap: 12,
  },
  battleCard: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    backgroundColor: '#F4F4F5',
    borderRadius: 12,
    padding: 12,
  },
  battleIcon: {
    width: 48,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#E4E4E7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  battleInfo: {
    flex: 1,
  },
  battleTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#0E0D1B',
    fontFamily: 'Lexend',
  },
  battleOpponent: {
    fontSize: 14,
    color: '#71717A',
    marginTop: 2,
    fontFamily: 'Lexend',
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E4E4E7',
  },
  actionButton: {
    flex: 1,
    height: 48,
    borderRadius: 8,
    backgroundColor: '#1E13EC',
    alignItems: 'center',
    justifyContent: 'center',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#E4E4E7',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: 'Lexend',
  },
  secondaryButtonText: {
    color: '#0E0D1B',
  },
});
