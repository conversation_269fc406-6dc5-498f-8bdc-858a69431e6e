import {useState, useEffect, useCallback} from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  LoginRequest as ApiLoginRequest,
  RegisterRequest as ApiRegisterRequest,
} from '../services/api/types';
import {User, AuthTokens, LoginRequest, RegisterRequest} from '../types';
import {authService} from '../services/api/authService';
import {STORAGE_KEYS} from '../services/api';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  error: string | null;
  isGuest: boolean;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  loginAsGuest: () => Promise<void>;
  logout: () => Promise<void>;
  clearError: () => void;
  refreshUser: () => Promise<void>;
  switchToGuest: () => void;
  switchToAuth: () => void;
}

export const useAuth = (): AuthState & AuthActions => {
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    tokens: null,
    isLoading: true,
    error: null,
    isGuest: true, // Start in guest mode
  });

  // Initialize auth state on app start
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      // Check if user data exists in storage
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      const tokens = await authService.getAuthTokens();

      if (userData && tokens) {
        // Validate token
        const isValid = await authService.isAuthenticated();

        if (isValid) {
          const user = JSON.parse(userData);

          // Convert API tokens to local format
          const localTokens: AuthTokens = {
            accessToken: tokens.token,
            refreshToken: '',
            expiresIn: 0,
          };

          setState(prev => ({
            ...prev,
            isAuthenticated: true,
            user,
            tokens: localTokens,
            isGuest: false,
            isLoading: false,
          }));
          return;
        } else {
          // Token invalid, clear auth data
          await authService.logout();
        }
      }

      // No valid auth, start in guest mode
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    } catch (error) {
      console.error('Auth initialization error:', error);
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
        error: 'Kimlik doğrulama başlatılamadı',
      }));
    }
  };

  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      // Convert to API format
      const apiCredentials: ApiLoginRequest = {
        username: credentials.identifier,
        password: credentials.password,
      };

      const response = await authService.login(apiCredentials);

      // Convert UserInfo to User format
      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email || '',
        phone: response.user.phone,
        firstName: response.user.name.split(' ')[0],
        lastName: response.user.name.split(' ').slice(1).join(' ') || '',
        avatar: response.user.avatar_url,
        isVerified: response.user.is_verified,
        isActive: response.user.is_active,
        tokens: 0, // Default value
        totalScore: 0, // Default value
        rank: 0, // Default value
        createdAt: response.user.created_at,
        updatedAt: response.user.updated_at,
      };

      // Convert AuthResponse to AuthTokens format
      const tokens: AuthTokens = {
        accessToken: response.token,
        refreshToken: '', // Not provided by API
        expiresIn: 0, // Not provided by API
      };

      // Store user data
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user,
        tokens,
        isGuest: false,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Giriş yapılamadı',
      }));
      throw error;
    }
  }, []);

  const register = useCallback(async (userData: RegisterRequest) => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      // Convert to API format
      const apiUserData: ApiRegisterRequest = {
        username: userData.username,
        email: userData.email,
        password: userData.password,
        name: `${userData.firstName || ''} ${userData.lastName || ''}`.trim(),
        phone: userData.phone,
      };

      const response = await authService.register(apiUserData);

      // Note: Register response only contains message and user_id
      // We'll need to create a mock user or fetch user data separately
      const mockUser: User = {
        id: response.user_id,
        username: userData.username,
        email: userData.email,
        phone: userData.phone,
        firstName: userData.firstName || '',
        lastName: userData.lastName || '',
        avatar: undefined,
        isVerified: false,
        isActive: true,
        tokens: 0,
        totalScore: 0,
        rank: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Store user data
      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_DATA,
        JSON.stringify(mockUser),
      );

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user: mockUser,
        tokens: null, // No tokens provided on registration
        isGuest: false,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Kayıt olunamadı',
      }));
      throw error;
    }
  }, []);

  const loginAsGuest = useCallback(async () => {
    try {
      setState(prev => ({...prev, isLoading: true, error: null}));

      const response = await authService.loginAsGuest();

      // Convert UserInfo to User format
      const user: User = {
        id: response.user.id,
        username: response.user.username,
        email: response.user.email || '',
        phone: response.user.phone,
        firstName: response.user.name.split(' ')[0],
        lastName: response.user.name.split(' ').slice(1).join(' ') || '',
        avatar: response.user.avatar_url,
        isVerified: response.user.is_verified,
        isActive: response.user.is_active,
        tokens: 0,
        totalScore: 0,
        rank: 0,
        createdAt: response.user.created_at,
        updatedAt: response.user.updated_at,
      };

      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        user,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    } catch (error: any) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error.error || 'Misafir girişi başarısız',
      }));
      throw error;
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      await authService.logout();
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);

      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Logout error:', error);
      // Even if logout fails, clear local state
      setState(prev => ({
        ...prev,
        isAuthenticated: false,
        user: null,
        tokens: null,
        isGuest: true,
        isLoading: false,
      }));
    }
  }, []);

  const clearError = useCallback(() => {
    setState(prev => ({...prev, error: null}));
  }, []);

  const refreshUser = useCallback(async () => {
    try {
      if (!state.isAuthenticated) {
        return;
      }

      const userInfo = await authService.getCurrentUser();

      if (!userInfo) {
        throw new Error('User data not found');
      }

      // Convert UserInfo to User format
      const user: User = {
        id: userInfo.id,
        username: userInfo.username,
        email: userInfo.email || '',
        phone: userInfo.phone,
        firstName: userInfo.name.split(' ')[0],
        lastName: userInfo.name.split(' ').slice(1).join(' ') || '',
        avatar: userInfo.avatar_url,
        isVerified: userInfo.is_verified,
        isActive: userInfo.is_active,
        tokens: 0,
        totalScore: 0,
        rank: 0,
        createdAt: userInfo.created_at,
        updatedAt: userInfo.updated_at,
      };

      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));

      setState(prev => ({...prev, user}));
    } catch (error) {
      console.error('Refresh user error:', error);
      // If refresh fails, user might need to re-login
      await logout();
    }
  }, [state.isAuthenticated, logout]);

  const switchToGuest = useCallback(() => {
    setState(prev => ({...prev, isGuest: true}));
  }, []);

  const switchToAuth = useCallback(() => {
    setState(prev => ({...prev, isGuest: false}));
  }, []);

  return {
    ...state,
    login,
    register,
    loginAsGuest,
    logout,
    clearError,
    refreshUser,
    switchToGuest,
    switchToAuth,
  };
};
