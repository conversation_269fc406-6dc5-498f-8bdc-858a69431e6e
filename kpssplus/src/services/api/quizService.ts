import {apiClient} from './client';
import {
  Quiz,
  Question,
  QuizSession,
  QuizResult,
  QuizCategory,
  QuizLeaderboard,
  PaginatedResponse,
  // UserInfo,
} from './types';

export interface QuizFilters {
  subject?: string;
  difficulty_level?: 'easy' | 'medium' | 'hard';
  is_public?: boolean;
  page?: number;
  limit?: number;
  search?: string;
}

export interface StartQuizRequest {
  quiz_id: string;
}

export interface SubmitAnswerRequest {
  session_id: string;
  question_id: string;
  answer: string;
  time_taken?: number;
}

export interface FinishQuizRequest {
  session_id: string;
}

export class QuizService {
  // Public quiz endpoints

  // Get quiz by ID
  async getQuizById(quizId: string): Promise<Quiz> {
    try {
      const response = await apiClient.get(`/quizzes/${quizId}`);
      return response.data as Quiz;
    } catch (error) {
      console.error('Error fetching quiz by ID:', error);
      throw error;
    }
  }

  // Get quiz list with filters
  async getQuizList(
    filters: QuizFilters = {},
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Quiz>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/quiz${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      endpoint,
      false,
    );

    return response.data!;
  }

  // Get single quiz by ID
  async getQuiz(id: string): Promise<Quiz> {
    const response = await apiClient.get<Quiz>(`/quiz/${id}`, false);
    return response.data!;
  }

  // Get quizzes (alias for compatibility)
  async getQuizzes(
    page: number = 1,
    limit: number = 20,
    filters?: QuizFilters,
  ): Promise<PaginatedResponse<Quiz>> {
    return this.getQuizList(filters || {}, page, limit);
  }

  // Search quizzes
  async searchQuizzes(
    query: string,
    filters: Omit<QuizFilters, 'search'> = {},
  ): Promise<PaginatedResponse<Quiz>> {
    const queryParams = new URLSearchParams({search: query});

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/search?${queryParams.toString()}`,
      false,
    );

    return response.data!;
  }

  // Get quizzes by subject
  async getQuizzesBySubject(
    subject: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/subject/${subject}?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get quizzes by type
  async getQuizzesByType(
    type: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/type/${type}?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get popular quizzes
  async getPopularQuizzes(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/popular?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get quiz categories
  async getQuizCategories(): Promise<QuizCategory[]> {
    const response = await apiClient.get<QuizCategory[]>(
      '/quiz/categories',
      false,
    );
    return response.data!;
  }

  // Get quiz questions
  async getQuizQuestions(quizId: string): Promise<Question[]> {
    const response = await apiClient.get<Question[]>(
      `/quiz/${quizId}/questions`,
      false,
    );
    return response.data!;
  }

  // Get quiz statistics
  async getQuizStatistics(quizId: string): Promise<{
    total_attempts: number;
    average_score: number;
    completion_rate: number;
    average_time: number;
  }> {
    const response = await apiClient.get<{
      total_attempts: number;
      average_score: number;
      completion_rate: number;
      average_time: number;
    }>(`/quiz/${quizId}/statistics`, false);

    return response.data!;
  }

  // Get quiz leaderboard
  async getQuizLeaderboard(
    quizId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<QuizLeaderboard>> {
    const response = await apiClient.get<PaginatedResponse<QuizLeaderboard>>(
      `/quiz/${quizId}/leaderboard?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Protected quiz endpoints (require authentication)

  // Get user's quizzes
  async getUserQuizzes(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Quiz>> {
    const response = await apiClient.get<PaginatedResponse<Quiz>>(
      `/quiz/my?page=${page}&limit=${limit}`,
    );

    return response.data!;
  }

  // Start a quiz session
  async startQuiz(quizId: string): Promise<QuizSession> {
    const response = await apiClient.post<QuizSession>(`/quiz/${quizId}/start`);
    return response.data!;
  }

  // Start a quiz session (alias for compatibility)
  async startQuizSession(quizId: string): Promise<QuizSession> {
    return this.startQuiz(quizId);
  }

  // Submit an answer
  async submitAnswer(
    sessionId: string,
    questionId: string,
    answer: string,
    timeTaken?: number,
  ): Promise<{
    correct: boolean;
    explanation?: string;
    score: number;
  }> {
    const response = await apiClient.post<{
      correct: boolean;
      explanation?: string;
      score: number;
    }>(`/quiz/sessions/${sessionId}/answer`, {
      question_id: questionId,
      answer,
      time_taken: timeTaken,
    });

    return response.data!;
  }

  // Submit quiz session (alternative to finish)
  async submitQuiz(sessionId: string): Promise<QuizResult> {
    const response = await apiClient.post<QuizResult>(
      `/quiz/sessions/${sessionId}/submit`,
    );
    return response.data!;
  }

  // Finish quiz session
  async finishQuiz(sessionId: string): Promise<QuizResult> {
    const response = await apiClient.post<QuizResult>(
      `/quiz/sessions/${sessionId}/finish`,
    );
    return response.data!;
  }

  // Get quiz result
  async getQuizResult(resultId: string): Promise<
    QuizResult & {
      quiz: Quiz;
      answers: Array<{
        question: Question;
        user_answer: string;
        is_correct: boolean;
        time_taken: number;
      }>;
    }
  > {
    const response = await apiClient.get<
      QuizResult & {
        quiz: Quiz;
        answers: Array<{
          question: Question;
          user_answer: string;
          is_correct: boolean;
          time_taken: number;
        }>;
      }
    >(`/quiz/results/${resultId}`);

    return response.data!;
  }

  // Get user's quiz results
  async getUserQuizResults(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<QuizResult & {quiz: Quiz}>> {
    const response = await apiClient.get<
      PaginatedResponse<QuizResult & {quiz: Quiz}>
    >(`/quiz/results?page=${page}&limit=${limit}`);

    return response.data!;
  }

  // Create quiz (admin/creator only)
  async createQuiz(quizData: Partial<Quiz>): Promise<Quiz> {
    const response = await apiClient.post<Quiz>('/quiz', quizData);
    return response.data!;
  }

  // Update quiz (admin/creator only)
  async updateQuiz(id: string, quizData: Partial<Quiz>): Promise<Quiz> {
    const response = await apiClient.put<Quiz>(`/quiz/${id}`, quizData);
    return response.data!;
  }

  // Delete quiz (admin/creator only)
  async deleteQuiz(id: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(`/quiz/${id}`);
    return response.data!;
  }

  // Share quiz
  async shareQuiz(
    quizId: string,
    shareData: {
      platform: string;
      message?: string;
    },
  ): Promise<{message: string; share_url: string}> {
    const response = await apiClient.post<{message: string; share_url: string}>(
      `/quiz/${quizId}/share`,
      shareData,
    );
    return response.data!;
  }

  // Invite to quiz
  async inviteToQuiz(
    quizId: string,
    inviteData: {
      user_ids: string[];
      message?: string;
    },
  ): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      `/quiz/${quizId}/invite`,
      inviteData,
    );
    return response.data!;
  }

  // Submit quiz session with all answers
  async submitQuizSession(
    sessionId: string,
    answers: {[key: number]: string},
  ): Promise<QuizResult> {
    const response = await apiClient.post<QuizResult>(
      `/quiz/sessions/${sessionId}/submit`,
      {answers},
    );
    return response.data!;
  }
}

// Export singleton instance
export const quizService = new QuizService();
