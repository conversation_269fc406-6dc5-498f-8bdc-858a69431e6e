import {ApiClient} from './client';
import {
  Battle,
  BattleParticipant,
  BattleStats,
  Challenge,
  PaginatedResponse,
} from './types';

class BattleService {
  private client: ApiClient;

  constructor() {
    this.client = new ApiClient();
  }

  // Get user battle stats
  async getBattleStats(userId: string): Promise<BattleStats> {
    try {
      const response = await this.client.get(`/battles/stats/${userId}`);
      return response.data as BattleStats;
    } catch (error) {
      console.error('Error fetching battle stats:', error);
      throw error;
    }
  }

  // Get active battles
  async getActiveBattles(
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponse<Battle>> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await this.client.get(`/battles/active?${queryParams}`);
      return response.data as PaginatedResponse<Battle>;
    } catch (error) {
      console.error('Error fetching active battles:', error);
      throw error;
    }
  }

  // Get available battles to join
  async getAvailableBattles(
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponse<Battle>> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        status: 'waiting',
      });

      const response = await this.client.get(`/battles?${queryParams}`);
      return response.data as PaginatedResponse<Battle>;
    } catch (error) {
      console.error('Error fetching available battles:', error);
      throw error;
    }
  }

  // Create a new battle
  async createBattle(battleData: {
    title: string;
    description: string;
    type: 'individual' | 'group';
    subject: string;
    difficulty_level: 'beginner' | 'intermediate' | 'advanced';
    max_participants: number;
    duration: number;
  }): Promise<Battle> {
    try {
      const response = await this.client.post('/battles', battleData);
      return response.data as Battle;
    } catch (error) {
      console.error('Error creating battle:', error);
      throw error;
    }
  }

  // Join a battle
  async joinBattle(battleId: string): Promise<BattleParticipant> {
    try {
      const response = await this.client.post(`/battles/${battleId}/join`);
      return response.data as BattleParticipant;
    } catch (error) {
      console.error('Error joining battle:', error);
      throw error;
    }
  }

  // Leave a battle
  async leaveBattle(battleId: string): Promise<void> {
    try {
      await this.client.delete(`/battles/${battleId}/leave`);
    } catch (error) {
      console.error('Error leaving battle:', error);
      throw error;
    }
  }

  // Get battle participants
  async getBattleParticipants(battleId: string): Promise<BattleParticipant[]> {
    try {
      const response = await this.client.get(
        `/battles/${battleId}/participants`,
      );
      return response.data as BattleParticipant[];
    } catch (error) {
      console.error('Error fetching battle participants:', error);
      throw error;
    }
  }

  // Get user challenges
  async getUserChallenges(
    page: number = 1,
    limit: number = 10,
  ): Promise<PaginatedResponse<Challenge>> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      const response = await this.client.get(`/challenges?${queryParams}`);
      return response.data as PaginatedResponse<Challenge>;
    } catch (error) {
      console.error('Error fetching user challenges:', error);
      throw error;
    }
  }

  // Send challenge to user
  async sendChallenge(challengeData: {
    challenged_id: string;
    subject: string;
    difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  }): Promise<Challenge> {
    try {
      const response = await this.client.post('/challenges', challengeData);
      return response.data as Challenge;
    } catch (error) {
      console.error('Error sending challenge:', error);
      throw error;
    }
  }

  // Accept challenge
  async acceptChallenge(challengeId: string): Promise<Challenge> {
    try {
      const response = await this.client.post(
        `/challenges/${challengeId}/accept`,
      );
      return response.data as Challenge;
    } catch (error) {
      console.error('Error accepting challenge:', error);
      throw error;
    }
  }

  // Decline challenge
  async declineChallenge(challengeId: string): Promise<Challenge> {
    try {
      const response = await this.client.post(
        `/challenges/${challengeId}/decline`,
      );
      return response.data as Challenge;
    } catch (error) {
      console.error('Error declining challenge:', error);
      throw error;
    }
  }

  // Get battle leaderboard
  async getBattleLeaderboard(battleId: string): Promise<BattleParticipant[]> {
    try {
      const response = await this.client.get(
        `/battles/${battleId}/leaderboard`,
      );
      return response.data as BattleParticipant[];
    } catch (error) {
      console.error('Error fetching battle leaderboard:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const battleService = new BattleService();
