import {apiClient} from './client';
import {
  UserProgress,
  ProgressStats,
  StudySession,
  ProgressGoal,
  ProgressAchievement,
  PaginatedResponse,
  ApiResponse,
} from './types';

export interface UpdateProgressRequest {
  content_id: string;
  progress_percentage: number;
  completed?: boolean;
  last_position?: number;
  time_spent?: number;
}

export interface CreateGoalRequest {
  title: string;
  description?: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  target_value: number;
  unit: 'minutes' | 'hours' | 'content' | 'quizzes' | 'points';
  deadline?: string;
}

export interface StartSessionRequest {
  content_id?: string;
  session_type: 'content' | 'quiz' | 'practice';
}

export class ProgressService {
  // Progress tracking
  async updateUserProgress(data: UpdateProgressRequest): Promise<UserProgress> {
    const response = await apiClient.put<ApiResponse<UserProgress>>(
      '/progress',
      data,
    );
    return response.data?.data!;
  }

  async getUserContentProgress(contentId: string): Promise<UserProgress> {
    const response = await apiClient.get<ApiResponse<UserProgress>>(
      `/progress/content/${contentId}`,
    );
    return response.data?.data!;
  }

  async getAllUserProgress(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<UserProgress>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get<PaginatedResponse<UserProgress>>(
      `/progress?${queryParams.toString()}`,
    );
    return response.data!;
  }

  // Progress analytics
  async getProgressStats(): Promise<ProgressStats> {
    const response = await apiClient.get<ApiResponse<ProgressStats>>(
      '/progress/stats',
    );
    return response.data?.data!;
  }

  async getProgressHistory(days: number = 30): Promise<
    {
      date: string;
      completed_content: number;
      time_spent: number;
      points_earned: number;
    }[]
  > {
    const queryParams = new URLSearchParams({
      days: days.toString(),
    });

    const response = await apiClient.get<ApiResponse<any[]>>(
      `/progress/history?${queryParams.toString()}`,
    );
    return response.data?.data!;
  }

  async getStudyStreak(): Promise<{
    current_streak: number;
    longest_streak: number;
    last_study_date: string;
  }> {
    const response = await apiClient.get<ApiResponse<any>>('/progress/streak');
    return response.data?.data!;
  }

  // Study sessions
  async startStudySession(data: StartSessionRequest): Promise<StudySession> {
    const response = await apiClient.post<ApiResponse<StudySession>>(
      '/progress/sessions',
      data,
    );
    return response.data?.data!;
  }

  async endStudySession(
    sessionId: string,
    progressMade: number = 0,
    completed: boolean = false,
  ): Promise<StudySession> {
    const response = await apiClient.put<ApiResponse<StudySession>>(
      `/progress/sessions/${sessionId}/end`,
      {
        progress_made: progressMade,
        completed,
      },
    );
    return response.data?.data!;
  }

  async getStudySessions(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<StudySession>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get<PaginatedResponse<StudySession>>(
      `/progress/sessions?${queryParams.toString()}`,
    );
    return response.data!;
  }

  // Progress goals
  async setProgressGoal(data: CreateGoalRequest): Promise<ProgressGoal> {
    const response = await apiClient.post<ApiResponse<ProgressGoal>>(
      '/progress/goals',
      data,
    );
    return response.data?.data!;
  }

  async getProgressGoals(): Promise<ProgressGoal[]> {
    const response = await apiClient.get<ApiResponse<ProgressGoal[]>>(
      '/progress/goals',
    );
    return response.data?.data!;
  }

  async updateProgressGoal(
    goalId: string,
    data: Partial<CreateGoalRequest>,
  ): Promise<ProgressGoal> {
    const response = await apiClient.put<ApiResponse<ProgressGoal>>(
      `/progress/goals/${goalId}`,
      data,
    );
    return response.data?.data!;
  }

  async deleteProgressGoal(goalId: string): Promise<void> {
    await apiClient.delete(`/progress/goals/${goalId}`);
  }

  // Achievements
  async getProgressAchievements(): Promise<ProgressAchievement[]> {
    const response = await apiClient.get<ApiResponse<ProgressAchievement[]>>(
      '/progress/achievements',
    );
    return response.data?.data!;
  }

  // Get comprehensive progress dashboard data
  async getProgressDashboard(): Promise<{
    stats: ProgressStats;
    recent_sessions: StudySession[];
    active_goals: ProgressGoal[];
    recent_achievements: ProgressAchievement[];
    streak: {
      current_streak: number;
      longest_streak: number;
      last_study_date: string;
    };
  }> {
    try {
      const [stats, sessions, goals, achievements, streak] = await Promise.all([
        this.getProgressStats(),
        this.getStudySessions(1, 5),
        this.getProgressGoals(),
        this.getProgressAchievements(),
        this.getStudyStreak(),
      ]);

      return {
        stats,
        recent_sessions: sessions.data,
        active_goals: goals.filter(goal => !goal.completed),
        recent_achievements: achievements.slice(0, 5),
        streak,
      };
    } catch (error) {
      // Fallback to mock data if API fails
      return {
        stats: {
          total_content: 150,
          completed_content: 45,
          in_progress_content: 12,
          total_time_spent: 18000, // 5 hours
          completion_percentage: 30,
          streak_days: 7,
          weekly_progress: [],
          subject_progress: [
            {subject: 'Matematik', total: 50, completed: 15, percentage: 30},
            {subject: 'Türkçe', total: 40, completed: 12, percentage: 30},
            {subject: 'Tarih', total: 35, completed: 10, percentage: 29},
            {subject: 'Coğrafya', total: 25, completed: 8, percentage: 32},
          ],
        },
        recent_sessions: [],
        active_goals: [],
        recent_achievements: [],
        streak: {
          current_streak: 7,
          longest_streak: 15,
          last_study_date: new Date().toISOString(),
        },
      };
    }
  }
}

export const progressService = new ProgressService();
