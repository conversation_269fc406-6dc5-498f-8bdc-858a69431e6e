// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  status: number;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

// Auth Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface EmailLoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email?: string;
  phone?: string;
  password: string;
  name: string;
  target_kpss_year?: number;
  study_area?: string;
}

export interface AuthResponse {
  token: string;
  expires: string;
  is_succeeded: boolean;
  user: UserInfo;
}

export interface AuthTokens {
  token: string;
  expires: string;
}

export interface UserInfo {
  id: string;
  username: string;
  email?: string;
  phone?: string;
  name: string;
  first_name?: string; // Added for compatibility
  last_name?: string; // Added for compatibility
  bio?: string;
  profile_image_url?: string;
  avatar_url?: string;
  target_kpss_year?: number;
  study_area?: string;
  is_verified: boolean;
  is_active: boolean;
  is_admin: boolean;
  push_notification_enabled: boolean;
  email_notification_enabled: boolean;
  created_at: string;
  updated_at: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  password: string;
}

export interface VerifyOTPRequest {
  code: string;
  email?: string;
  phone?: string;
}

// Content Types
export interface Content {
  id: string;
  title: string;
  description?: string;
  content_type: 'video' | 'pdf' | 'audio' | 'text';
  subject: string;
  topic?: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  duration?: number;
  file_url?: string;
  thumbnail_url?: string;
  view_count: number;
  like_count: number;
  is_premium: boolean;
  tags?: string[];
  created_at: string;
  updated_at: string;
}

export interface ContentProgress {
  content_id: string;
  progress_percentage: number;
  completed: boolean;
  last_position?: number;
  updated_at: string;
}

// Quiz Types
export interface Quiz {
  id: string;
  title: string;
  description?: string;
  subject: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  difficulty?: 'easy' | 'medium' | 'hard'; // Alternative difficulty field
  question_count: number;
  time_limit?: number;
  duration?: number; // Duration in minutes
  points?: number; // Total points for the quiz
  category_id?: string; // Category reference
  thumbnail_url?: string;
  is_public: boolean;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface Question {
  id: string;
  quiz_id: string;
  question_text: string;
  question?: string; // Alternative question field
  question_type: 'multiple_choice' | 'true_false' | 'fill_blank';
  options?: string[];
  option_a?: string; // Individual option fields for compatibility
  option_b?: string;
  option_c?: string;
  option_d?: string;
  correct_answer: string;
  explanation?: string;
  points: number;
  order_index: number;
}

export interface QuizSession {
  id: string;
  quiz_id: string;
  user_id: string;
  started_at: string;
  finished_at?: string;
  status: 'in_progress' | 'completed' | 'abandoned';
}

export interface QuizResult {
  id: string;
  session_id: string;
  quiz_id: string;
  user_id: string;
  score: number;
  correct_answers: number;
  total_questions: number;
  time_taken: number;
  points_earned?: number; // Points earned from the quiz
  completed_at: string;
}

export interface QuizCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  quiz_count?: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface QuizLeaderboard {
  id: string;
  user_id: string;
  quiz_id: string;
  user: UserInfo;
  score: number;
  completion_time: number;
  rank: number;
  completed_at: string;
}

// Social Types
export interface Friend {
  id: string;
  username: string;
  name: string;
  profile_image_url?: string;
  is_online: boolean;
  mutual_friends_count?: number;
  friendship_date: string;
}

export interface FriendRequest {
  id: string;
  sender_id: string;
  receiver_id: string;
  sender: UserInfo;
  receiver: UserInfo;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
}

export interface Post {
  id: string;
  user_id: string;
  user: UserInfo;
  content: string;
  image_url?: string;
  like_count: number;
  comment_count: number;
  share_count: number;
  is_liked: boolean;
  created_at: string;
  updated_at: string;
}

export interface Comment {
  id: string;
  post_id: string;
  user_id: string;
  user: UserInfo;
  content: string;
  like_count: number;
  is_liked: boolean;
  created_at: string;
}

// Notification Types
export interface Notification {
  id: string;
  user_id: string;
  type: 'quiz' | 'social' | 'achievement' | 'system';
  title: string;
  message: string;
  data?: any;
  is_read: boolean;
  created_at: string;
}

// Battle Types
export interface Battle {
  id: string;
  title: string;
  description: string;
  type: 'individual' | 'group';
  status: 'waiting' | 'active' | 'completed';
  subject: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  max_participants: number;
  current_participants: number;
  duration: number; // in minutes
  start_time: string;
  end_time?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface BattleParticipant {
  id: string;
  battle_id: string;
  user_id: string;
  user: UserInfo;
  score: number;
  rank: number;
  completed_at?: string;
  created_at: string;
}

export interface BattleStats {
  total_battles: number;
  wins: number;
  losses: number;
  draws: number;
  win_rate: number;
  average_score: number;
  best_rank: number;
  total_points: number;
}

export interface Challenge {
  id: string;
  challenger_id: string;
  challenged_id: string;
  challenger: UserInfo;
  challenged: UserInfo;
  subject: string;
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  status: 'pending' | 'accepted' | 'declined' | 'completed';
  battle_id?: string;
  created_at: string;
  updated_at: string;
}

// Badge System Types
export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  points: number;
  requirements: {
    type: string;
    value: number;
    description: string;
  }[];
  created_at: string;
  updated_at: string;
}

export interface UserBadge {
  id: string;
  user_id: string;
  badge_id: string;
  badge: Badge;
  earned_at: string;
  progress?: number;
  is_claimed: boolean;
}

export interface BadgeProgress {
  badge_id: string;
  badge: Badge;
  current_progress: number;
  required_progress: number;
  percentage: number;
  is_completed: boolean;
  next_milestone?: number;
}

export interface BadgeCategory {
  id: string;
  name: string;
  description: string;
  icon: string;
  badge_count: number;
}

// Progress System Types
export interface UserProgress {
  id: string;
  user_id: string;
  content_id: string;
  content?: Content;
  progress_percentage: number;
  completed: boolean;
  last_position?: number;
  time_spent: number; // in seconds
  created_at: string;
  updated_at: string;
}

export interface ProgressStats {
  total_content: number;
  completed_content: number;
  in_progress_content: number;
  total_time_spent: number; // in seconds
  completion_percentage: number;
  streak_days: number;
  weekly_progress: {
    week: string;
    completed: number;
    time_spent: number;
  }[];
  subject_progress: {
    subject: string;
    total: number;
    completed: number;
    percentage: number;
  }[];
}

export interface StudySession {
  id: string;
  user_id: string;
  content_id?: string;
  content?: Content;
  session_type: 'content' | 'quiz' | 'practice';
  started_at: string;
  ended_at?: string;
  duration: number; // in seconds
  progress_made: number; // percentage
  completed: boolean;
}

export interface ProgressGoal {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  target_value: number;
  current_value: number;
  unit: 'minutes' | 'hours' | 'content' | 'quizzes' | 'points';
  deadline?: string;
  completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProgressAchievement {
  id: string;
  user_id: string;
  achievement_type: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  unlocked_at: string;
}

// Analytics Types
export interface AnalyticsData {
  id: string;
  user_id: string;
  metric_name: string;
  metric_value: number;
  date: string;
  created_at: string;
  updated_at: string;
}

export interface UserAnalytics {
  total_study_time: number;
  total_content_completed: number;
  total_quizzes_taken: number;
  average_quiz_score: number;
  current_streak: number;
  longest_streak: number;
  total_badges_earned: number;
  favorite_subject: string;
  weekly_study_time: number;
  monthly_study_time: number;
  daily_average: number;
  improvement_rate: number;
}

export interface StudyTimeAnalytics {
  date: string;
  study_time: number;
  content_completed: number;
  quizzes_taken: number;
}

export interface SubjectAnalytics {
  subject: string;
  study_time: number;
  content_completed: number;
  quiz_score_average: number;
  improvement_rate: number;
  last_studied: string;
}

export interface PerformanceAnalytics {
  period: 'daily' | 'weekly' | 'monthly';
  quiz_scores: number[];
  study_times: number[];
  content_completion: number[];
  dates: string[];
  average_score: number;
  score_trend: 'improving' | 'declining' | 'stable';
}

// Goals System Types
export interface Goal {
  id: string;
  user_id: string;
  title: string;
  description: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  target_value: number;
  current_value: number;
  unit: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  category:
    | 'study_time'
    | 'content_completion'
    | 'quiz_score'
    | 'streak'
    | 'custom';
  reward_points: number;
  created_at: string;
  updated_at: string;
}

export interface GoalProgress {
  goal_id: string;
  date: string;
  progress_value: number;
  notes?: string;
  created_at: string;
}

export interface GoalTemplate {
  id: string;
  title: string;
  description: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  suggested_target_value: number;
  unit: string;
  category:
    | 'study_time'
    | 'content_completion'
    | 'quiz_score'
    | 'streak'
    | 'custom';
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  reward_points: number;
}

export interface GoalStats {
  total_goals: number;
  active_goals: number;
  completed_goals: number;
  completion_rate: number;
  total_points_earned: number;
  current_streak: number;
  longest_streak: number;
  average_completion_time: number;
}

// Error Types
export interface ApiError {
  message: string;
  status: number;
  code?: string;
  details?: any;
}
