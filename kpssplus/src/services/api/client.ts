import {API_CONFIG, TokenManager} from './config';
import {ApiResponse} from './types';

export class ApiError extends Error {
  public status: number;
  public code?: string;
  public data?: any;

  constructor(message: string, status: number, code?: string, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.code = code;
    this.data = data;
  }
}

export class ApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.timeout = API_CONFIG.timeout;
    this.defaultHeaders = API_CONFIG.headers;
  }

  private async getHeaders(
    includeAuth: boolean = true,
  ): Promise<Record<string, string>> {
    const headers = {...this.defaultHeaders};

    if (includeAuth) {
      const token = await TokenManager.getToken();
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = await this.getHeaders(includeAuth);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await response.json();

      if (!response.ok) {
        throw new ApiError(
          responseData.error || responseData.message || 'Request failed',
          response.status,
          responseData.code,
          responseData,
        );
      }

      return {
        data: responseData.data,
        status: response.status,
        message: responseData.message,
      };
    } catch (error: unknown) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408);
      }

      throw new ApiError(
        error instanceof Error ? error.message : 'Network error',
        0,
        'NETWORK_ERROR',
        error,
      );
    }
  }

  async get<T>(
    endpoint: string,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {method: 'GET'}, includeAuth);
  }

  async post<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth,
    );
  }

  async put<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(
      endpoint,
      {
        method: 'PUT',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth,
    );
  }

  async delete<T>(
    endpoint: string,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {method: 'DELETE'}, includeAuth);
  }

  async patch<T>(
    endpoint: string,
    data?: any,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    return this.request<T>(
      endpoint,
      {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : undefined,
      },
      includeAuth,
    );
  }

  // Upload file method
  async uploadFile<T>(
    endpoint: string,
    file: FormData,
    includeAuth: boolean = true,
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = await this.getHeaders(includeAuth);

      // Remove Content-Type header for FormData
      delete headers['Content-Type'];

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout * 3); // Longer timeout for uploads

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: file,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const responseData = await response.json();

      if (!response.ok) {
        throw new ApiError(
          responseData.error || responseData.message || 'Upload failed',
          response.status,
          responseData.code,
          responseData,
        );
      }

      return {
        data: responseData.data,
        status: response.status,
        message: responseData.message,
      };
    } catch (error: unknown) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error instanceof Error && error.name === 'AbortError') {
        throw new ApiError('Upload timeout', 408);
      }

      throw new ApiError(
        error instanceof Error ? error.message : 'Upload error',
        0,
        'UPLOAD_ERROR',
        error,
      );
    }
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
