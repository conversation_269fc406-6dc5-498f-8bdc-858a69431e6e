import {apiClient} from './client';
import {
  Content,
  ContentProgress,
  PaginatedResponse,
  // ApiResponse,
} from './types';

export interface ContentFilters {
  subject?: string;
  content_type?: 'video' | 'pdf' | 'audio' | 'text';
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  is_premium?: boolean;
  page?: number;
  limit?: number;
  search?: string;
}

export interface UpdateProgressRequest {
  content_id: string;
  progress_percentage: number;
  last_position?: number;
  completed?: boolean;
}

export class ContentService {
  // Get content list with filters and pagination
  async getContentList(
    filters: ContentFilters = {},
  ): Promise<PaginatedResponse<Content>> {
    const queryParams = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/content${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`;
    const response = await apiClient.get<PaginatedResponse<Content>>(
      endpoint,
      false,
    );

    return response.data!;
  }

  // Get single content by ID
  async getContent(id: string): Promise<Content> {
    const response = await apiClient.get<Content>(`/content/${id}`, false);
    return response.data!;
  }

  // Get single content by ID (alias for compatibility)
  async getContentById(id: string): Promise<Content> {
    return this.getContent(id);
  }

  // Search content
  async searchContent(
    query: string,
    filters: Omit<ContentFilters, 'search'> = {},
  ): Promise<PaginatedResponse<Content>> {
    const queryParams = new URLSearchParams({search: query});

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/search?${queryParams.toString()}`,
      false,
    );

    return response.data!;
  }

  // Get content by type
  async getContentByType(
    type: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/type/${type}?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get content by subject
  async getContentBySubject(
    subject: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/subject/${subject}?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get popular content
  async getPopularContent(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/popular?page=${page}&limit=${limit}`,
      false,
    );

    return response.data!;
  }

  // Get recommended content (personalized if authenticated)
  async getRecommendedContent(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/recommended?page=${page}&limit=${limit}`,
      true, // Include auth for personalization
    );

    return response.data!;
  }

  // Get content statistics
  async getContentStats(id: string): Promise<{
    view_count: number;
    like_count: number;
    completion_rate: number;
    average_rating: number;
  }> {
    const response = await apiClient.get<{
      view_count: number;
      like_count: number;
      completion_rate: number;
      average_rating: number;
    }>(`/content/${id}/stats`, false);

    return response.data!;
  }

  // Protected methods (require authentication)

  // Get user's content library
  async getUserContent(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/library?page=${page}&limit=${limit}`,
    );

    return response.data!;
  }

  // Add content to user's library
  async addContentToLibrary(contentId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      `/content/${contentId}/library`,
    );
    return response.data!;
  }

  // Remove content from user's library
  async removeContentFromLibrary(
    contentId: string,
  ): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/content/${contentId}/library`,
    );
    return response.data!;
  }

  // Update content progress
  async updateProgress(
    progressData: UpdateProgressRequest,
  ): Promise<{message: string}> {
    const response = await apiClient.put<{message: string}>(
      '/content/progress',
      progressData,
    );
    return response.data!;
  }

  // Get content progress
  async getProgress(contentId: string): Promise<ContentProgress> {
    const response = await apiClient.get<ContentProgress>(
      `/content/${contentId}/progress`,
    );
    return response.data!;
  }

  // Get user's overall progress
  async getUserProgress(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<ContentProgress>> {
    const response = await apiClient.get<PaginatedResponse<ContentProgress>>(
      `/content/progress?page=${page}&limit=${limit}`,
    );

    return response.data!;
  }

  // Create content (admin/creator only)
  async createContent(contentData: Partial<Content>): Promise<Content> {
    const response = await apiClient.post<Content>('/content', contentData);
    return response.data!;
  }

  // Update content (admin/creator only)
  async updateContent(
    id: string,
    contentData: Partial<Content>,
  ): Promise<Content> {
    const response = await apiClient.put<Content>(
      `/content/${id}`,
      contentData,
    );
    return response.data!;
  }

  // Delete content (admin/creator only)
  async deleteContent(id: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/content/${id}`,
    );
    return response.data!;
  }

  // Like/Unlike content
  async toggleLike(
    contentId: string,
  ): Promise<{liked: boolean; like_count: number}> {
    const response = await apiClient.post<{liked: boolean; like_count: number}>(
      `/content/${contentId}/like`,
    );
    return response.data!;
  }

  // Rate content
  async rateContent(
    contentId: string,
    rating: number,
  ): Promise<{message: string; average_rating: number}> {
    const response = await apiClient.post<{
      message: string;
      average_rating: number;
    }>(`/content/${contentId}/rate`, {rating});
    return response.data!;
  }

  // Get favorite content (saved content)
  async getFavoriteContent(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/favorites?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Get watch history (recent content)
  async getWatchHistory(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/history?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Remove from favorites
  async removeFavorite(contentId: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/content/${contentId}/favorite`,
    );
    return response.data!;
  }

  // Library management (backend has separate library endpoints)
  async addToLibrary(contentId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      `/content/${contentId}/library`,
    );
    return response.data!;
  }

  // Remove from library
  async removeFromLibrary(contentId: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/content/${contentId}/library`,
    );
    return response.data!;
  }

  // Get user library
  async getUserLibrary(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Content>> {
    const response = await apiClient.get<PaginatedResponse<Content>>(
      `/content/library?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Like/unlike content
  async likeContent(contentId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      `/content/${contentId}/like`,
    );
    return response.data!;
  }
}

// Export singleton instance
export const contentService = new ContentService();
