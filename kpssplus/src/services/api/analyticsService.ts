import {apiClient} from './client';
import {ApiResponse, PaginatedResponse} from './types';

export interface AnalyticsData {
  id: string;
  user_id: string;
  metric_name: string;
  metric_value: number;
  date: string;
  created_at: string;
  updated_at: string;
}

export interface UserAnalytics {
  total_study_time: number;
  total_content_completed: number;
  total_quizzes_taken: number;
  average_quiz_score: number;
  current_streak: number;
  longest_streak: number;
  total_badges_earned: number;
  favorite_subject: string;
  weekly_study_time: number;
  monthly_study_time: number;
  daily_average: number;
  improvement_rate: number;
}

export interface StudyTimeAnalytics {
  date: string;
  study_time: number;
  content_completed: number;
  quizzes_taken: number;
}

export interface SubjectAnalytics {
  subject: string;
  study_time: number;
  content_completed: number;
  quiz_score_average: number;
  improvement_rate: number;
  last_studied: string;
}

export interface PerformanceAnalytics {
  period: 'daily' | 'weekly' | 'monthly';
  quiz_scores: number[];
  study_times: number[];
  content_completion: number[];
  dates: string[];
  average_score: number;
  score_trend: 'improving' | 'declining' | 'stable';
}

export class AnalyticsService {
  async getUserAnalytics(): Promise<UserAnalytics> {
    try {
      const response = await apiClient.get<ApiResponse<UserAnalytics>>(
        '/analytics/user',
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching user analytics:', error);
      // Fallback to mock data
      return this.getMockUserAnalytics();
    }
  }

  async getStudyTimeAnalytics(
    period: 'week' | 'month' | 'year' = 'month',
  ): Promise<StudyTimeAnalytics[]> {
    try {
      const response = await apiClient.get<ApiResponse<StudyTimeAnalytics[]>>(
        `/analytics/study-time?period=${period}`,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching study time analytics:', error);
      // Fallback to mock data
      return this.getMockStudyTimeAnalytics();
    }
  }

  async getSubjectAnalytics(): Promise<SubjectAnalytics[]> {
    try {
      const response = await apiClient.get<ApiResponse<SubjectAnalytics[]>>(
        '/analytics/subjects',
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching subject analytics:', error);
      // Fallback to mock data
      return this.getMockSubjectAnalytics();
    }
  }

  async getPerformanceAnalytics(
    period: 'daily' | 'weekly' | 'monthly' = 'weekly',
  ): Promise<PerformanceAnalytics> {
    try {
      const response = await apiClient.get<ApiResponse<PerformanceAnalytics>>(
        `/analytics/performance?period=${period}`,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching performance analytics:', error);
      // Fallback to mock data
      return this.getMockPerformanceAnalytics();
    }
  }

  async getAnalyticsData(
    metric?: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<AnalyticsData>> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (metric) {
        queryParams.append('metric', metric);
      }
      if (startDate) {
        queryParams.append('start_date', startDate);
      }
      if (endDate) {
        queryParams.append('end_date', endDate);
      }

      const response = await apiClient.get<PaginatedResponse<AnalyticsData>>(
        `/analytics/data?${queryParams.toString()}`,
      );
      return response.data!;
    } catch (error) {
      console.error('Error fetching analytics data:', error);
      // Fallback to mock data
      return this.getMockAnalyticsData();
    }
  }

  async recordAnalytics(
    metricName: string,
    metricValue: number,
  ): Promise<AnalyticsData> {
    try {
      const response = await apiClient.post<ApiResponse<AnalyticsData>>(
        '/analytics/record',
        {
          metric_name: metricName,
          metric_value: metricValue,
        },
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error recording analytics:', error);
      throw error;
    }
  }

  // Mock data methods for fallback
  private getMockUserAnalytics(): UserAnalytics {
    return {
      total_study_time: 12600, // 3.5 hours in seconds
      total_content_completed: 45,
      total_quizzes_taken: 23,
      average_quiz_score: 78.5,
      current_streak: 7,
      longest_streak: 15,
      total_badges_earned: 12,
      favorite_subject: 'Matematik',
      weekly_study_time: 7200, // 2 hours
      monthly_study_time: 28800, // 8 hours
      daily_average: 1800, // 30 minutes
      improvement_rate: 12.5,
    };
  }

  private getMockStudyTimeAnalytics(): StudyTimeAnalytics[] {
    const data: StudyTimeAnalytics[] = [];
    const today = new Date();

    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      data.push({
        date: date.toISOString().split('T')[0],
        study_time: Math.floor(Math.random() * 3600) + 600, // 10 min to 1 hour
        content_completed: Math.floor(Math.random() * 5),
        quizzes_taken: Math.floor(Math.random() * 3),
      });
    }

    return data;
  }

  private getMockSubjectAnalytics(): SubjectAnalytics[] {
    return [
      {
        subject: 'Matematik',
        study_time: 4500,
        content_completed: 15,
        quiz_score_average: 82.3,
        improvement_rate: 15.2,
        last_studied: '2024-01-15T10:30:00Z',
      },
      {
        subject: 'Türkçe',
        study_time: 3600,
        content_completed: 12,
        quiz_score_average: 75.8,
        improvement_rate: 8.7,
        last_studied: '2024-01-14T14:20:00Z',
      },
      {
        subject: 'Tarih',
        study_time: 2700,
        content_completed: 10,
        quiz_score_average: 68.5,
        improvement_rate: -2.1,
        last_studied: '2024-01-13T16:45:00Z',
      },
      {
        subject: 'Coğrafya',
        study_time: 1800,
        content_completed: 8,
        quiz_score_average: 71.2,
        improvement_rate: 5.3,
        last_studied: '2024-01-12T09:15:00Z',
      },
    ];
  }

  private getMockPerformanceAnalytics(): PerformanceAnalytics {
    return {
      period: 'weekly',
      quiz_scores: [65, 72, 68, 75, 78, 82, 85],
      study_times: [1800, 2400, 1200, 3600, 2700, 3000, 2100],
      content_completion: [2, 3, 1, 4, 3, 4, 2],
      dates: [
        '2024-01-08',
        '2024-01-09',
        '2024-01-10',
        '2024-01-11',
        '2024-01-12',
        '2024-01-13',
        '2024-01-14',
      ],
      average_score: 75.0,
      score_trend: 'improving',
    };
  }

  private getMockAnalyticsData(): PaginatedResponse<AnalyticsData> {
    const mockData: AnalyticsData[] = [
      {
        id: '1',
        user_id: 'user1',
        metric_name: 'study_time',
        metric_value: 1800,
        date: '2024-01-15',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z',
      },
      {
        id: '2',
        user_id: 'user1',
        metric_name: 'quiz_score',
        metric_value: 85,
        date: '2024-01-15',
        created_at: '2024-01-15T11:45:00Z',
        updated_at: '2024-01-15T11:45:00Z',
      },
    ];

    return {
      data: mockData,
      pagination: {
        page: 1,
        limit: 20,
        total: mockData.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      },
    };
  }
}

export const analyticsService = new AnalyticsService();
