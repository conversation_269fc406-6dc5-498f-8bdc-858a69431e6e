import {apiClient} from './client';
import {
  Friend,
  FriendRequest,
  Post,
  UserInfo,
  Comment,
  PaginatedResponse,
} from './types';

export interface CreatePostRequest {
  content: string;
  image_url?: string;
}

export interface CreateCommentRequest {
  post_id: string;
  content: string;
}

export interface FriendRequestResponse {
  request_id: string;
  action: 'accept' | 'reject';
}

export interface UserSearchFilters {
  query: string;
  page?: number;
  limit?: number;
}

export class SocialService {
  // Activity Feed
  async getActivityFeed(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Post>> {
    const queryParams = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get<PaginatedResponse<Post>>(
      `/social/feed?${queryParams}`,
    );
    return response.data!;
  }

  // Friendship management

  // Send friend request
  async sendFriendRequest(userId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      '/social/friends/request',
      {
        receiver_id: userId,
      },
    );
    return response.data!;
  }

  // Respond to friend request
  async respondToFriendRequest(
    requestId: string,
    action: 'accept' | 'reject',
  ): Promise<{message: string}> {
    const response = await apiClient.put<{message: string}>(
      '/social/friends/request/respond',
      {
        request_id: requestId,
        action,
      },
    );
    return response.data!;
  }

  // Get friend requests (sent or received)
  async getFriendRequests(
    type: 'sent' | 'received',
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<FriendRequest>> {
    const response = await apiClient.get<PaginatedResponse<FriendRequest>>(
      `/social/friends/requests/${type}?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Get friends list
  async getFriends(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Friend>> {
    const response = await apiClient.get<PaginatedResponse<Friend>>(
      `/social/friends?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Remove friend
  async removeFriend(friendId: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/social/friends/${friendId}`,
    );
    return response.data!;
  }

  // Follow system

  // Follow user
  async followUser(userId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>('/social/follow', {
      user_id: userId,
    });
    return response.data!;
  }

  // Unfollow user
  async unfollowUser(userId: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/social/follow/${userId}`,
    );
    return response.data!;
  }

  // Get followers
  async getFollowers(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<UserInfo>> {
    const response = await apiClient.get<PaginatedResponse<UserInfo>>(
      `/social/followers?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Get following
  async getFollowing(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<UserInfo>> {
    const response = await apiClient.get<PaginatedResponse<UserInfo>>(
      `/social/following?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // User search and discovery

  // Search users
  async searchUsers(
    query: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<
    PaginatedResponse<
      UserInfo & {
        is_friend: boolean;
        is_following: boolean;
        mutual_friends_count: number;
      }
    >
  > {
    const response = await apiClient.get<
      PaginatedResponse<
        UserInfo & {
          is_friend: boolean;
          is_following: boolean;
          mutual_friends_count: number;
        }
      >
    >(
      `/social/search/users?query=${encodeURIComponent(
        query,
      )}&page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Get friend suggestions
  async getFriendSuggestions(
    page: number = 1,
    limit: number = 20,
  ): Promise<
    PaginatedResponse<
      UserInfo & {
        mutual_friends_count: number;
        common_interests: string[];
      }
    >
  > {
    const response = await apiClient.get<
      PaginatedResponse<
        UserInfo & {
          mutual_friends_count: number;
          common_interests: string[];
        }
      >
    >(`/social/suggestions/friends?page=${page}&limit=${limit}`);
    return response.data!;
  }

  // Get mutual friends
  async getMutualFriends(
    userId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Friend>> {
    const response = await apiClient.get<PaginatedResponse<Friend>>(
      `/social/mutual-friends/${userId}?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // User blocking

  // Block user
  async blockUser(userId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>('/social/block', {
      user_id: userId,
    });
    return response.data!;
  }

  // Unblock user
  async unblockUser(userId: string): Promise<{message: string}> {
    const response = await apiClient.delete<{message: string}>(
      `/social/block/${userId}`,
    );
    return response.data!;
  }

  // Get blocked users
  async getBlockedUsers(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<UserInfo>> {
    const response = await apiClient.get<PaginatedResponse<UserInfo>>(
      `/social/blocked?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Social activity and profiles

  // Get friend activity
  async getFriendActivity(
    page: number = 1,
    limit: number = 20,
  ): Promise<
    PaginatedResponse<{
      user: UserInfo;
      activity_type:
        | 'quiz_completed'
        | 'content_watched'
        | 'achievement_earned'
        | 'friend_added';
      activity_data: any;
      created_at: string;
    }>
  > {
    const response = await apiClient.get<
      PaginatedResponse<{
        user: UserInfo;
        activity_type:
          | 'quiz_completed'
          | 'content_watched'
          | 'achievement_earned'
          | 'friend_added';
        activity_data: any;
        created_at: string;
      }>
    >(`/social/activity?page=${page}&limit=${limit}`);
    return response.data!;
  }

  // Get user profile
  async getUserProfile(userId: string): Promise<
    UserInfo & {
      is_friend: boolean;
      is_following: boolean;
      is_blocked: boolean;
      mutual_friends_count: number;
      stats: {
        quiz_count: number;
        total_score: number;
        achievements_count: number;
        friends_count: number;
      };
    }
  > {
    const response = await apiClient.get<
      UserInfo & {
        is_friend: boolean;
        is_following: boolean;
        is_blocked: boolean;
        mutual_friends_count: number;
        stats: {
          quiz_count: number;
          total_score: number;
          achievements_count: number;
          friends_count: number;
        };
      }
    >(`/social/profile/${userId}`);
    return response.data!;
  }

  // Get social statistics
  async getSocialStats(): Promise<{
    friends_count: number;
    followers_count: number;
    following_count: number;
    pending_requests_count: number;
  }> {
    const response = await apiClient.get<{
      friends_count: number;
      followers_count: number;
      following_count: number;
      pending_requests_count: number;
    }>('/social/stats');
    return response.data!;
  }

  // Get leaderboard (public endpoint)
  async getLeaderboard(
    page: number = 1,
    limit: number = 20,
  ): Promise<
    PaginatedResponse<{
      user: UserInfo;
      total_score: number;
      quiz_count: number;
      achievements_count: number;
      rank: number;
    }>
  > {
    const response = await apiClient.get<
      PaginatedResponse<{
        user: UserInfo;
        total_score: number;
        quiz_count: number;
        achievements_count: number;
        rank: number;
      }>
    >(
      `/social/leaderboard?page=${page}&limit=${limit}`,
      false, // Public endpoint
    );
    return response.data!;
  }

  // Posts

  // Create post
  async createPost(content: string): Promise<Post> {
    const response = await apiClient.post<Post>('/social/posts', {
      content,
    });
    return response.data!;
  }

  // Get post
  async getPost(postId: string): Promise<Post> {
    const response = await apiClient.get<Post>(`/social/posts/${postId}`);
    return response.data!;
  }

  // Like post
  async likePost(postId: string): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>('/social/likes', {
      post_id: postId,
    });
    return response.data!;
  }

  // Comments

  // Get post comments
  async getPostComments(
    postId: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Comment>> {
    const response = await apiClient.get<PaginatedResponse<Comment>>(
      `/social/posts/${postId}/comments?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Create comment
  async createComment(postId: string, content: string): Promise<Comment> {
    const response = await apiClient.post<Comment>('/social/comments', {
      post_id: postId,
      content,
    });
    return response.data!;
  }
}

// Export singleton instance
export const socialService = new SocialService();
