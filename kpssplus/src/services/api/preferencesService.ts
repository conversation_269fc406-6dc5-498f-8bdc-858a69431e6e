import {apiClient} from './client';

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'tr' | 'en';
  notifications: {
    push_notifications: boolean;
    email_notifications: boolean;
    sms_notifications: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'private' | 'friends';
    data_sharing: boolean;
  };
  account: {
    two_factor_auth: boolean;
    auto_logout: boolean;
  };
}

export class PreferencesService {
  // Get user preferences
  async getUserPreferences(): Promise<UserPreferences> {
    const response = await apiClient.get<UserPreferences>('/preferences');
    return response.data!;
  }

  // Update user preferences
  async updateUserPreferences(
    preferences: Partial<UserPreferences>,
  ): Promise<UserPreferences> {
    const response = await apiClient.put<UserPreferences>(
      '/preferences',
      preferences,
    );
    return response.data!;
  }

  // Update theme preference
  async updateTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    await apiClient.put('/preferences/theme', {theme});
  }

  // Update language preference
  async updateLanguage(language: 'tr' | 'en'): Promise<void> {
    await apiClient.put('/preferences/language', {language});
  }

  // Update notification preferences
  async updateNotificationPreferences(
    notifications: UserPreferences['notifications'],
  ): Promise<void> {
    await apiClient.put('/preferences/notifications', {notifications});
  }

  // Update privacy preferences
  async updatePrivacyPreferences(
    privacy: UserPreferences['privacy'],
  ): Promise<void> {
    await apiClient.put('/preferences/privacy', {privacy});
  }

  // Update account preferences
  async updateAccountPreferences(
    account: UserPreferences['account'],
  ): Promise<void> {
    await apiClient.put('/preferences/account', {account});
  }

  // Reset preferences to default
  async resetPreferences(): Promise<UserPreferences> {
    const response = await apiClient.post<UserPreferences>(
      '/preferences/reset',
    );
    return response.data!;
  }
}

// Export singleton instance
export const preferencesService = new PreferencesService();
