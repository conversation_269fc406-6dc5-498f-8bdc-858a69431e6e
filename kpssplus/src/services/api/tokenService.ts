import {apiClient} from './client';
import {PaginatedResponse} from './types';

export interface TokenBalance {
  user_id: string;
  balance: number;
  total_earned: number;
  total_spent: number;
  last_updated: string;
}

export interface TokenTransaction {
  id: string;
  user_id: string;
  type: 'earn' | 'spend' | 'purchase' | 'bonus';
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
  created_at: string;
}

export interface TokenPackage {
  id: string;
  name: string;
  description: string;
  token_amount: number;
  price: number;
  currency: string;
  bonus_tokens?: number;
  is_popular?: boolean;
  is_active: boolean;
}

export interface PurchaseTokensRequest {
  package_id: string;
  payment_method: string;
  payment_token?: string;
}

export interface SpendTokensRequest {
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
}

export interface EarnTokensRequest {
  amount: number;
  description: string;
  reference_id?: string;
  reference_type?: string;
}

export interface DailyBonusResponse {
  tokens_earned: number;
  streak_days: number;
  next_bonus_available: string;
  message: string;
}

export class TokenService {
  // Get token balance
  async getTokenBalance(): Promise<TokenBalance> {
    const response = await apiClient.get<TokenBalance>('/user/tokens');
    return response.data!;
  }

  // Get token transaction history
  async getTokenHistory(
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<TokenTransaction>> {
    const response = await apiClient.get<PaginatedResponse<TokenTransaction>>(
      `/user/tokens/history?page=${page}&limit=${limit}`,
    );
    return response.data!;
  }

  // Purchase tokens
  async purchaseTokens(request: PurchaseTokensRequest): Promise<{
    transaction_id: string;
    tokens_added: number;
    new_balance: number;
    message: string;
  }> {
    const response = await apiClient.post<{
      transaction_id: string;
      tokens_added: number;
      new_balance: number;
      message: string;
    }>('/user/tokens/purchase', request);
    return response.data!;
  }

  // Spend tokens
  async spendTokens(request: SpendTokensRequest): Promise<{
    transaction_id: string;
    tokens_spent: number;
    new_balance: number;
    message: string;
  }> {
    const response = await apiClient.post<{
      transaction_id: string;
      tokens_spent: number;
      new_balance: number;
      message: string;
    }>('/user/tokens/spend', request);
    return response.data!;
  }

  // Earn tokens
  async earnTokens(request: EarnTokensRequest): Promise<{
    transaction_id: string;
    tokens_earned: number;
    new_balance: number;
    message: string;
  }> {
    const response = await apiClient.post<{
      transaction_id: string;
      tokens_earned: number;
      new_balance: number;
      message: string;
    }>('/user/tokens/earn', request);
    return response.data!;
  }

  // Claim daily bonus
  async claimDailyBonus(): Promise<DailyBonusResponse> {
    const response = await apiClient.post<DailyBonusResponse>(
      '/user/tokens/daily-bonus',
    );
    return response.data!;
  }

  // Get token packages
  async getTokenPackages(): Promise<TokenPackage[]> {
    const response = await apiClient.get<TokenPackage[]>(
      '/user/tokens/packages',
      false, // Public endpoint
    );
    return response.data!;
  }
}

// Export singleton instance
export const tokenService = new TokenService();
