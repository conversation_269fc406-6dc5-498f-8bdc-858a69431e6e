import {apiClient} from './client';
import {ApiResponse, PaginatedResponse} from './types';

export interface Goal {
  id: string;
  user_id: string;
  title: string;
  description: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  target_value: number;
  current_value: number;
  unit: string;
  start_date: string;
  end_date: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  category:
    | 'study_time'
    | 'content_completion'
    | 'quiz_score'
    | 'streak'
    | 'custom';
  reward_points: number;
  created_at: string;
  updated_at: string;
}

export interface GoalProgress {
  goal_id: string;
  date: string;
  progress_value: number;
  notes?: string;
  created_at: string;
}

export interface GoalTemplate {
  id: string;
  title: string;
  description: string;
  target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
  suggested_target_value: number;
  unit: string;
  category:
    | 'study_time'
    | 'content_completion'
    | 'quiz_score'
    | 'streak'
    | 'custom';
  difficulty_level: 'beginner' | 'intermediate' | 'advanced';
  reward_points: number;
}

export interface GoalStats {
  total_goals: number;
  active_goals: number;
  completed_goals: number;
  completion_rate: number;
  total_points_earned: number;
  current_streak: number;
  longest_streak: number;
  average_completion_time: number;
}

export class GoalsService {
  async getGoals(
    status?: 'active' | 'completed' | 'paused' | 'cancelled',
    category?: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<PaginatedResponse<Goal>> {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });

      if (status) {
        queryParams.append('status', status);
      }
      if (category) {
        queryParams.append('category', category);
      }

      const response = await apiClient.get<PaginatedResponse<Goal>>(
        `/goals?${queryParams.toString()}`,
      );
      return response.data!;
    } catch (error) {
      console.error('Error fetching goals:', error);
      // Fallback to mock data
      return this.getMockGoals();
    }
  }

  async getGoal(goalId: string): Promise<Goal> {
    try {
      const response = await apiClient.get<ApiResponse<Goal>>(
        `/goals/${goalId}`,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching goal:', error);
      throw error;
    }
  }

  async createGoal(goalData: {
    title: string;
    description: string;
    target_type: 'daily' | 'weekly' | 'monthly' | 'custom';
    target_value: number;
    unit: string;
    start_date: string;
    end_date: string;
    priority: 'low' | 'medium' | 'high';
    category:
      | 'study_time'
      | 'content_completion'
      | 'quiz_score'
      | 'streak'
      | 'custom';
  }): Promise<Goal> {
    try {
      const response = await apiClient.post<ApiResponse<Goal>>(
        '/goals',
        goalData,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error creating goal:', error);
      throw error;
    }
  }

  async updateGoal(goalId: string, updates: Partial<Goal>): Promise<Goal> {
    try {
      const response = await apiClient.put<ApiResponse<Goal>>(
        `/goals/${goalId}`,
        updates,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error updating goal:', error);
      throw error;
    }
  }

  async deleteGoal(goalId: string): Promise<void> {
    try {
      await apiClient.delete(`/goals/${goalId}`);
    } catch (error) {
      console.error('Error deleting goal:', error);
      throw error;
    }
  }

  async updateGoalProgress(
    goalId: string,
    progressValue: number,
    notes?: string,
  ): Promise<GoalProgress> {
    try {
      const response = await apiClient.post<ApiResponse<GoalProgress>>(
        `/goals/${goalId}/progress`,
        {
          progress_value: progressValue,
          notes,
        },
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error updating goal progress:', error);
      throw error;
    }
  }

  async getGoalProgress(
    goalId: string,
    startDate?: string,
    endDate?: string,
  ): Promise<GoalProgress[]> {
    try {
      const queryParams = new URLSearchParams();

      if (startDate) {
        queryParams.append('start_date', startDate);
      }
      if (endDate) {
        queryParams.append('end_date', endDate);
      }

      const response = await apiClient.get<ApiResponse<GoalProgress[]>>(
        `/goals/${goalId}/progress?${queryParams.toString()}`,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching goal progress:', error);
      return [];
    }
  }

  async getGoalTemplates(): Promise<GoalTemplate[]> {
    try {
      const response = await apiClient.get<ApiResponse<GoalTemplate[]>>(
        '/goals/templates',
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching goal templates:', error);
      // Fallback to mock data
      return this.getMockGoalTemplates();
    }
  }

  async getGoalStats(): Promise<GoalStats> {
    try {
      const response = await apiClient.get<ApiResponse<GoalStats>>(
        '/goals/stats',
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error fetching goal stats:', error);
      // Fallback to mock data
      return this.getMockGoalStats();
    }
  }

  async completeGoal(goalId: string): Promise<Goal> {
    try {
      const response = await apiClient.post<ApiResponse<Goal>>(
        `/goals/${goalId}/complete`,
      );
      return response.data?.data!;
    } catch (error) {
      console.error('Error completing goal:', error);
      throw error;
    }
  }

  // Mock data methods for fallback
  private getMockGoals(): PaginatedResponse<Goal> {
    const mockGoals: Goal[] = [
      {
        id: '1',
        user_id: 'user1',
        title: 'Günlük 30 Dakika Çalışma',
        description: 'Her gün en az 30 dakika çalışma hedefi',
        target_type: 'daily',
        target_value: 1800, // 30 minutes in seconds
        current_value: 1200, // 20 minutes
        unit: 'saniye',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        priority: 'high',
        category: 'study_time',
        reward_points: 10,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:30:00Z',
      },
      {
        id: '2',
        user_id: 'user1',
        title: 'Haftalık 5 Quiz Tamamlama',
        description: 'Her hafta en az 5 quiz tamamlama hedefi',
        target_type: 'weekly',
        target_value: 5,
        current_value: 3,
        unit: 'quiz',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        priority: 'medium',
        category: 'quiz_score',
        reward_points: 25,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:30:00Z',
      },
      {
        id: '3',
        user_id: 'user1',
        title: '7 Günlük Çalışma Serisi',
        description: '7 gün üst üste çalışma serisi oluşturma',
        target_type: 'custom',
        target_value: 7,
        current_value: 4,
        unit: 'gün',
        start_date: '2024-01-01',
        end_date: '2024-01-31',
        status: 'active',
        priority: 'high',
        category: 'streak',
        reward_points: 50,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T10:30:00Z',
      },
    ];

    return {
      data: mockGoals,
      pagination: {
        page: 1,
        limit: 20,
        total: mockGoals.length,
        total_pages: 1,
        has_next: false,
        has_prev: false,
      },
    };
  }

  private getMockGoalTemplates(): GoalTemplate[] {
    return [
      {
        id: '1',
        title: 'Günlük Çalışma Hedefi',
        description: 'Her gün belirli süre çalışma',
        target_type: 'daily',
        suggested_target_value: 1800, // 30 minutes
        unit: 'saniye',
        category: 'study_time',
        difficulty_level: 'beginner',
        reward_points: 10,
      },
      {
        id: '2',
        title: 'Haftalık Quiz Hedefi',
        description: 'Haftalık quiz tamamlama hedefi',
        target_type: 'weekly',
        suggested_target_value: 5,
        unit: 'quiz',
        category: 'quiz_score',
        difficulty_level: 'intermediate',
        reward_points: 25,
      },
      {
        id: '3',
        title: 'Aylık İçerik Tamamlama',
        description: 'Aylık içerik tamamlama hedefi',
        target_type: 'monthly',
        suggested_target_value: 20,
        unit: 'içerik',
        category: 'content_completion',
        difficulty_level: 'advanced',
        reward_points: 100,
      },
    ];
  }

  private getMockGoalStats(): GoalStats {
    return {
      total_goals: 8,
      active_goals: 3,
      completed_goals: 4,
      completion_rate: 66.7,
      total_points_earned: 285,
      current_streak: 2,
      longest_streak: 5,
      average_completion_time: 18.5, // days
    };
  }
}

export const goalsService = new GoalsService();
