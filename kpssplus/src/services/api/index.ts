// Export service classes and instances
export {AuthService} from './authService';
export {ContentService} from './contentService';
export {QuizService} from './quizService';
export {SocialService} from './socialService';
export {BadgeService} from './badgeService';
export {ProgressService} from './progressService';
export {AnalyticsService} from './analyticsService';
export {GoalsService} from './goalsService';

// Export service instances (created in their respective files)
export {authService} from './authService';
export {contentService} from './contentService';
export {quizService} from './quizService';
export {socialService} from './socialService';
export {badgeService} from './badgeService';
export {progressService} from './progressService';
export {analyticsService} from './analyticsService';
export {goalsService} from './goalsService';
export {preferencesService} from './preferencesService';

// Export API client and config
export {apiClient, ApiError} from './client';
export {TokenManager, API_CONFIG, STORAGE_KEYS} from './config';

// Export types
export * from './types';

// Export API client class
export {ApiClient} from './client';
