import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
}

export const API_CONFIG: ApiConfig = {
  baseURL: __DEV__
    ? 'http://localhost:8080/api' // Development
    : 'https://api.kpssplus.com/api', // Production
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
};

export const STORAGE_KEYS = {
  AUTH_TOKEN: '@kpssplus_auth_token',
  USER_DATA: '@kpssplus_user_data',
  GUEST_TOKEN: '@kpssplus_guest_token',
  REFRESH_TOKEN: '@kpssplus_refresh_token',
} as const;

export class TokenManager {
  static async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error('Error getting token:', error);
      return null;
    }
  }

  static async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
    } catch (error) {
      console.error('Error setting token:', error);
    }
  }

  static async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN);
    } catch (error) {
      console.error('Error removing token:', error);
    }
  }

  static async getGuestToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.GUEST_TOKEN);
    } catch (error) {
      console.error('Error getting guest token:', error);
      return null;
    }
  }

  static async setGuestToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.GUEST_TOKEN, token);
    } catch (error) {
      console.error('Error setting guest token:', error);
    }
  }

  static async getUserData(): Promise<any | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  static async setUserData(userData: any): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_DATA,
        JSON.stringify(userData),
      );
    } catch (error) {
      console.error('Error setting user data:', error);
    }
  }

  static async clearAll(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.USER_DATA,
        STORAGE_KEYS.GUEST_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
      ]);
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }
}
