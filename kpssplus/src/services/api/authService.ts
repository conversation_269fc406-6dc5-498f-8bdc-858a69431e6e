import {apiClient} from './client';
import {TokenManager} from './config';
import {User} from '../../types';
import {
  LoginRequest,
  EmailLoginRequest,
  RegisterRequest,
  AuthResponse,
  AuthTokens,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  VerifyOTPRequest,
  UserInfo,
  // ApiResponse,
} from './types';

export class AuthService {
  // Login with username
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/username',
      credentials,
      false,
    );

    if (response.data) {
      await TokenManager.setToken(response.data.token);
      await TokenManager.setUserData(response.data.user);
    }

    return response.data!;
  }

  // Login with email
  async loginWithEmail(credentials: EmailLoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/email',
      credentials,
      false,
    );

    if (response.data) {
      await TokenManager.setToken(response.data.token);
      await TokenManager.setUserData(response.data.user);
    }

    return response.data!;
  }

  // Guest login
  async loginAsGuest(): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/guest',
      {},
      false,
    );

    if (response.data) {
      await TokenManager.setGuestToken(response.data.token);
    }

    return response.data!;
  }

  // Register new user
  async register(
    userData: RegisterRequest,
  ): Promise<{message: string; user_id: string}> {
    const response = await apiClient.post<{message: string; user_id: string}>(
      '/auth/register',
      userData,
      false,
    );

    return response.data!;
  }

  // Forgot password
  async forgotPassword(
    request: ForgotPasswordRequest,
  ): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      '/auth/forgot-password',
      request,
      false,
    );

    return response.data!;
  }

  // Reset password
  async resetPassword(
    request: ResetPasswordRequest,
  ): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      '/auth/reset-password',
      request,
      false,
    );

    return response.data!;
  }

  // Verify OTP
  async verifyOTP(request: VerifyOTPRequest): Promise<{message: string}> {
    const response = await apiClient.post<{message: string}>(
      '/auth/verify-otp',
      request,
      false,
    );

    return response.data!;
  }

  // Validate token
  async validateToken(token?: string): Promise<{
    valid: boolean;
    user_id: string;
    user_type: 'user' | 'admin';
    admin_id?: string;
  }> {
    const tokenToValidate = token || (await TokenManager.getToken());

    if (!tokenToValidate) {
      throw new Error('No token available');
    }

    const response = await apiClient.post<{
      valid: boolean;
      user_id: string;
      user_type: 'user' | 'admin';
      admin_id?: string;
    }>('/auth/validate-token', {token: tokenToValidate}, false);

    return response.data!;
  }

  // Get current user info
  async getCurrentUser(): Promise<UserInfo | null> {
    try {
      const userData = await TokenManager.getUserData();
      if (userData) {
        return userData;
      }

      // If no cached user data, validate token and get user info
      const tokenValidation = await this.validateToken();
      if (tokenValidation.valid) {
        // You might need to implement a separate endpoint to get user details
        // For now, return null and let the app handle it
        return null;
      }

      return null;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const token = await TokenManager.getToken();
      if (!token) {
        return false;
      }

      const validation = await this.validateToken(token);
      return validation.valid;
    } catch (error) {
      console.error('Error checking authentication:', error);
      return false;
    }
  }

  // Check if user is guest
  async isGuest(): Promise<boolean> {
    try {
      const guestToken = await TokenManager.getGuestToken();
      const userToken = await TokenManager.getToken();

      return !!guestToken && !userToken;
    } catch (error) {
      console.error('Error checking guest status:', error);
      return false;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await TokenManager.clearAll();
    } catch (error) {
      console.error('Error during logout:', error);
      throw error;
    }
  }

  // Refresh token (if you implement refresh token logic)
  async refreshToken(): Promise<AuthResponse> {
    // This would need to be implemented based on your backend refresh token logic
    throw new Error('Refresh token not implemented');
  }

  // Social login methods
  async loginWithGoogle(
    googleToken: string,
    name: string,
    email: string,
    username?: string,
  ): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/google',
      {
        google_token: googleToken,
        name,
        email,
        username,
      },
      false,
    );

    if (response.data) {
      await TokenManager.setToken(response.data.token);
      await TokenManager.setUserData(response.data.user);
    }

    return response.data!;
  }

  async loginWithApple(
    appleToken: string,
    name: string,
    email?: string,
    username?: string,
  ): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/apple',
      {
        apple_token: appleToken,
        name,
        email,
        username,
      },
      false,
    );

    if (response.data) {
      await TokenManager.setToken(response.data.token);
      await TokenManager.setUserData(response.data.user);
    }

    return response.data!;
  }

  // Get current auth tokens
  async getAuthTokens(): Promise<AuthTokens | null> {
    const token = await TokenManager.getToken();
    if (!token) {
      return null;
    }

    return {
      token,
      expires: '', // We don't store expires separately in TokenManager
    };
  }

  // Profile management methods
  async getProfile(): Promise<UserInfo> {
    const response = await apiClient.get<UserInfo>('/user/profile');
    return response.data!;
  }

  async updateProfile(updates: {
    firstName?: string;
    lastName?: string;
    phone?: string;
    push_notification_enabled?: boolean;
  }): Promise<UserInfo> {
    const response = await apiClient.put<UserInfo>('/user/profile', updates);

    // Update stored user data
    if (response.data) {
      await TokenManager.setUserData(response.data as unknown as User);
    }

    return response.data!;
  }
}

// Export singleton instance
export const authService = new AuthService();
