/**
 * @format
 */

import {authService} from '../../src/services/api/authService';
import {it, describe, expect, beforeEach, jest} from '@jest/globals';

// Mock axios
jest.mock('axios');

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            user: {
              id: '1',
              username: 'testuser',
              email: '<EMAIL>',
            },
            token: 'mock-token',
          },
        },
      };

      // Mock axios post
      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      const result = await authService.login({
        username: 'testuser',
        password: 'password123',
      });

      expect(result).toEqual(mockResponse.data.data);
      expect(axios.post).toHaveBeenCalledWith('/auth/login/username', {
        username: 'testuser',
        password: 'password123',
      });
    });

    it('should throw error with invalid credentials', async () => {
      const mockError = {
        response: {
          data: {
            success: false,
            message: 'Invalid credentials',
          },
        },
      };

      const axios = require('axios');
      axios.post.mockRejectedValue(mockError);

      await expect(
        authService.login({
          username: 'invaliduser',
          password: 'wrongpassword',
        }),
      ).rejects.toThrow('Invalid credentials');
    });
  });

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            user: {
              id: '1',
              username: 'newuser',
              email: '<EMAIL>',
            },
            token: 'mock-token',
          },
        },
      };

      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      const result = await authService.register({
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
      });

      expect(result).toEqual(mockResponse.data.data);
      expect(axios.post).toHaveBeenCalledWith('/auth/register', {
        username: 'newuser',
        email: '<EMAIL>',
        password: 'password123',
        name: 'New User',
      });
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          data: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
          },
        },
      };

      const axios = require('axios');
      axios.get.mockResolvedValue(mockResponse);

      const result = await authService.getCurrentUser();

      expect(result).toEqual(mockResponse.data.data);
      expect(axios.get).toHaveBeenCalledWith('/auth/me');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Logged out successfully',
        },
      };

      const axios = require('axios');
      axios.post.mockResolvedValue(mockResponse);

      await authService.logout();

      expect(axios.post).toHaveBeenCalledWith('/auth/logout');
    });
  });
});
