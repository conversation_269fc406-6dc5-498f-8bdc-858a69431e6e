/**
 * @format
 */

import 'react-native';
import React from 'react';
import {DashboardScreen} from '../../src/screens/common/DashboardScreen';

// Note: import explicitly to use the types shipped with jest.
import {it, describe, expect} from '@jest/globals';

// Note: test renderer must be required after react-native.
import renderer from 'react-test-renderer';

describe('DashboardScreen', () => {
  it('renders correctly', () => {
    const tree = renderer.create(<DashboardScreen />);
    expect(tree).toMatchSnapshot();
  });

  it('displays welcome message', () => {
    const tree = renderer.create(<DashboardScreen />);
    const instance = tree.root;

    // Check if welcome message exists
    const welcomeText = instance.findByProps({testID: 'welcome-message'});
    expect(welcomeText).toBeDefined();
  });

  it('displays user stats', () => {
    const tree = renderer.create(<DashboardScreen />);
    const instance = tree.root;

    // Check if stats section exists
    const statsSection = instance.findByProps({testID: 'stats-section'});
    expect(statsSection).toBeDefined();
  });

  it('displays daily questions card', () => {
    const tree = renderer.create(<DashboardScreen />);
    const instance = tree.root;

    // Check if daily questions card exists
    const dailyQuestionsCard = instance.findByProps({
      testID: 'daily-questions-card',
    });
    expect(dailyQuestionsCard).toBeDefined();
  });

  it('displays engage and compete section', () => {
    const tree = renderer.create(<DashboardScreen />);
    const instance = tree.root;

    // Check if engage section exists
    const engageSection = instance.findByProps({testID: 'engage-section'});
    expect(engageSection).toBeDefined();
  });
});
