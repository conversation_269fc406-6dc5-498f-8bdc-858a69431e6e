/**
 * @format
 */

import 'react-native';
import React from 'react';
import {ProfileScreen} from '../../src/screens/menu/ProfileScreen';

// Note: import explicitly to use the types shipped with jest.
import {it, describe, expect} from '@jest/globals';

// Note: test renderer must be required after react-native.
import renderer from 'react-test-renderer';

describe('ProfileScreen', () => {
  it('renders correctly', () => {
    const tree = renderer.create(<ProfileScreen />);
    expect(tree).toMatchSnapshot();
  });

  it('displays profile header', () => {
    const tree = renderer.create(<ProfileScreen />);
    const instance = tree.root;

    // Check if profile header exists
    const profileHeader = instance.findByProps({testID: 'profile-header'});
    expect(profileHeader).toBeDefined();
  });

  it('displays user avatar and info', () => {
    const tree = renderer.create(<ProfileScreen />);
    const instance = tree.root;

    // Check if user info section exists
    const userInfo = instance.findByProps({testID: 'user-info'});
    expect(userInfo).toBeDefined();
  });

  it('displays menu items', () => {
    const tree = renderer.create(<ProfileScreen />);
    const instance = tree.root;

    // Check if menu items exist
    const menuItems = instance.findByProps({testID: 'menu-items'});
    expect(menuItems).toBeDefined();
  });

  it('displays edit profile button', () => {
    const tree = renderer.create(<ProfileScreen />);
    const instance = tree.root;

    // Check if edit button exists
    const editButton = instance.findByProps({testID: 'edit-profile-button'});
    expect(editButton).toBeDefined();
  });

  it('displays bottom navigation', () => {
    const tree = renderer.create(<ProfileScreen />);
    const instance = tree.root;

    // Check if bottom navigation exists
    const bottomNav = instance.findByProps({testID: 'bottom-navigation'});
    expect(bottomNav).toBeDefined();
  });
});
