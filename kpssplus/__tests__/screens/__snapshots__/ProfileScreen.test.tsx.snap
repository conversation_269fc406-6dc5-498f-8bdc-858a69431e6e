// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`ProfileScreen renders correctly 1`] = `
<View
  style={
    {
      "backgroundColor": "#FFFFFF",
      "flex": 1,
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
        "backgroundColor": "#FFFFFF",
        "flexDirection": "row",
        "justifyContent": "space-between",
        "paddingBottom": 8,
        "paddingHorizontal": 16,
        "paddingVertical": 16,
      }
    }
  >
    <Text
      style={
        {
          "color": "#1E293B",
          "flex": 1,
          "fontFamily": "Be Vietnam Pro",
          "fontSize": 18,
          "fontWeight": "bold",
          "paddingLeft": 48,
          "textAlign": "center",
        }
      }
    >
      Profile
    </Text>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={true}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "height": 48,
          "justifyContent": "center",
          "opacity": 1,
          "width": 48,
        }
      }
    >
      <Icon
        color="#1E293B"
        name="settings"
        size={24}
      />
    </View>
  </View>
  <RCTScrollView
    showsVerticalScrollIndicator={false}
    style={
      {
        "flex": 1,
      }
    }
  >
    <View>
      <View
        style={
          {
            "paddingHorizontal": 16,
          }
        }
      >
        <View
          accessibilityIgnoresInvertColors={true}
          style={
            {
              "justifyContent": "flex-end",
              "minHeight": 218,
              "width": "100%",
            }
          }
        >
          <Image
            source={
              {
                "uri": "https://lh3.googleusercontent.com/aida-public/AB6AXuDh6blg1PRDHyI0iktBx_PUOkaa1ub27eZPkDt85JZcrgZkYbjbvs_CbKKMs-myrnF1LQrIl9QOHkGvWKsiR_PWqpaUKaRftnQX8X2VuaObE3dMvaQ9wnkahkYlfnxaYF6uixZ6OKueUxh4LWZAprgvdq-JfryyTfKfOv4uTaBH_b_EY63AEhNU-Y2s90YtbizDaVURlz9OwqiT3QROSixyDcldRdoi2ExyfXH7FPlyqSpEL48NX-V_a8akEmNzz6dCrwMpK9P_cW8",
              }
            }
            style={
              [
                {
                  "bottom": 0,
                  "left": 0,
                  "position": "absolute",
                  "right": 0,
                  "top": 0,
                },
                {
                  "height": undefined,
                  "width": "100%",
                },
                {
                  "borderRadius": 12,
                },
              ]
            }
          />
        </View>
      </View>
      <View
        style={
          {
            "marginTop": -64,
            "paddingHorizontal": 16,
          }
        }
      >
        <View
          style={
            {
              "alignItems": "center",
              "gap": 16,
              "width": "100%",
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "gap": 16,
              }
            }
          >
            <Image
              source={
                {
                  "uri": "https://lh3.googleusercontent.com/aida-public/AB6AXuA4BhHD9tJA0buY8UZHNS0MXtD0elYfenQcEN6Sh1iyXd4c00t03P3WWzLbZ8dMyBqLWkTL2fE4qB8rdkHK7xLWIFKedTOBcTPBFZkEDz3kKogXktT4il2em4VRjJBOGQO8IbkNDGkasgH6Z6Omn_XX4wTdJtLXl4QWWS1qr2VNV-qlYX0d4w7lfrli-oePoa6UaGn6xT4ljQIeLOX2wo2VX7GfE7A6VnPz-PB24pZ9b7jRZN4BvdcLZkdnE3_a0eE4ejFJB72sPII",
                }
              }
              style={
                {
                  "borderColor": "#FFFFFF",
                  "borderRadius": 64,
                  "borderWidth": 4,
                  "elevation": 8,
                  "height": 128,
                  "shadowColor": "#000",
                  "shadowOffset": {
                    "height": 4,
                    "width": 0,
                  },
                  "shadowOpacity": 0.25,
                  "shadowRadius": 8,
                  "width": 128,
                }
              }
            />
            <View
              style={
                {
                  "alignItems": "center",
                  "paddingTop": 8,
                }
              }
            >
              <Text
                style={
                  {
                    "color": "#1E293B",
                    "fontFamily": "Be Vietnam Pro",
                    "fontSize": 24,
                    "fontWeight": "bold",
                    "textAlign": "center",
                  }
                }
              >
                Emre K.
              </Text>
              <Text
                style={
                  {
                    "color": "#64748B",
                    "fontFamily": "Be Vietnam Pro",
                    "fontSize": 16,
                    "textAlign": "center",
                  }
                }
              >
                @emre_k
              </Text>
              <Text
                style={
                  {
                    "color": "#4F46E5",
                    "fontFamily": "Be Vietnam Pro",
                    "fontSize": 18,
                    "fontWeight": "bold",
                    "marginTop": 8,
                    "textAlign": "center",
                  }
                }
              >
                12,450 Puan
              </Text>
            </View>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "gap": 12,
            "marginTop": 24,
            "paddingHorizontal": 16,
          }
        }
      >
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#F8FAFC",
              "borderRadius": 12,
              "flexDirection": "row",
              "gap": 16,
              "opacity": 1,
              "padding": 16,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "#E2E8F0",
                "borderRadius": 8,
                "height": 40,
                "justifyContent": "center",
                "width": 40,
              }
            }
          >
            <Icon
              color="#1E293B"
              name="workspace-premium"
              size={24}
            />
          </View>
          <Text
            style={
              {
                "color": "#1E293B",
                "flex": 1,
                "fontFamily": "Be Vietnam Pro",
                "fontSize": 16,
                "fontWeight": "500",
              }
            }
          >
            Badges
          </Text>
          <Icon
            color="#94A3B8"
            name="chevron-right"
            size={24}
          />
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#F8FAFC",
              "borderRadius": 12,
              "flexDirection": "row",
              "gap": 16,
              "opacity": 1,
              "padding": 16,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "flex": 1,
                "flexDirection": "row",
                "gap": 16,
              }
            }
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#E2E8F0",
                  "borderRadius": 8,
                  "height": 40,
                  "justifyContent": "center",
                  "width": 40,
                }
              }
            >
              <Icon
                color="#1E293B"
                name="monetization-on"
                size={24}
              />
            </View>
            <Text
              style={
                {
                  "color": "#1E293B",
                  "flex": 1,
                  "fontFamily": "Be Vietnam Pro",
                  "fontSize": 16,
                  "fontWeight": "500",
                }
              }
            >
              Tokens
            </Text>
          </View>
          <Text
            style={
              {
                "color": "#1E293B",
                "fontFamily": "Be Vietnam Pro",
                "fontSize": 16,
                "fontWeight": "bold",
              }
            }
          >
            250
          </Text>
        </View>
      </View>
      <View
        style={
          {
            "alignItems": "center",
            "paddingHorizontal": 16,
            "paddingVertical": 24,
          }
        }
      >
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={true}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#4F46E5",
              "borderRadius": 8,
              "height": 48,
              "justifyContent": "center",
              "opacity": 1,
              "paddingHorizontal": 16,
              "width": "100%",
            }
          }
        >
          <Text
            style={
              {
                "color": "#FFFFFF",
                "fontFamily": "Be Vietnam Pro",
                "fontSize": 16,
                "fontWeight": "bold",
              }
            }
          >
            Edit Profile
          </Text>
        </View>
      </View>
    </View>
  </RCTScrollView>
  <View
    style={
      {
        "backgroundColor": "#FFFFFF",
        "borderTopColor": "#E2E8F0",
        "borderTopWidth": 1,
        "flexDirection": "row",
        "gap": 8,
        "paddingBottom": 12,
        "paddingHorizontal": 16,
        "paddingTop": 8,
      }
    }
  >
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "flex": 1,
          "gap": 4,
          "opacity": 1,
        }
      }
    >
      <Icon
        color="#64748B"
        name="home"
        size={24}
      />
      <Text
        style={
          {
            "color": "#64748B",
            "fontFamily": "Be Vietnam Pro",
            "fontSize": 12,
            "fontWeight": "500",
          }
        }
      >
        Home
      </Text>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "flex": 1,
          "gap": 4,
          "opacity": 1,
        }
      }
    >
      <Icon
        color="#64748B"
        name="leaderboard"
        size={24}
      />
      <Text
        style={
          {
            "color": "#64748B",
            "fontFamily": "Be Vietnam Pro",
            "fontSize": 12,
            "fontWeight": "500",
          }
        }
      >
        Leaderboard
      </Text>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "flex": 1,
          "gap": 4,
          "opacity": 1,
        }
      }
    >
      <Icon
        color="#64748B"
        name="checklist"
        size={24}
      />
      <Text
        style={
          {
            "color": "#64748B",
            "fontFamily": "Be Vietnam Pro",
            "fontSize": 12,
            "fontWeight": "500",
          }
        }
      >
        Test
      </Text>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "flex": 1,
          "gap": 4,
          "opacity": 1,
        }
      }
    >
      <Icon
        color="#64748B"
        name="article"
        size={24}
      />
      <Text
        style={
          {
            "color": "#64748B",
            "fontFamily": "Be Vietnam Pro",
            "fontSize": 12,
            "fontWeight": "500",
          }
        }
      >
        Mock Exams
      </Text>
    </View>
    <View
      accessibilityState={
        {
          "busy": undefined,
          "checked": undefined,
          "disabled": undefined,
          "expanded": undefined,
          "selected": undefined,
        }
      }
      accessibilityValue={
        {
          "max": undefined,
          "min": undefined,
          "now": undefined,
          "text": undefined,
        }
      }
      accessible={true}
      collapsable={false}
      focusable={false}
      onClick={[Function]}
      onResponderGrant={[Function]}
      onResponderMove={[Function]}
      onResponderRelease={[Function]}
      onResponderTerminate={[Function]}
      onResponderTerminationRequest={[Function]}
      onStartShouldSetResponder={[Function]}
      style={
        {
          "alignItems": "center",
          "flex": 1,
          "gap": 4,
          "opacity": 1,
        }
      }
    >
      <Icon
        color="#4F46E5"
        name="person"
        size={24}
      />
      <Text
        style={
          {
            "color": "#4F46E5",
            "fontFamily": "Be Vietnam Pro",
            "fontSize": 12,
            "fontWeight": "500",
          }
        }
      >
        Profile
      </Text>
    </View>
  </View>
</View>
`;
