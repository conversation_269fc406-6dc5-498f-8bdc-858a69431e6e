// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DashboardScreen renders correctly 1`] = `
<View
  style={
    {
      "backgroundColor": "#F9FAFB",
      "flex": 1,
    }
  }
>
  <RCTScrollView
    showsVerticalScrollIndicator={false}
    style={
      {
        "flex": 1,
      }
    }
  >
    <View>
      <View
        style={
          {
            "alignItems": "center",
            "backgroundColor": "rgba(255, 255, 255, 0.8)",
            "flexDirection": "row",
            "justifyContent": "space-between",
            "paddingHorizontal": 16,
            "paddingVertical": 16,
          }
        }
      >
        <View
          style={
            {
              "alignItems": "center",
              "flexDirection": "row",
              "gap": 12,
            }
          }
        >
          <View
            style={
              {
                "borderRadius": 20,
                "height": 40,
                "overflow": "hidden",
                "width": 40,
              }
            }
          >
            <Image
              source={
                {
                  "uri": "https://lh3.googleusercontent.com/aida-public/AB6AXuBRSqewATh2qZpuSQ02xRJ92uAnOjnpyhu4Q_GXcEAmxRhE6z53cyt6zHZ1PDDnHb8izQkVILWhRZkBNE43vSygivIoGpVS3HR_monLP-K9P8P6MNqPL7srXfMgrhy5VYIPpDjbsoKd8fqq6sRe31n0Zqp61DyfrZeKn3wdq4Tkj4nvhX4N345LRJYYIz19KOZm_xyPi1mLcWjYpxNTRXS6RBsUUOxa9GYuzNK7vL1s6wstj3y8LX2Q3j82WEuuQfDjQFbOVKVIcY0",
                }
              }
              style={
                {
                  "borderRadius": 20,
                  "height": "100%",
                  "width": "100%",
                }
              }
            />
          </View>
          <View
            style={
              {
                "gap": 2,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#9CA3AF",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                }
              }
            >
              Welcome back,
            </Text>
            <Text
              style={
                {
                  "color": "#1F2937",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 18,
                  "fontWeight": "bold",
                  "lineHeight": 22,
                }
              }
            >
              Sophia
              !
            </Text>
          </View>
        </View>
        <View
          style={
            {
              "alignItems": "center",
              "flexDirection": "row",
              "gap": 8,
            }
          }
        >
          <View
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": undefined,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              {
                "alignItems": "center",
                "backgroundColor": "transparent",
                "borderRadius": 20,
                "height": 40,
                "justifyContent": "center",
                "opacity": 1,
                "width": 40,
              }
            }
          >
            <Icon
              color="#1F2937"
              name="notifications"
              size={24}
            />
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "rgba(255, 145, 77, 0.1)",
                "borderRadius": 20,
                "flexDirection": "row",
                "gap": 4,
                "paddingHorizontal": 12,
                "paddingVertical": 6,
              }
            }
          >
            <Icon
              color="#FF914D"
              name="local-fire-department"
              size={18}
            />
            <Text
              style={
                {
                  "color": "#FF914D",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 14,
                  "fontWeight": "bold",
                }
              }
            >
              3
            </Text>
          </View>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "#4A47A3",
            "borderRadius": 12,
            "elevation": 8,
            "marginBottom": 24,
            "marginHorizontal": 16,
            "padding": 20,
            "shadowColor": "#000",
            "shadowOffset": {
              "height": 4,
              "width": 0,
            },
            "shadowOpacity": 0.15,
            "shadowRadius": 8,
          }
        }
      >
        <View
          style={
            {
              "alignItems": "flex-start",
              "flexDirection": "row",
              "justifyContent": "space-between",
            }
          }
        >
          <View
            style={
              {
                "gap": 4,
              }
            }
          >
            <Text
              style={
                {
                  "color": "rgba(255, 255, 255, 0.8)",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                }
              }
            >
              Your Rank
            </Text>
            <Text
              style={
                {
                  "color": "#FFFFFF",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 30,
                  "fontWeight": "bold",
                }
              }
            >
              12
            </Text>
          </View>
          <View
            style={
              {
                "alignItems": "flex-end",
                "gap": 8,
              }
            }
          >
            <View
              style={
                {
                  "alignItems": "center",
                  "flexDirection": "row",
                  "gap": 8,
                }
              }
            >
              <Icon
                color="#FF914D"
                name="monetization-on"
                size={20}
              />
              <Text
                style={
                  {
                    "color": "#FFFFFF",
                    "fontFamily": "Lexend-SemiBold",
                    "fontSize": 18,
                    "fontWeight": "600",
                  }
                }
              >
                1200 Coins
              </Text>
            </View>
            <View
              style={
                {
                  "alignItems": "center",
                  "flexDirection": "row",
                  "gap": 8,
                }
              }
            >
              <Icon
                color="#2DD4BF"
                name="star"
                size={20}
              />
              <Text
                style={
                  {
                    "color": "#FFFFFF",
                    "fontFamily": "Lexend-SemiBold",
                    "fontSize": 18,
                    "fontWeight": "600",
                  }
                }
              >
                2500 Points
              </Text>
            </View>
          </View>
        </View>
        <View
          style={
            {
              "gap": 4,
              "marginTop": 16,
            }
          }
        >
          <Text
            style={
              {
                "color": "rgba(255, 255, 255, 0.8)",
                "fontFamily": "Lexend-Regular",
                "fontSize": 14,
              }
            }
          >
            Weekly Progress
          </Text>
          <View
            style={
              {
                "backgroundColor": "rgba(255, 255, 255, 0.2)",
                "borderRadius": 5,
                "height": 10,
                "overflow": "hidden",
                "width": "100%",
              }
            }
          >
            <View
              style={
                [
                  {
                    "backgroundColor": "#FF914D",
                    "borderRadius": 5,
                    "height": "100%",
                  },
                  {
                    "width": "75%",
                  },
                ]
              }
            />
          </View>
        </View>
      </View>
      <View
        style={
          {
            "backgroundColor": "#FFFFFF",
            "borderColor": "#E5E7EB",
            "borderRadius": 12,
            "borderWidth": 1,
            "elevation": 2,
            "marginBottom": 24,
            "marginHorizontal": 16,
            "padding": 16,
            "shadowColor": "#000",
            "shadowOffset": {
              "height": 1,
              "width": 0,
            },
            "shadowOpacity": 0.05,
            "shadowRadius": 2,
          }
        }
      >
        <View
          style={
            {
              "alignItems": "center",
              "flexDirection": "row",
              "justifyContent": "space-between",
              "marginBottom": 16,
            }
          }
        >
          <View
            style={
              {
                "flex": 1,
                "gap": 4,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#1F2937",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 18,
                  "fontWeight": "bold",
                }
              }
            >
              Daily Questions
            </Text>
            <Text
              style={
                {
                  "color": "#374151",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                }
              }
            >
              Complete 5 questions to earn 50 coins!
            </Text>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "#FF914D",
                "borderRadius": 32,
                "height": 64,
                "justifyContent": "center",
                "width": 64,
              }
            }
          >
            <Icon
              color="#FFFFFF"
              name="lightbulb"
              size={32}
            />
          </View>
        </View>
        <View
          accessibilityState={
            {
              "busy": undefined,
              "checked": undefined,
              "disabled": undefined,
              "expanded": undefined,
              "selected": undefined,
            }
          }
          accessibilityValue={
            {
              "max": undefined,
              "min": undefined,
              "now": undefined,
              "text": undefined,
            }
          }
          accessible={true}
          collapsable={false}
          focusable={false}
          onClick={[Function]}
          onResponderGrant={[Function]}
          onResponderMove={[Function]}
          onResponderRelease={[Function]}
          onResponderTerminate={[Function]}
          onResponderTerminationRequest={[Function]}
          onStartShouldSetResponder={[Function]}
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#FF914D",
              "borderRadius": 8,
              "flexDirection": "row",
              "gap": 8,
              "justifyContent": "center",
              "opacity": 1,
              "paddingHorizontal": 16,
              "paddingVertical": 12,
            }
          }
        >
          <Text
            style={
              {
                "color": "#FFFFFF",
                "fontFamily": "Lexend-Bold",
                "fontSize": 16,
                "fontWeight": "bold",
              }
            }
          >
            Start Now
          </Text>
          <Icon
            color="#FFFFFF"
            name="arrow-forward"
            size={20}
          />
        </View>
      </View>
      <View
        style={
          {
            "gap": 12,
            "paddingHorizontal": 16,
          }
        }
      >
        <Text
          style={
            {
              "color": "#1F2937",
              "fontFamily": "Lexend-Bold",
              "fontSize": 18,
              "fontWeight": "bold",
              "paddingBottom": 12,
            }
          }
        >
          Engage & Compete
        </Text>
        <View
          style={
            {
              "flexDirection": "row",
              "gap": 16,
              "marginBottom": 16,
            }
          }
        >
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "#FFFFFF",
                "borderColor": "#E5E7EB",
                "borderRadius": 12,
                "borderWidth": 1,
                "elevation": 2,
                "flex": 1,
                "padding": 16,
                "shadowColor": "#000",
                "shadowOffset": {
                  "height": 1,
                  "width": 0,
                },
                "shadowOpacity": 0.05,
                "shadowRadius": 2,
              }
            }
          >
            <View
              style={
                [
                  {
                    "alignItems": "center",
                    "borderRadius": 32,
                    "height": 64,
                    "justifyContent": "center",
                    "marginBottom": 12,
                    "width": 64,
                  },
                  {
                    "backgroundColor": "#2DD4BF",
                  },
                ]
              }
            >
              <Icon
                color="#FFFFFF"
                name="groups"
                size={32}
              />
            </View>
            <Text
              style={
                {
                  "color": "#1F2937",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 16,
                  "fontWeight": "bold",
                  "marginBottom": 4,
                }
              }
            >
              Group Battle
            </Text>
            <Text
              style={
                {
                  "color": "#9CA3AF",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                  "marginBottom": 12,
                }
              }
            >
              Join a group
            </Text>
            <View
              accessibilityState={
                {
                  "busy": undefined,
                  "checked": undefined,
                  "disabled": undefined,
                  "expanded": undefined,
                  "selected": undefined,
                }
              }
              accessibilityValue={
                {
                  "max": undefined,
                  "min": undefined,
                  "now": undefined,
                  "text": undefined,
                }
              }
              accessible={true}
              collapsable={false}
              focusable={false}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#2DD4BF",
                  "borderRadius": 8,
                  "opacity": 1,
                  "paddingHorizontal": 16,
                  "paddingVertical": 8,
                  "width": "100%",
                }
              }
            >
              <Text
                style={
                  {
                    "color": "#FFFFFF",
                    "fontFamily": "Lexend-Medium",
                    "fontSize": 14,
                    "fontWeight": "500",
                  }
                }
              >
                Join
              </Text>
            </View>
          </View>
          <View
            style={
              {
                "alignItems": "center",
                "backgroundColor": "#FFFFFF",
                "borderColor": "#E5E7EB",
                "borderRadius": 12,
                "borderWidth": 1,
                "elevation": 2,
                "flex": 1,
                "padding": 16,
                "shadowColor": "#000",
                "shadowOffset": {
                  "height": 1,
                  "width": 0,
                },
                "shadowOpacity": 0.05,
                "shadowRadius": 2,
              }
            }
          >
            <View
              style={
                [
                  {
                    "alignItems": "center",
                    "borderRadius": 32,
                    "height": 64,
                    "justifyContent": "center",
                    "marginBottom": 12,
                    "width": 64,
                  },
                  {
                    "backgroundColor": "#F472B6",
                  },
                ]
              }
            >
              <Icon
                color="#FFFFFF"
                name="sports-martial-arts"
                size={32}
              />
            </View>
            <Text
              style={
                {
                  "color": "#1F2937",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 16,
                  "fontWeight": "bold",
                  "marginBottom": 4,
                }
              }
            >
              1v1 Battle
            </Text>
            <Text
              style={
                {
                  "color": "#9CA3AF",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                  "marginBottom": 12,
                }
              }
            >
              Challenge a friend
            </Text>
            <View
              accessibilityState={
                {
                  "busy": undefined,
                  "checked": undefined,
                  "disabled": undefined,
                  "expanded": undefined,
                  "selected": undefined,
                }
              }
              accessibilityValue={
                {
                  "max": undefined,
                  "min": undefined,
                  "now": undefined,
                  "text": undefined,
                }
              }
              accessible={true}
              collapsable={false}
              focusable={false}
              onClick={[Function]}
              onResponderGrant={[Function]}
              onResponderMove={[Function]}
              onResponderRelease={[Function]}
              onResponderTerminate={[Function]}
              onResponderTerminationRequest={[Function]}
              onStartShouldSetResponder={[Function]}
              style={
                {
                  "alignItems": "center",
                  "backgroundColor": "#F472B6",
                  "borderRadius": 8,
                  "opacity": 1,
                  "paddingHorizontal": 16,
                  "paddingVertical": 8,
                  "width": "100%",
                }
              }
            >
              <Text
                style={
                  {
                    "color": "#FFFFFF",
                    "fontFamily": "Lexend-Medium",
                    "fontSize": 14,
                    "fontWeight": "500",
                  }
                }
              >
                Challenge
              </Text>
            </View>
          </View>
        </View>
        <View
          style={
            {
              "alignItems": "center",
              "backgroundColor": "#FFFFFF",
              "borderColor": "#E5E7EB",
              "borderRadius": 12,
              "borderWidth": 1,
              "elevation": 2,
              "flexDirection": "row",
              "justifyContent": "space-between",
              "padding": 16,
              "shadowColor": "#000",
              "shadowOffset": {
                "height": 1,
                "width": 0,
              },
              "shadowOpacity": 0.05,
              "shadowRadius": 2,
            }
          }
        >
          <View
            style={
              {
                "gap": 4,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#1F2937",
                  "fontFamily": "Lexend-Bold",
                  "fontSize": 16,
                  "fontWeight": "bold",
                }
              }
            >
              Quiz Bot
            </Text>
            <Text
              style={
                {
                  "color": "#9CA3AF",
                  "fontFamily": "Lexend-Regular",
                  "fontSize": 14,
                }
              }
            >
              Test your knowledge
            </Text>
          </View>
          <View
            accessibilityState={
              {
                "busy": undefined,
                "checked": undefined,
                "disabled": undefined,
                "expanded": undefined,
                "selected": undefined,
              }
            }
            accessibilityValue={
              {
                "max": undefined,
                "min": undefined,
                "now": undefined,
                "text": undefined,
              }
            }
            accessible={true}
            collapsable={false}
            focusable={false}
            onClick={[Function]}
            onResponderGrant={[Function]}
            onResponderMove={[Function]}
            onResponderRelease={[Function]}
            onResponderTerminate={[Function]}
            onResponderTerminationRequest={[Function]}
            onStartShouldSetResponder={[Function]}
            style={
              {
                "backgroundColor": "#4A47A3",
                "borderRadius": 8,
                "opacity": 1,
                "paddingHorizontal": 20,
                "paddingVertical": 10,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#FFFFFF",
                  "fontFamily": "Lexend-Medium",
                  "fontSize": 14,
                  "fontWeight": "500",
                }
              }
            >
              Start
            </Text>
          </View>
        </View>
      </View>
    </View>
  </RCTScrollView>
</View>
`;
