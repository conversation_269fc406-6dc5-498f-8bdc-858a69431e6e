import { FormEvent, useEffect, useMemo, useState } from 'react';
import {
  AuthResponse,
  AuthUser,
  Explanation,
  ReadStatus,
  Subject,
  fetchChildSubjects,
  fetchExplanation,
  fetchExplanations,
  fetchProfile,
  fetchRootSubjects,
  login,
  markExplanationRead,
  register,
} from './api';
import './App.css';

interface AuthState {
  token: string;
  user: AuthUser;
}

const LOCAL_STORAGE_TOKEN_KEY = 'kpss_plus_token';
const LOCAL_STORAGE_USER_KEY = 'kpss_plus_user';

function App() {
  const [auth, setAuth] = useState<AuthState | null>(() => {
    const storedToken = localStorage.getItem(LOCAL_STORAGE_TOKEN_KEY);
    const storedUser = localStorage.getItem(LOCAL_STORAGE_USER_KEY);
    if (storedToken && storedUser) {
      try {
        return { token: storedToken, user: JSON.parse(storedUser) as AuthUser };
      } catch (err) {
        console.warn('Failed to parse stored user', err);
      }
    }
    return null;
  });

  const [rootSubjects, setRootSubjects] = useState<Subject[]>([]);
  const [childSubjects, setChildSubjects] = useState<Subject[]>([]);
  const [explanations, setExplanations] = useState<Explanation[]>([]);
  const [selectedRootSubject, setSelectedRootSubject] = useState<string | null>(null);
  const [selectedChildSubject, setSelectedChildSubject] = useState<string | null>(null);
  const [selectedExplanation, setSelectedExplanation] = useState<Explanation | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isRegisterMode, setIsRegisterMode] = useState(false);

  const token = auth?.token ?? null;

  useEffect(() => {
    if (!auth) {
      return;
    }

    localStorage.setItem(LOCAL_STORAGE_TOKEN_KEY, auth.token);
    localStorage.setItem(LOCAL_STORAGE_USER_KEY, JSON.stringify(auth.user));
  }, [auth]);

  useEffect(() => {
    if (!token) {
      return;
    }

    setIsLoading(true);
    fetchRootSubjects(token)
      .then((subjects) => {
        setRootSubjects(subjects.sort((a, b) => a.sort_order - b.sort_order));
        if (subjects.length > 0) {
          setSelectedRootSubject(subjects[0].id);
        }
      })
      .catch((err) => setError(err.message))
      .finally(() => setIsLoading(false));
  }, [token]);

  useEffect(() => {
    if (!selectedRootSubject) {
      setChildSubjects([]);
      setExplanations([]);
      return;
    }

    setIsLoading(true);
    Promise.all([
      fetchChildSubjects(selectedRootSubject, token),
      fetchExplanations(selectedRootSubject, token),
    ])
      .then(([children, explanationList]) => {
        setChildSubjects(children.sort((a, b) => a.sort_order - b.sort_order));
        setExplanations(
          explanationList
            .filter((exp) => exp.is_published || token)
            .sort((a, b) => a.title.localeCompare(b.title)),
        );
        setSelectedChildSubject(null);
        setSelectedExplanation(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setIsLoading(false));
  }, [selectedRootSubject, token]);

  useEffect(() => {
    if (!selectedChildSubject) {
      return;
    }

    setIsLoading(true);
    fetchExplanations(selectedChildSubject, token)
      .then((list) => {
        setExplanations(
          list
            .filter((exp) => exp.is_published || token)
            .sort((a, b) => a.title.localeCompare(b.title)),
        );
        setSelectedExplanation(null);
      })
      .catch((err) => setError(err.message))
      .finally(() => setIsLoading(false));
  }, [selectedChildSubject, token]);

  const hasChildSubjects = useMemo(
    () => selectedRootSubject !== null,
    [selectedRootSubject],
  );

  const handleAuthSuccess = (response: AuthResponse) => {
    setAuth({ token: response.token, user: response.user });
  };

  const handleLogin = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = new FormData(event.currentTarget);
    const email = String(form.get('email') ?? '').trim();
    const password = String(form.get('password') ?? '');
    setError(null);
    setIsLoading(true);
    try {
      const response = await login(email, password);
      handleAuthSuccess(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegister = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const form = new FormData(event.currentTarget);
    const name = String(form.get('name') ?? '').trim();
    const email = String(form.get('email') ?? '').trim();
    const password = String(form.get('password') ?? '');
    setError(null);
    setIsLoading(true);
    try {
      await register(name, email, password);
      const response = await login(email, password);
      handleAuthSuccess(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    setAuth(null);
    setRootSubjects([]);
    setChildSubjects([]);
    setExplanations([]);
    setSelectedChildSubject(null);
    setSelectedRootSubject(null);
    setSelectedExplanation(null);
    localStorage.removeItem(LOCAL_STORAGE_TOKEN_KEY);
    localStorage.removeItem(LOCAL_STORAGE_USER_KEY);
  };

  const handleRefreshProfile = async () => {
    if (!token) {
      return;
    }
    setIsLoading(true);
    setError(null);
    try {
      const profile = await fetchProfile(token);
      setAuth((prev) => (prev ? { ...prev, user: profile } : prev));
      localStorage.setItem(LOCAL_STORAGE_USER_KEY, JSON.stringify(profile));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Profil bilgisi alınamadı');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectExplanation = async (explanationId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const explanation = await fetchExplanation(explanationId, token);
      setSelectedExplanation(explanation);
      if (token) {
        const status: ReadStatus = await markExplanationRead(explanationId, token);
        setExplanations((prev) =>
          prev.map((item) =>
            item.id === explanationId ? { ...item, read_at: status.read_at } : item,
          ),
        );
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Açıklama yüklenemedi');
    } finally {
      setIsLoading(false);
    }
  };

  if (!auth) {
    return (
      <div className="app-shell">
        <div className="auth-card">
          <h1 className="app-title">KPSS Plus Panel</h1>
          <p className="app-subtitle">Hesabınıza giriş yapın veya yeni hesap oluşturun.</p>

          {error && <p className="app-error">{error}</p>}

          {isRegisterMode ? (
            <form className="auth-form" onSubmit={handleRegister}>
              <label>
                İsim
                <input name="name" type="text" placeholder="Adınız" required />
              </label>
              <label>
                E-posta
                <input name="email" type="email" placeholder="<EMAIL>" required />
              </label>
              <label>
                Şifre
                <input name="password" type="password" placeholder="En az 6 karakter" required />
              </label>
              <button type="submit" disabled={isLoading}>
                {isLoading ? 'Kaydediliyor…' : 'Kayıt Ol'}
              </button>
            </form>
          ) : (
            <form className="auth-form" onSubmit={handleLogin}>
              <label>
                E-posta
                <input name="email" type="email" placeholder="<EMAIL>" required />
              </label>
              <label>
                Şifre
                <input name="password" type="password" placeholder="Şifreniz" required />
              </label>
              <button type="submit" disabled={isLoading}>
                {isLoading ? 'Giriş yapılıyor…' : 'Giriş Yap'}
              </button>
            </form>
          )}

          <button
            type="button"
            className="auth-toggle"
            onClick={() => setIsRegisterMode((prev) => !prev)}
          >
            {isRegisterMode ? 'Zaten hesabınız var mı? Giriş yapın' : 'Hesabınız yok mu? Hemen kayıt olun'}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="app-layout">
      <aside className="sidebar">
        <div className="sidebar-header">
          <h1>KPSS Plus</h1>
          <p>{auth.user.name}</p>
          <div className="sidebar-actions">
            <button type="button" onClick={handleRefreshProfile} disabled={isLoading}>
              Profili yenile
            </button>
            <button type="button" onClick={handleLogout}>
              Çıkış yap
            </button>
          </div>
        </div>
        <div className="sidebar-section">
          <h2>Üst Başlıklar</h2>
          <ul>
            {rootSubjects.map((subject) => (
              <li key={subject.id}>
                <button
                  type="button"
                  className={subject.id === selectedRootSubject ? 'active' : ''}
                  onClick={() => setSelectedRootSubject(subject.id)}
                >
                  {subject.title}
                </button>
              </li>
            ))}
          </ul>
        </div>
      </aside>

      <main className="content">
        {error && <div className="banner error">{error}</div>}
        {isLoading && <div className="banner info">Yükleniyor…</div>}

        <div className="columns">
          {hasChildSubjects && (
            <section className="column">
              <h2>Alt Başlıklar</h2>
              {childSubjects.length === 0 ? (
                <p>Bu başlık için alt konu bulunmuyor.</p>
              ) : (
                <ul>
                  {childSubjects.map((subject) => (
                    <li key={subject.id}>
                      <button
                        type="button"
                        className={subject.id === selectedChildSubject ? 'active' : ''}
                        onClick={() => setSelectedChildSubject(subject.id)}
                      >
                        {subject.title}
                      </button>
                    </li>
                  ))}
                </ul>
              )}
            </section>
          )}

          <section className="column">
            <h2>Açıklamalar</h2>
            {explanations.length === 0 ? (
              <p>Bu konuya ait açıklama bulunamadı.</p>
            ) : (
              <ul>
                {explanations.map((explanation) => (
                  <li key={explanation.id}>
                    <button
                      type="button"
                      className={selectedExplanation?.id === explanation.id ? 'active' : ''}
                      onClick={() => handleSelectExplanation(explanation.id)}
                    >
                      <span>{explanation.title}</span>
                      {explanation.read_at && <small className="muted">Okundu: {new Date(explanation.read_at).toLocaleString()}</small>}
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </section>

          <section className="column detail">
            <h2>Açıklama Detayı</h2>
            {selectedExplanation ? (
              <article>
                <h3>{selectedExplanation.title}</h3>
                {selectedExplanation.summary && (
                  <p className="summary">{selectedExplanation.summary}</p>
                )}
                {selectedExplanation.body ? (
                  <div className="body" dangerouslySetInnerHTML={{ __html: selectedExplanation.body }} />
                ) : (
                  <p>Bu açıklama için içerik henüz eklenmedi.</p>
                )}
              </article>
            ) : (
              <p>Detay görmek için açıklama seçin.</p>
            )}
          </section>
        </div>
      </main>
    </div>
  );
}

export default App;
