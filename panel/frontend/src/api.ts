const API_BASE_URL = process.env.REACT_APP_API_BASE_URL ?? 'http://localhost:8000/api/v1';

export interface AuthUser {
  id: string;
  name: string;
  email: string;
}

export interface AuthResponse {
  token: string;
  user: AuthUser;
}

export interface Subject {
  id: string;
  lesson_id: string;
  parent_id?: string | null;
  title: string;
  summary?: string | null;
  sort_order: number;
}

export interface Explanation {
  id: string;
  lesson_id: string;
  subject_id: string;
  title: string;
  summary?: string | null;
  body?: string | null;
  is_published: boolean;
  read_at?: string | null;
}

export interface ReadStatus {
  explanation_id: string;
  read_at: string;
}

async function request<T>(path: string, options: RequestInit = {}): Promise<T> {
  const response = await fetch(`${API_BASE_URL}${path}`, {
    headers: {
      'Content-Type': 'application/json',
      ...(options.headers ?? {}),
    },
    ...options,
  });

  if (!response.ok) {
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      throw new Error(data?.error ?? 'Request failed');
    }
    throw new Error(`Request failed with status ${response.status}`);
  }

  if (response.status === 204) {
    return {} as T;
  }

  const data = await response.json();
  return (data?.data ?? data) as T;
}

export function login(email: string, password: string): Promise<AuthResponse> {
  return request<AuthResponse>('/auth/login', {
    method: 'POST',
    body: JSON.stringify({ email, password }),
  });
}

export function register(name: string, email: string, password: string): Promise<AuthUser> {
  return request<AuthUser>('/auth/register', {
    method: 'POST',
    body: JSON.stringify({ name, email, password }),
  });
}

export function fetchProfile(token: string): Promise<AuthUser> {
  return request<AuthUser>('/auth/me', {
    headers: { Authorization: `Bearer ${token}` },
  });
}

export function fetchRootSubjects(token: string | null): Promise<Subject[]> {
  return request<Subject[]>('/subjects', {
    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
  });
}

export function fetchChildSubjects(subjectId: string, token: string | null): Promise<Subject[]> {
  return request<Subject[]>(`/subjects/${subjectId}/children`, {
    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
  });
}

export function fetchExplanations(subjectId: string, token: string | null): Promise<Explanation[]> {
  return request<Explanation[]>(`/subjects/${subjectId}/explanations`, {
    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
  });
}

export function fetchExplanation(explanationId: string, token: string | null): Promise<Explanation> {
  return request<Explanation>(`/explanations/${explanationId}`, {
    headers: token ? { Authorization: `Bearer ${token}` } : undefined,
  });
}

export function markExplanationRead(explanationId: string, token: string): Promise<ReadStatus> {
  return request<ReadStatus>(`/explanations/${explanationId}/read`, {
    method: 'POST',
    headers: { Authorization: `Bearer ${token}` },
  });
}
