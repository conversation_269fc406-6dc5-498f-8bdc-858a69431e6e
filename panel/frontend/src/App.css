:root {
  color-scheme: light dark;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: #0f172a;
  color: #e2e8f0;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  background-color: #0f172a;
  color: #e2e8f0;
}

a {
  color: inherit;
}

.app-shell {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: rgba(15, 23, 42, 0.85);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 16px;
  padding: 2.5rem 2rem;
  max-width: 420px;
  width: 100%;
  box-shadow: 0 20px 50px rgba(15, 23, 42, 0.45);
}

.app-title {
  margin: 0 0 0.25rem;
  font-size: 2rem;
  font-weight: 700;
}

.app-subtitle {
  margin: 0 0 1.5rem;
  color: #94a3b8;
  font-size: 0.95rem;
}

.app-error {
  background-color: rgba(248, 113, 113, 0.1);
  color: #fecaca;
  border: 1px solid rgba(248, 113, 113, 0.35);
  padding: 0.75rem 1rem;
  border-radius: 12px;
  margin-bottom: 1rem;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.auth-form label {
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
  font-size: 0.9rem;
  color: #cbd5f5;
}

.auth-form input {
  border-radius: 10px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: rgba(15, 23, 42, 0.6);
  color: inherit;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.auth-form button {
  margin-top: 0.5rem;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 600;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border: none;
  color: white;
  cursor: pointer;
  transition: transform 0.15s ease, box-shadow 0.15s ease;
}

.auth-form button:disabled {
  opacity: 0.7;
  cursor: progress;
}

.auth-form button:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.35);
}

.auth-toggle {
  margin-top: 1.5rem;
  background: none;
  border: none;
  color: #a5b4fc;
  cursor: pointer;
  font-size: 0.9rem;
  text-decoration: underline;
}

.auth-toggle:hover {
  color: #c7d2fe;
}

.app-layout {
  display: grid;
  grid-template-columns: 320px 1fr;
  min-height: 100vh;
}

.sidebar {
  border-right: 1px solid rgba(148, 163, 184, 0.2);
  padding: 2rem 1.5rem;
  background: rgba(15, 23, 42, 0.9);
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.sidebar-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.sidebar-header p {
  margin: 0.15rem 0 1rem;
  color: #94a3b8;
  font-size: 0.9rem;
}

.sidebar-actions {
  display: flex;
  gap: 0.5rem;
}

.sidebar-actions button {
  flex: 1;
  border-radius: 8px;
  border: 1px solid rgba(148, 163, 184, 0.2);
  background: rgba(30, 41, 59, 0.8);
  color: inherit;
  padding: 0.6rem 0.9rem;
  cursor: pointer;
  font-size: 0.85rem;
}

.sidebar-section h2 {
  margin: 0 0 0.75rem;
  font-size: 1rem;
  color: #cbd5f5;
}

.sidebar-section ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.sidebar-section button {
  width: 100%;
  border-radius: 10px;
  border: 1px solid transparent;
  background: rgba(30, 41, 59, 0.6);
  color: inherit;
  padding: 0.6rem 0.9rem;
  text-align: left;
  cursor: pointer;
  transition: background 0.15s ease, border 0.15s ease;
}

.sidebar-section button:hover,
.sidebar-section button.active {
  background: rgba(99, 102, 241, 0.15);
  border-color: rgba(99, 102, 241, 0.4);
}

.content {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.banner {
  border-radius: 12px;
  padding: 0.85rem 1.1rem;
  font-size: 0.9rem;
}

.banner.error {
  background: rgba(248, 113, 113, 0.12);
  color: #fecaca;
  border: 1px solid rgba(248, 113, 113, 0.35);
}

.banner.info {
  background: rgba(37, 99, 235, 0.12);
  color: #bfdbfe;
  border: 1px solid rgba(37, 99, 235, 0.35);
}

.columns {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 1.5rem;
}

.column {
  background: rgba(15, 23, 42, 0.8);
  border-radius: 16px;
  border: 1px solid rgba(148, 163, 184, 0.15);
  padding: 1.25rem;
  min-height: 280px;
}

.column h2 {
  margin: 0 0 1rem;
  font-size: 1.1rem;
  color: #cbd5f5;
}

.column ul {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.column ul button {
  width: 100%;
  border-radius: 10px;
  border: 1px solid transparent;
  background: rgba(30, 41, 59, 0.6);
  color: inherit;
  padding: 0.65rem 0.85rem;
  text-align: left;
  cursor: pointer;
  transition: background 0.15s ease, border 0.15s ease;
  display: flex;
  flex-direction: column;
  gap: 0.35rem;
}

.column ul button:hover,
.column ul button.active {
  background: rgba(34, 197, 94, 0.14);
  border-color: rgba(34, 197, 94, 0.35);
}

.column ul button .muted {
  color: #94a3b8;
  font-size: 0.75rem;
}

.column.detail article {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.column.detail h3 {
  margin: 0;
  font-size: 1.35rem;
}

.column.detail .summary {
  margin: 0;
  color: #cbd5f5;
  font-size: 0.95rem;
  line-height: 1.5;
}

.column.detail .body {
  background: rgba(30, 41, 59, 0.7);
  border-radius: 10px;
  padding: 1rem;
  line-height: 1.6;
  color: #e2e8f0;
}

.column.detail .body p {
  margin: 0 0 1rem;
}

.column.detail .body p:last-child {
  margin-bottom: 0;
}

@media (max-width: 960px) {
  .app-layout {
    grid-template-columns: 1fr;
  }

  .sidebar {
    border-right: none;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
  }
}
