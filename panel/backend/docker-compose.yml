services:
  kpss-plus-db:
    image: postgres:15-alpine
    container_name: kpss-plus-db
    environment:
      POSTGRES_DB: kpss-plus
      POSTGRES_USER: kpss-plus-user
      POSTGRES_PASSWORD: kpss-plus-pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - kpss-plus
    restart: unless-stopped

  # Panel Backend (serves Panel FE on same port)
  kpss-plus-panel-backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: kpss-plus-panel-backend
    environment:
      - TZ=Europe/Istanbul
    ports:
      - "8000:8000"
    depends_on:
      - kpss-plus-db
    networks:
      - kpss-plus
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  kpss-plus:
    name: kpss-plus
    driver: bridge
