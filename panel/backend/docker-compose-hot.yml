services:
  kpss-plus-db:
    image: postgres:15-alpine
    container_name: kpss-plus-db
    environment:
      POSTGRES_DB: kpss-plus
      POSTGRES_USER: kpss-plus-user
      POSTGRES_PASSWORD: kpss-plus-pass
      TZ: Europe/Istanbul
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - kpss-plus
    restart: unless-stopped

  panel-backend:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: kpss-plus-panel-backend
    working_dir: /app
    environment:
      TZ: Europe/Istanbul
      RESET_DB: "true"
    volumes:
      - .:/app
      - panel_go_mod:/go/pkg/mod
      - panel_go_build:/root/.cache/go-build
    ports:
      - "8000:8000"
    depends_on:
      - kpss-plus-db
    networks:
      - kpss-plus
    restart: unless-stopped

  panel-frontend:
    image: node:20-alpine
    container_name: kpss-plus-panel-frontend-hot
    working_dir: /app
    stdin_open: true
    tty: true
    environment:
      NODE_ENV: development
      CHOKIDAR_USEPOLLING: "true"
      WATCHPACK_POLLING: "true"
      PORT: "8300"
      HOST: 0.0.0.0
      BROWSER: none
    volumes:
      - ../frontend:/app
      - panel_frontend_node_modules:/app/node_modules
    command: sh -c "npm install && npm run start"
    ports:
      - "8300:8300"
    depends_on:
      - panel-backend
    networks:
      - kpss-plus

volumes:
  postgres_data:
  panel_go_build:
  panel_go_mod:
  panel_frontend_node_modules:

networks:
  kpss-plus:
    name: kpss-plus
    driver: bridge
