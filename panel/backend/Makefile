build:
	go build -o fin_notebook  main.go

swagger:
	swag init

hot:
	docker compose -p kpss-plus -f docker-compose-hot.yml up
	

hot-backend:
	@echo "Starting backend with Docker..."
	docker compose -p fin_notebook -f docker-compose.yml up

hot-frontend:
	@echo "Starting frontend development server..."
	cd ../frontend && npm start

serve:
	@echo "Building frontend and serving from backend..."
	@echo "Frontend will be built and copied to backend/dist"
	@echo "Backend will serve both API and frontend on http://localhost:8008"
	cd ../frontend-web && npm run build
	rm -rf ./dist/*
	cp -r ../frontend-web/build/* ./dist/
	@echo "Starting backend with static files..."
	DEV_MODE=true docker compose -p fin_notebook -f docker-compose.yml up

build-production:
	@echo "Building frontend for production..."
	@echo "API calls will use relative paths (/api/v1)"
	cd ../frontend-web && npm run build
	rm -rf ./dist/*
	cp -r ../frontend-web/build/* ./dist/
	@echo "Production build ready in ./dist/"
	@echo "Deploy this backend with the dist folder to your server"

run:
	docker compose -f docker-compose.yml up -d --build

down:
	docker compose -f docker-compose.yml down

hot-admin:
	cd ../../admin/backend && docker compose -p kpss-plus-admin -f docker-compose.yml up -d --build

down-admin:
	cd ../admin && docker compose -f docker-compose.yml down
