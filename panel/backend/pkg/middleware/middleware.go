package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/utils"
)

// Context keys used across handlers.
const (
	ContextUserID = "current_user_id"
)

// Authenticated ensures a valid bearer token exists and stores the user id in context.
func Authenticated(jwtManager *utils.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		bearer := c.<PERSON>eader("Authorization")
		if bearer == "" || !strings.HasPrefix(strings.ToLower(bearer), "bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "authorization token required"})
			return
		}

		token := strings.TrimSpace(bearer[len("Bearer "):])
		claims, err := jwtManager.Parse(token)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid or expired token"})
			return
		}

		c.Set(ContextUserID, claims.UserID)
		c.Next()
	}
}

// OptionalAuthenticated sets the user id in context if a valid token is provided.
func OptionalAuthenticated(jwtManager *utils.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		bearer := c.GetHeader("Authorization")
		if bearer == "" || !strings.HasPrefix(strings.ToLower(bearer), "bearer ") {
			c.Next()
			return
		}

		token := strings.TrimSpace(bearer[len("Bearer "):])
		claims, err := jwtManager.Parse(token)
		if err == nil {
			c.Set(ContextUserID, claims.UserID)
		}
		c.Next()
	}
}
