package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/yaml.v3"
)

// Config represents application runtime configuration.
type Config struct {
	App      App      `yaml:"app"`
	Database Database `yaml:"database"`
	Allows   Allows   `yaml:"allows"`
}

// App contains application-level settings.
type App struct {
	Name      string `yaml:"name"`
	Port      string `yaml:"port"`
	Host      string `yaml:"host"`
	BaseURL   string `yaml:"base_url"`
	JwtIssuer string `yaml:"jwt_issuer"`
	JwtSecret string `yaml:"jwt_secret"`
	JwtExpire int    `yaml:"jwt_expire"`
}

// Database holds Postgres connection details.
type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

// Allows describes CORS settings.
type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

// InitConfig loads configuration from file and environment variables.
func InitConfig() *Config {
	var cfg Config

	filename, _ := filepath.Abs("./config.yaml")
	if data, err := os.ReadFile(filename); err == nil {
		if err := yaml.Unmarshal(data, &cfg); err != nil {
			log.Fatalf("config: unmarshal error: %v", err)
		}
	}

	if val := os.Getenv("APP_NAME"); val != "" {
		cfg.App.Name = val
	}
	if val := os.Getenv("APP_PORT"); val != "" {
		cfg.App.Port = val
	}
	if val := os.Getenv("APP_HOST"); val != "" {
		cfg.App.Host = val
	}
	if val := os.Getenv("APP_BASE_URL"); val != "" {
		cfg.App.BaseURL = val
	}
	if val := os.Getenv("APP_JWT_ISSUER"); val != "" {
		cfg.App.JwtIssuer = val
	}
	if val := os.Getenv("APP_JWT_SECRET"); val != "" {
		cfg.App.JwtSecret = val
	}
	if val := os.Getenv("APP_JWT_EXPIRE"); val != "" {
		cfg.App.JwtExpire = mustAtoi(val, cfg.App.JwtExpire)
	}

	if val := os.Getenv("DB_HOST"); val != "" {
		cfg.Database.Host = val
	}
	if val := os.Getenv("DB_PORT"); val != "" {
		cfg.Database.Port = val
	}
	if val := os.Getenv("DB_USER"); val != "" {
		cfg.Database.User = val
	}
	if val := os.Getenv("DB_PASS"); val != "" {
		cfg.Database.Pass = val
	}
	if val := os.Getenv("DB_NAME"); val != "" {
		cfg.Database.Name = val
	}

	if val := os.Getenv("CORS_ORIGINS"); val != "" {
		cfg.Allows.Origins = strings.Split(val, ",")
	}
	if val := os.Getenv("CORS_METHODS"); val != "" {
		cfg.Allows.Methods = strings.Split(val, ",")
	}
	if val := os.Getenv("CORS_HEADERS"); val != "" {
		cfg.Allows.Headers = strings.Split(val, ",")
	}

	return &cfg
}

func mustAtoi(val string, fallback int) int {
	if val == "" {
		return fallback
	}
	var parsed int
	if _, err := fmt.Sscanf(val, "%d", &parsed); err == nil {
		return parsed
	}
	return fallback
}
