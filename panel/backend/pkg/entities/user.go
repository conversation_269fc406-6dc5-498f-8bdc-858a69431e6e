package entities

import "time"

// User represents an end-user that can authenticate in the panel.
type User struct {
	Base
	Email        string     `gorm:"uniqueIndex;not null" json:"email"`
	PasswordHash string     `gorm:"not null" json:"-"`
	Name         string     `gorm:"not null" json:"name"`
	IsActive     bool       `gorm:"default:true" json:"is_active"`
	LastLoginAt  *time.Time `json:"last_login_at"`
}
