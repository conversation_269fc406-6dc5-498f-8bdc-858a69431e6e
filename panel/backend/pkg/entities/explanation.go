package entities

import "github.com/google/uuid"

// Explanation is a piece of study content tied to a subject.
type Explanation struct {
	Base
	LessonID    uuid.UUID `gorm:"type:uuid;index;not null" json:"lesson_id"`
	SubjectID   uuid.UUID `gorm:"type:uuid;index;not null" json:"subject_id"`
	Title       string    `gorm:"not null" json:"title"`
	Summary     *string   `json:"summary"`
	Body        *string   `gorm:"type:text" json:"body"`
	SortOrder   int       `json:"sort_order"`
	IsPublished bool      `gorm:"default:false" json:"is_published"`
}
