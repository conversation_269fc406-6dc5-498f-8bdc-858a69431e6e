package entities

import (
	"time"

	"github.com/google/uuid"
)

// UserExplanationRead stores when a user viewed an explanation.
type UserExplanationRead struct {
	Base
	UserID        uuid.UUID `gorm:"type:uuid;not null;index;uniqueIndex:uidx_user_explanation" json:"user_id"`
	ExplanationID uuid.UUID `gorm:"type:uuid;not null;index;uniqueIndex:uidx_user_explanation" json:"explanation_id"`
	ReadAt        time.Time `gorm:"not null" json:"read_at"`
}
