package entities

import "github.com/google/uuid"

// Subject represents a hierarchical topic under a lesson.
type Subject struct {
	Base
	LessonID  uuid.UUID  `gorm:"type:uuid;index;not null" json:"lesson_id"`
	ParentID  *uuid.UUID `gorm:"type:uuid;index" json:"parent_id"`
	Title     string     `gorm:"not null" json:"title"`
	Summary   *string    `json:"summary"`
	SortOrder int        `json:"sort_order"`
	IsActive  bool       `gorm:"default:true" json:"is_active"`
}
