package utils

import "golang.org/x/crypto/bcrypt"

// HashPassword hashes the provided plaintext password.
func HashPassword(password string) string {
	hash, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(hash)
}

// ComparePassword validates a plaintext password against a hashed value.
func ComparePassword(hashedPassword, password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	return err == nil
}
