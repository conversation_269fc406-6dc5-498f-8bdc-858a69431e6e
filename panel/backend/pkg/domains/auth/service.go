package auth

import (
	"errors"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/utils"
	"gorm.io/gorm"
)

// Service exposes authentication operations for the API.
type Service interface {
	Register(input RegisterInput) (*UserDTO, error)
	Login(input LoginInput) (*AuthResponse, error)
	Profile(id uuid.UUID) (*UserDTO, error)
}

type service struct {
	repo Repository
	jwt  *utils.JWTManager
}

// NewService constructs a new Service instance.
func NewService(repo Repository, jwt *utils.JWTManager) Service {
	return &service{repo: repo, jwt: jwt}
}

func (s *service) Register(input RegisterInput) (*UserDTO, error) {
	name := strings.TrimSpace(input.Name)
	email := strings.ToLower(strings.TrimSpace(input.Email))
	password := strings.TrimSpace(input.Password)

	if name == "" || email == "" || password == "" {
		return nil, errors.New("name, email and password are required")
	}

	if len(password) < 6 {
		return nil, errors.New("password must contain at least 6 characters")
	}

	if _, err := s.repo.FindByEmail(email); err == nil {
		return nil, errors.New("email is already registered")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	user := &entities.User{
		Email:        email,
		Name:         name,
		PasswordHash: utils.HashPassword(password),
		IsActive:     true,
	}

	if err := s.repo.Create(user); err != nil {
		return nil, err
	}

	return &UserDTO{
		ID:    user.ID.String(),
		Name:  user.Name,
		Email: user.Email,
	}, nil
}

func (s *service) Login(input LoginInput) (*AuthResponse, error) {
	email := strings.ToLower(strings.TrimSpace(input.Email))
	password := strings.TrimSpace(input.Password)

	if email == "" || password == "" {
		return nil, errors.New("email and password are required")
	}

	user, err := s.repo.FindByEmail(email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid credentials")
		}
		return nil, err
	}

	if !user.IsActive {
		return nil, errors.New("account is disabled")
	}

	if !utils.ComparePassword(user.PasswordHash, password) {
		return nil, errors.New("invalid credentials")
	}

	token, err := s.jwt.Generate(user.ID.String())
	if err != nil {
		return nil, err
	}

	_ = s.repo.UpdateLastLogin(user.ID, time.Now())

	return &AuthResponse{
		Token: token,
		User: UserDTO{
			ID:    user.ID.String(),
			Name:  user.Name,
			Email: user.Email,
		},
	}, nil
}

func (s *service) Profile(id uuid.UUID) (*UserDTO, error) {
	user, err := s.repo.FindByID(id)
	if err != nil {
		return nil, err
	}

	return &UserDTO{
		ID:    user.ID.String(),
		Name:  user.Name,
		Email: user.Email,
	}, nil
}
