package auth

import (
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

// Repository abstracts data access for authentication-aware operations.
type Repository interface {
	Create(user *entities.User) error
	FindByEmail(email string) (*entities.User, error)
	FindByID(id uuid.UUID) (*entities.User, error)
	UpdateLastLogin(id uuid.UUID, ts time.Time) error
}

type repository struct {
	db *gorm.DB
}

// NewRepository constructs a Repository backed by GORM.
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) Create(user *entities.User) error {
	return r.db.Create(user).Error
}

func (r *repository) FindByEmail(email string) (*entities.User, error) {
	var user entities.User
	if err := r.db.Where("email = ?", email).First(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) FindByID(id uuid.UUID) (*entities.User, error) {
	var user entities.User
	if err := r.db.First(&user, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func (r *repository) UpdateLastLogin(id uuid.UUID, ts time.Time) error {
	return r.db.Model(&entities.User{}).Where("id = ?", id).Update("last_login_at", ts).Error
}
