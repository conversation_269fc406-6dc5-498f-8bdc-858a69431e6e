package explanation

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

// Service coordinates explanation access and read tracking.
type Service interface {
	ListBySubject(opts ListOptions) ([]ExplanationDTO, error)
	Get(id uuid.UUID, userID *uuid.UUID) (*ExplanationDTO, error)
	MarkRead(explanationID uuid.UUID, userID uuid.UUID) (*ReadStatusDTO, error)
}

type service struct {
	repo Repository
}

// NewService builds a new explanation Service.
func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) ListBySubject(opts ListOptions) ([]ExplanationDTO, error) {
	if opts.SubjectID == uuid.Nil {
		return nil, errors.New("subject_id is required")
	}

	explanations, err := s.repo.ListBySubject(opts.SubjectID, opts.IncludeDrafts)
	if err != nil {
		return nil, err
	}

	dtos := make([]ExplanationDTO, 0, len(explanations))
	explanationIDs := make([]uuid.UUID, 0, len(explanations))
	for _, exp := range explanations {
		dtos = append(dtos, mapEntity(exp, nil))
		explanationIDs = append(explanationIDs, exp.ID)
	}

	if opts.UserID != nil && len(explanationIDs) > 0 {
		reads, err := s.repo.ReadsForUser(*opts.UserID, explanationIDs)
		if err != nil {
			return nil, err
		}
		for idx, exp := range explanations {
			if readAt, ok := reads[exp.ID]; ok {
				dtos[idx].ReadAt = &readAt
			}
		}
	}

	return dtos, nil
}

func (s *service) Get(id uuid.UUID, userID *uuid.UUID) (*ExplanationDTO, error) {
	explanation, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}

	var readAt *time.Time
	if userID != nil {
		read, err := s.repo.ReadForUser(*userID, explanation.ID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err
		}
		if err == nil && read != nil {
			readAt = &read.ReadAt
		}
	}

	dto := mapEntity(*explanation, readAt)
	return &dto, nil
}

func (s *service) MarkRead(explanationID uuid.UUID, userID uuid.UUID) (*ReadStatusDTO, error) {
	if explanationID == uuid.Nil || userID == uuid.Nil {
		return nil, errors.New("user_id and explanation_id are required")
	}

	if _, err := s.repo.Get(explanationID); err != nil {
		return nil, err
	}

	now := time.Now().UTC()
	read := &entities.UserExplanationRead{
		UserID:        userID,
		ExplanationID: explanationID,
		ReadAt:        now,
	}
	if err := s.repo.UpsertRead(read); err != nil {
		return nil, err
	}

	return &ReadStatusDTO{
		ExplanationID: explanationID.String(),
		ReadAt:        now,
	}, nil
}

func mapEntity(item entities.Explanation, readAt *time.Time) ExplanationDTO {
	dto := ExplanationDTO{
		ID:          item.ID.String(),
		LessonID:    item.LessonID.String(),
		SubjectID:   item.SubjectID.String(),
		Title:       item.Title,
		Summary:     item.Summary,
		Body:        item.Body,
		IsPublished: item.IsPublished,
	}
	if readAt != nil {
		dto.ReadAt = readAt
	}
	return dto
}
