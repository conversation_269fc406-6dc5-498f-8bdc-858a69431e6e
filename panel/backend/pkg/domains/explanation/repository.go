package explanation

import (
	"time"

	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Repository handles persistence for explanations and read tracking.
type Repository interface {
	ListBySubject(subjectID uuid.UUID, includeDrafts bool) ([]entities.Explanation, error)
	Get(id uuid.UUID) (*entities.Explanation, error)
	UpsertRead(read *entities.UserExplanationRead) error
	ReadsForUser(userID uuid.UUID, explanationIDs []uuid.UUID) (map[uuid.UUID]time.Time, error)
	ReadForUser(userID uuid.UUID, explanationID uuid.UUID) (*entities.UserExplanationRead, error)
}

type repository struct {
	db *gorm.DB
}

// NewRepository creates a new explanation Repository.
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) ListBySubject(subjectID uuid.UUID, includeDrafts bool) ([]entities.Explanation, error) {
	query := r.db.Where("subject_id = ?", subjectID)
	if !includeDrafts {
		query = query.Where("is_published = ?", true)
	}
	var list []entities.Explanation
	err := query.Order("sort_order ASC, title ASC").Find(&list).Error
	return list, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Explanation, error) {
	var explanation entities.Explanation
	if err := r.db.First(&explanation, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &explanation, nil
}

func (r *repository) UpsertRead(read *entities.UserExplanationRead) error {
	return r.db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "explanation_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"read_at", "updated_at"}),
	}).Create(read).Error
}

func (r *repository) ReadsForUser(userID uuid.UUID, explanationIDs []uuid.UUID) (map[uuid.UUID]time.Time, error) {
	if len(explanationIDs) == 0 {
		return map[uuid.UUID]time.Time{}, nil
	}
	var reads []entities.UserExplanationRead
	if err := r.db.Where("user_id = ?", userID).
		Where("explanation_id IN ?", explanationIDs).
		Find(&reads).Error; err != nil {
		return nil, err
	}
	result := make(map[uuid.UUID]time.Time, len(reads))
	for _, read := range reads {
		result[read.ExplanationID] = read.ReadAt
	}
	return result, nil
}

func (r *repository) ReadForUser(userID uuid.UUID, explanationID uuid.UUID) (*entities.UserExplanationRead, error) {
	var read entities.UserExplanationRead
	if err := r.db.Where("user_id = ?", userID).
		Where("explanation_id = ?", explanationID).
		First(&read).Error; err != nil {
		return nil, err
	}
	return &read, nil
}
