package explanation

import (
	"time"

	"github.com/google/uuid"
)

// ListOptions defines filters when listing explanations.
type ListOptions struct {
	SubjectID     uuid.UUID
	IncludeDrafts bool
	UserID        *uuid.UUID
}

// ExplanationDTO shapes explanation data for API consumers.
type ExplanationDTO struct {
	ID          string     `json:"id"`
	LessonID    string     `json:"lesson_id"`
	SubjectID   string     `json:"subject_id"`
	Title       string     `json:"title"`
	Summary     *string    `json:"summary"`
	Body        *string    `json:"body,omitempty"`
	IsPublished bool       `json:"is_published"`
	ReadAt      *time.Time `json:"read_at,omitempty"`
}

// ReadStatusDTO reports the latest read activity for an explanation.
type ReadStatusDTO struct {
	ExplanationID string    `json:"explanation_id"`
	ReadAt        time.Time `json:"read_at"`
}
