package subject

import (
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
)

// Service exposes read-only operations for subjects.
type Service interface {
	ListMain(opts ListOptions) ([]SubjectDTO, error)
	ListChildren(parentID uuid.UUID) ([]SubjectDTO, error)
	Get(id uuid.UUID) (*SubjectDTO, error)
}

type service struct {
	repo Repository
}

// NewService constructs a subject Service.
func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) ListMain(opts ListOptions) ([]SubjectDTO, error) {
	subjects, err := s.repo.ListMain(opts)
	if err != nil {
		return nil, err
	}
	return mapEntities(subjects), nil
}

func (s *service) ListChildren(parentID uuid.UUID) ([]SubjectDTO, error) {
	subjects, err := s.repo.ListChildren(parentID)
	if err != nil {
		return nil, err
	}
	return mapEntities(subjects), nil
}

func (s *service) Get(id uuid.UUID) (*SubjectDTO, error) {
	subject, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}
	dto := mapEntity(*subject)
	return &dto, nil
}

func mapEntities(list []entities.Subject) []SubjectDTO {
	result := make([]SubjectDTO, 0, len(list))
	for _, item := range list {
		dto := mapEntity(item)
		result = append(result, dto)
	}
	return result
}

func mapEntity(item entities.Subject) SubjectDTO {
	dto := SubjectDTO{
		ID:        item.ID.String(),
		LessonID:  item.LessonID.String(),
		Title:     item.Title,
		Summary:   item.Summary,
		SortOrder: item.SortOrder,
	}
	if item.ParentID != nil {
		parent := item.ParentID.String()
		dto.ParentID = &parent
	}
	return dto
}
