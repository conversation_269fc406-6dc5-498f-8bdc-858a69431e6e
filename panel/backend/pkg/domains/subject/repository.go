package subject

import (
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/gorm"
)

// Repository provides data access helpers for subjects.
type Repository interface {
	ListMain(opts ListOptions) ([]entities.Subject, error)
	ListChildren(parentID uuid.UUID) ([]entities.Subject, error)
	Get(id uuid.UUID) (*entities.Subject, error)
}

type repository struct {
	db *gorm.DB
}

// NewRepository constructs a subject Repository.
func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) ListMain(opts ListOptions) ([]entities.Subject, error) {
	query := r.db.Where("parent_id IS NULL").Where("is_active = ?", true)
	if opts.LessonID != nil {
		query = query.Where("lesson_id = ?", *opts.LessonID)
	}
	var subjects []entities.Subject
	err := query.Order("sort_order ASC, title ASC").Find(&subjects).Error
	return subjects, err
}

func (r *repository) ListChildren(parentID uuid.UUID) ([]entities.Subject, error) {
	var subjects []entities.Subject
	err := r.db.Where("parent_id = ?", parentID).
		Where("is_active = ?", true).
		Order("sort_order ASC, title ASC").
		Find(&subjects).Error
	return subjects, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Subject, error) {
	var subject entities.Subject
	if err := r.db.First(&subject, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &subject, nil
}
