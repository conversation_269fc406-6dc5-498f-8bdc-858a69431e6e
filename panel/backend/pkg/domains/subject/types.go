package subject

import "github.com/google/uuid"

// ListOptions defines filters when requesting subjects.
type ListOptions struct {
	LessonID *uuid.UUID
}

// SubjectDTO shapes a subject payload for HTTP responses.
type SubjectDTO struct {
	ID        string  `json:"id"`
	LessonID  string  `json:"lesson_id"`
	ParentID  *string `json:"parent_id"`
	Title     string  `json:"title"`
	Summary   *string `json:"summary"`
	SortOrder int     `json:"sort_order"`
}
