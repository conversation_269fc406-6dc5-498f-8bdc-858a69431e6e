package database

import (
	"fmt"
	"log"
	"os"
	"sync"

	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/entities"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var (
	db         *gorm.DB
	err        error
	clientOnce sync.Once
)

// InitDB establishes the database connection and runs auto migrations.
func InitDB(dbc config.Database) {
	clientOnce.Do(func() {
		dsn := fmt.Sprintf(
			"host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Europe/Istanbul",
			dbc.Host, dbc.Port, dbc.User, dbc.Pass, dbc.Name,
		)

		db, err = gorm.Open(postgres.New(postgres.Config{DSN: dsn, PreferSimpleProtocol: true}))
		if err != nil {
			panic(err)
		}

		if os.<PERSON>env("RESET_DB") == "true" {
			tables := []interface{}{
				&entities.UserExplanationRead{},
				&entities.Explanation{},
				&entities.Subject{},
				&entities.Lesson{},
				&entities.User{},
			}
			if err := db.Migrator().DropTable(tables...); err != nil {
				log.Printf("database: drop tables failed: %v", err)
			}
		}

		if err := db.AutoMigrate(
			&entities.User{},
			&entities.Lesson{},
			&entities.Subject{},
			&entities.Explanation{},
			&entities.UserExplanationRead{},
		); err != nil {
			panic(err)
		}

		db.Exec("SET TIME ZONE 'Europe/Istanbul';")
	})
}

// DBClient returns the singleton DB connection.
func DBClient() *gorm.DB {
	if db == nil {
		log.Panic("postgres is not initialized. call InitDB first")
	}
	return db
}
