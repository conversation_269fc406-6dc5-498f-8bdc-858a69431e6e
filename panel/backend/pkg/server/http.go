package server

import (
	"fmt"
	"log"
	"path/filepath"
	"time"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/app/api/routes"
	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/database"
	authDomain "github.com/kpss-plus-backend/pkg/domains/auth"
	explanationDomain "github.com/kpss-plus-backend/pkg/domains/explanation"
	subjectDomain "github.com/kpss-plus-backend/pkg/domains/subject"
	"github.com/kpss-plus-backend/pkg/utils"
)

// LaunchHTTPServer wires dependencies and starts the Gin HTTP server.
func LaunchHTTPServer(cfg *config.Config) {
	if cfg == nil {
		log.Fatal("config is required")
	}

	gin.SetMode(gin.ReleaseMode)

	router := gin.New()
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	router.Use(cors.New(cors.Config{
		AllowMethods:     cfg.Allows.Methods,
		AllowHeaders:     cfg.Allows.Headers,
		AllowOrigins:     cfg.Allows.Origins,
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	}))

	db := database.DBClient()

	tokenTTL := time.Duration(cfg.App.JwtExpire)
	if tokenTTL <= 0 {
		tokenTTL = 24
	}
	jwtManager := utils.NewJWTManager(cfg.App.JwtSecret, cfg.App.JwtIssuer, tokenTTL*time.Hour)

	authRepo := authDomain.NewRepository(db)
	authService := authDomain.NewService(authRepo, jwtManager)

	subjectRepo := subjectDomain.NewRepository(db)
	subjectService := subjectDomain.NewService(subjectRepo)

	explanationRepo := explanationDomain.NewRepository(db)
	explanationService := explanationDomain.NewService(explanationRepo)

	api := router.Group("/api/v1")
	routes.AuthRoutes(api.Group("/auth"), authService, jwtManager)
	routes.SubjectRoutes(api, subjectService)
	routes.ExplanationRoutes(api, explanationService, jwtManager)

	distDir := filepath.Clean("./dist")
	router.Static("/assets", filepath.Join(distDir, "assets"))
	router.StaticFile("/favicon.ico", filepath.Join(distDir, "favicon.ico"))
	router.StaticFile("/manifest.json", filepath.Join(distDir, "manifest.json"))

	router.NoRoute(func(c *gin.Context) {
		c.File(filepath.Join(distDir, "index.html"))
	})

	addr := fmt.Sprintf("%s:%s", cfg.App.Host, cfg.App.Port)
	log.Printf("HTTP server listening on %s", addr)
	if err := router.Run(addr); err != nil {
		log.Fatalf("http server error: %v", err)
	}
}
