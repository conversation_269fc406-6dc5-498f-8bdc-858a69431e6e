package routes

import (
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/domains/explanation"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/utils"
	"gorm.io/gorm"
)

// ExplanationRoutes registers endpoints for study content.
func ExplanationRoutes(r *gin.RouterGroup, service explanation.Service, jwtManager *utils.JWTManager) {
	public := r.Group("/")
	public.Use(middleware.OptionalAuthenticated(jwtManager))

	public.GET("/subjects/:id/explanations", func(c *gin.Context) {
		subjectID, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}

		opts := explanation.ListOptions{SubjectID: subjectID}
		if query := strings.ToLower(c.Query("include_drafts")); query == "true" {
			opts.IncludeDrafts = true
		}

		if userIDStr := c.GetString(middleware.ContextUserID); userIDStr != "" {
			if userID, err := uuid.Parse(userIDStr); err == nil {
				opts.UserID = &userID
			}
		}

		list, err := service.ListBySubject(opts)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": list})
	})

	public.GET("/explanations/:id", func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid explanation id"})
			return
		}

		var userID *uuid.UUID
		if userIDStr := c.GetString(middleware.ContextUserID); userIDStr != "" {
			if parsed, err := uuid.Parse(userIDStr); err == nil {
				userID = &parsed
			}
		}

		explanationDTO, err := service.Get(id, userID)
		if err != nil {
			status := http.StatusInternalServerError
			if errors.Is(err, gorm.ErrRecordNotFound) {
				status = http.StatusNotFound
			}
			c.JSON(status, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": explanationDTO})
	})

	protected := r.Group("/")
	protected.Use(middleware.Authenticated(jwtManager))
	protected.POST("/explanations/:id/read", func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid explanation id"})
			return
		}

		userIDStr := c.GetString(middleware.ContextUserID)
		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			return
		}

		statusDTO, err := service.MarkRead(id, userID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": statusDTO})
	})
}
