package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/domains/subject"
)

// SubjectRoutes registers subject listing endpoints.
func SubjectRoutes(r *gin.RouterGroup, service subject.Service) {
	r.GET("/subjects", func(c *gin.Context) {
		var opts subject.ListOptions
		if lessonID := c.Query("lesson_id"); lessonID != "" {
			id, err := uuid.Parse(lessonID)
			if err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
				return
			}
			opts.LessonID = &id
		}

		subjects, err := service.ListMain(opts)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": subjects})
	})

	r.GET("/subjects/:id", func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}

		subjectDTO, err := service.Get(id)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": subjectDTO})
	})

	r.GET("/subjects/:id/children", func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}

		subjects, err := service.ListChildren(id)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": subjects})
	})
}
