package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/kpss-plus-backend/pkg/domains/auth"
	"github.com/kpss-plus-backend/pkg/middleware"
	"github.com/kpss-plus-backend/pkg/utils"
)

// AuthRoutes registers authentication endpoints.
func AuthRoutes(r *gin.RouterGroup, service auth.Service, jwtManager *utils.JWTManager) {
	r.POST("/register", func(c *gin.Context) {
		var input auth.RegisterInput
		if err := c.ShouldBindJSON(&input); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		user, err := service.Register(input)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.<PERSON>()})
			return
		}

		c.<PERSON><PERSON><PERSON>(http.StatusCreated, gin.H{"data": user})
	})

	r.POST("/login", func(c *gin.Context) {
		var input auth.LoginInput
		if err := c.ShouldBindJSON(&input); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		resp, err := service.Login(input)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": resp})
	})

	protected := r.Group("/")
	protected.Use(middleware.Authenticated(jwtManager))
	protected.GET("/me", func(c *gin.Context) {
		userID := c.GetString(middleware.ContextUserID)
		parsed, err := uuid.Parse(userID)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid token"})
			return
		}

		profile, err := service.Profile(parsed)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"data": profile})
	})
}
