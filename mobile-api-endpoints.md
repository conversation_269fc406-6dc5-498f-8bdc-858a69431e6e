# Mobil Uygulama API Endpoint Analizi

## 🔐 Auth Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `POST /auth/login/username` - Use<PERSON><PERSON> ile giriş
- `POST /auth/login/email` - Email ile giriş  
- `POST /auth/login/guest` - <PERSON><PERSON><PERSON><PERSON> girişi
- `POST /auth/register` - Kay<PERSON>t
- `POST /auth/forgot-password` - <PERSON><PERSON><PERSON> sıfırlama
- `POST /auth/reset-password` - <PERSON><PERSON>re yenileme
- `POST /auth/verify-otp` - OTP doğrulama
- `POST /auth/validate-token` - Token doğrulama
- `POST /auth/login/google` - Google ile giriş
- `POST /auth/login/apple` - Apple ile giriş
- `GET /auth/profile` - Profil bilgisi
- `PUT /auth/profile` - Profil güncelleme

## 📚 Content Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /content` - <PERSON><PERSON><PERSON><PERSON> listesi (filtreleme ile)
- `GET /content/{id}` - <PERSON><PERSON><PERSON> içerik
- `GET /content/search` - İçerik arama
- `GET /content/type/{type}` - Tipe göre içerik
- `GET /content/subject/{subject}` - Konuya göre içerik
- `GET /content/popular` - Popüler içerik
- `GET /content/recommended` - Önerilen içerik (auth)
- `GET /content/{id}/stats` - İçerik istatistikleri
- `GET /content/library` - Kullanıcı kütüphanesi (auth)
- `POST /content/{id}/library` - Kütüphaneye ekle (auth)
- `DELETE /content/{id}/library` - Kütüphaneden çıkar (auth)
- `PUT /content/progress` - İlerleme güncelle (auth)
- `GET /content/{id}/progress` - İçerik ilerlemesi (auth)
- `GET /content/progress` - Genel ilerleme (auth)
- `POST /content` - İçerik oluştur (admin)
- `PUT /content/{id}` - İçerik güncelle (admin)
- `DELETE /content/{id}` - İçerik sil (admin)
- `POST /content/{id}/like` - Beğeni toggle (auth)
- `POST /content/{id}/rate` - İçerik puanla (auth)
- `GET /content/favorites` - Favori içerikler (auth)
- `GET /content/history` - İzleme geçmişi (auth)
- `DELETE /content/{id}/favorite` - Favoriden çıkar (auth)
- `POST /content/{id}/favorite` - Favoriye ekle (auth)

## 🧠 Quiz Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /quiz` - Quiz listesi (filtreleme ile)
- `GET /quiz/{id}` - Tekil quiz
- `GET /quiz/search` - Quiz arama
- `GET /quiz/type/{type}` - Tipe göre quiz
- `GET /quiz/subject/{subject}` - Konuya göre quiz
- `GET /quiz/popular` - Popüler quizler
- `GET /quiz/recommended` - Önerilen quizler (auth)
- `GET /quiz/{id}/statistics` - Quiz istatistikleri
- `GET /quiz/{id}/leaderboard` - Quiz liderlik tablosu
- `GET /quiz/{id}/similar` - Benzer quizler
- `POST /quiz/{id}/start` - Quiz başlat (auth)
- `POST /quiz/sessions/{sessionId}/answer` - Cevap gönder (auth)
- `POST /quiz/sessions/{sessionId}/finish` - Quiz bitir (auth)
- `POST /quiz/sessions/{sessionId}/submit` - Quiz gönder (auth)
- `GET /quiz/results` - Quiz sonuçları (auth)
- `GET /quiz/results/{id}` - Tekil quiz sonucu (auth)
- `GET /quiz/categories` - Quiz kategorileri
- `POST /quiz` - Quiz oluştur (admin)
- `PUT /quiz/{id}` - Quiz güncelle (admin)
- `DELETE /quiz/{id}` - Quiz sil (admin)

## 👥 Social Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /social/feed` - Aktivite akışı (auth)
- `POST /social/friends/request` - Arkadaşlık isteği gönder (auth)
- `POST /social/friends/request/respond` - Arkadaşlık isteğine yanıt (auth)
- `GET /social/friends/requests/sent` - Gönderilen istekler (auth)
- `GET /social/friends/requests/received` - Alınan istekler (auth)
- `GET /social/friends` - Arkadaş listesi (auth)
- `DELETE /social/friends/{id}` - Arkadaşlıktan çıkar (auth)
- `POST /social/follow` - Takip et (auth)
- `DELETE /social/follow/{id}` - Takibi bırak (auth)
- `GET /social/followers` - Takipçiler (auth)
- `GET /social/following` - Takip edilenler (auth)
- `GET /social/search/users` - Kullanıcı arama
- `GET /social/suggestions/friends` - Arkadaş önerileri (auth)
- `GET /social/mutual-friends/{id}` - Ortak arkadaşlar (auth)
- `POST /social/block` - Engelle (auth)
- `DELETE /social/block/{id}` - Engeli kaldır (auth)
- `GET /social/blocked` - Engellenen kullanıcılar (auth)
- `GET /social/activity` - Aktivite geçmişi (auth)
- `GET /social/profile/{id}` - Kullanıcı profili
- `GET /social/stats` - Sosyal istatistikler (auth)
- `PUT /social/privacy` - Gizlilik ayarları (auth)
- `GET /social/leaderboard` - Liderlik tablosu
- `POST /social/posts` - Post oluştur (auth)
- `PUT /social/posts/{id}` - Post güncelle (auth)
- `DELETE /social/posts/{id}` - Post sil (auth)
- `POST /social/posts/{id}/like` - Post beğen (auth)
- `POST /social/posts/{id}/comment` - Yorum yap (auth)
- `GET /social/posts/{id}/comments` - Yorumları getir
- `PUT /social/comments/{id}` - Yorum güncelle (auth)
- `DELETE /social/comments/{id}` - Yorum sil (auth)

## 🎯 Goals Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /goals` - Hedef listesi (auth)
- `POST /goals` - Hedef oluştur (auth)
- `GET /goals/{id}` - Tekil hedef (auth)
- `PUT /goals/{id}` - Hedef güncelle (auth)
- `DELETE /goals/{id}` - Hedef sil (auth)
- `POST /goals/{id}/progress` - Hedef ilerlemesi güncelle (auth)
- `GET /goals/templates` - Hedef şablonları
- `GET /goals/stats` - Hedef istatistikleri (auth)

## 🏆 Badge Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /badges` - Rozet listesi
- `GET /badges/{id}` - Tekil rozet
- `GET /badges/user` - Kullanıcı rozetleri (auth)
- `GET /badges/user/{id}` - Başka kullanıcının rozetleri
- `POST /badges/claim` - Rozet talep et (auth)
- `GET /badges/progress` - Rozet ilerlemesi (auth)

## 📊 Progress Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /progress/overview` - Genel ilerleme (auth)
- `GET /progress/content` - İçerik ilerlemesi (auth)
- `GET /progress/quiz` - Quiz ilerlemesi (auth)
- `GET /progress/goals` - Hedef ilerlemesi (auth)
- `GET /progress/streaks` - Seri bilgileri (auth)
- `PUT /progress/update` - İlerleme güncelle (auth)

## 📈 Analytics Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /analytics/user/activity` - Kullanıcı aktivitesi (auth)
- `GET /analytics/user/performance` - Kullanıcı performansı (auth)
- `GET /analytics/user/time-spent` - Harcanan zaman (auth)
- `GET /analytics/user/streaks` - Seri analizi (auth)
- `POST /analytics/events` - Event kaydet (auth)

## 🪙 Token Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /user/tokens` - Token bakiyesi (auth)
- `GET /user/tokens/history` - Token geçmişi (auth)
- `POST /user/tokens/purchase` - Token satın al (auth)
- `POST /user/tokens/spend` - Token harca (auth)
- `POST /user/tokens/earn` - Token kazan (auth)
- `POST /user/tokens/daily-bonus` - Günlük bonus (auth)
- `GET /user/tokens/packages` - Token paketleri

## ⚔️ Battle Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /battles/stats/{id}` - Savaş istatistikleri (auth)
- `GET /battles/active` - Aktif savaşlar (auth)
- `GET /battles/history` - Savaş geçmişi (auth)
- `POST /battles/create` - Savaş oluştur (auth)
- `POST /battles/{id}/join` - Savaşa katıl (auth)
- `POST /battles/{id}/leave` - Savaştan ayrıl (auth)
- `GET /battles/{id}/participants` - Katılımcılar (auth)
- `POST /battles/{id}/challenge` - Meydan okuma gönder (auth)
- `POST /battles/challenges/respond` - Meydan okumaya yanıt (auth)

## 🔧 Preferences Service Endpoints

### Mevcut Mobil Endpoint'ler:
- `GET /preferences` - Kullanıcı tercihleri (auth)
- `PUT /preferences` - Tercihleri güncelle (auth)
- `PUT /preferences/notifications` - Bildirim ayarları (auth)
- `PUT /preferences/privacy` - Gizlilik ayarları (auth)
- `PUT /preferences/theme` - Tema ayarları (auth)

---

## 🔍 Backend Karşılaştırması

### ✅ Backend'de Mevcut Endpoint'ler:

#### Auth Routes (`/auth`):
- ✅ `POST /auth/login` - Temel giriş
- ✅ `POST /auth/login/guest` - Misafir girişi
- ✅ `POST /auth/register` - Kayıt
- ✅ `POST /auth/login/username` - Username ile giriş
- ✅ `POST /auth/login/email` - Email ile giriş
- ✅ `POST /auth/login/phone` - Telefon ile giriş
- ✅ `POST /auth/login/admin` - Admin girişi
- ✅ `GET /auth/admin/check` - Admin kontrolü
- ✅ `POST /auth/login/google` - Google ile giriş
- ✅ `POST /auth/login/apple` - Apple ile giriş
- ✅ `POST /auth/forgot-password` - Şifre sıfırlama
- ✅ `POST /auth/reset-password` - Şifre yenileme
- ✅ `POST /auth/verify-otp` - OTP doğrulama
- ✅ `POST /auth/validate-token` - Token doğrulama

#### Content Routes (`/content`):
- ✅ `GET /content` - İçerik listesi
- ✅ `GET /content/:id` - Tekil içerik
- ✅ `GET /content/search` - İçerik arama
- ✅ `GET /content/type/:type` - Tipe göre içerik
- ✅ `GET /content/subject/:subject` - Konuya göre içerik
- ✅ `GET /content/popular` - Popüler içerik
- ✅ `GET /content/:id/stats` - İçerik istatistikleri
- ✅ `POST /content` - İçerik oluştur (auth)
- ✅ `PUT /content/:id` - İçerik güncelle (auth)
- ✅ `DELETE /content/:id` - İçerik sil (auth)
- ✅ `GET /content/library` - Kullanıcı kütüphanesi (auth)
- ✅ `POST /content/:id/library` - Kütüphaneye ekle (auth)
- ✅ `DELETE /content/:id/library` - Kütüphaneden çıkar (auth)
- ✅ `PUT /content/progress` - İlerleme güncelle (auth)
- ✅ `GET /content/:id/progress` - İçerik ilerlemesi (auth)
- ✅ `GET /content/progress` - Genel ilerleme (auth)
- ✅ `GET /content/recommended` - Önerilen içerik

#### Quiz Routes (`/quiz`):
- ✅ `GET /quiz` - Quiz listesi
- ✅ `GET /quiz/:id` - Tekil quiz
- ✅ `GET /quiz/search` - Quiz arama
- ✅ `GET /quiz/subject/:subject` - Konuya göre quiz
- ✅ `GET /quiz/popular` - Popüler quizler
- ✅ `GET /quiz/:id/questions` - Quiz soruları
- ✅ `GET /quiz/:id/statistics` - Quiz istatistikleri
- ✅ `GET /quiz/:id/leaderboard` - Quiz liderlik tablosu
- ✅ `POST /quiz` - Quiz oluştur (auth)
- ✅ `PUT /quiz/:id` - Quiz güncelle (auth)
- ✅ `DELETE /quiz/:id` - Quiz sil (auth)
- ✅ `GET /quiz/my` - Kullanıcı quizleri (auth)
- ✅ `POST /quiz/:id/questions` - Soru ekle (auth)
- ✅ `PUT /quiz/questions/:questionId` - Soru güncelle (auth)
- ✅ `DELETE /quiz/questions/:questionId` - Soru sil (auth)
- ✅ `POST /quiz/:id/start` - Quiz başlat (auth)
- ✅ `POST /quiz/sessions/:sessionId/answer` - Cevap gönder (auth)
- ✅ `POST /quiz/sessions/:sessionId/finish` - Quiz bitir (auth)
- ✅ `GET /quiz/results/:resultId` - Quiz sonucu (auth)
- ✅ `GET /quiz/results` - Kullanıcı quiz sonuçları (auth)
- ✅ `POST /quiz/:id/share` - Quiz paylaş (auth)
- ✅ `POST /quiz/:id/invite` - Quiz davet (auth)
- ✅ `GET /quiz/shared` - Paylaşılan quizler (auth)
- ✅ `GET /quiz/recommended` - Önerilen quizler
- ✅ `GET /quiz/:id/similar` - Benzer quizler

### ✅ Backend'de Mevcut Ek Servisler:

#### User Routes (`/user`):
- ✅ `GET /user/profile` - Profil bilgisi (auth)
- ✅ `PUT /user/profile` - Profil güncelleme (auth)
- ✅ `PUT /user/password` - Şifre değiştirme (auth)
- ✅ `PUT /user/notifications` - Bildirim ayarları (auth)
- ✅ `POST /user/deactivate` - Hesap deaktive (auth)
- ✅ `DELETE /user/delete` - Hesap silme (auth)
- ✅ `POST /user/verify-email` - Email doğrulama (auth)
- ✅ `POST /user/resend-verification` - Doğrulama tekrar gönder (auth)

#### Social Routes (`/social`):
- ✅ `GET /social/leaderboard` - Liderlik tablosu
- ✅ `POST /social/friends/request` - Arkadaşlık isteği (auth)
- ✅ `PUT /social/friends/request/respond` - Arkadaşlık yanıtı (auth)
- ✅ `GET /social/friends/requests/:type` - Arkadaşlık istekleri (auth)
- ✅ `GET /social/friends` - Arkadaş listesi (auth)
- ✅ `DELETE /social/friends/:friendId` - Arkadaşlıktan çıkar (auth)
- ✅ `POST /social/follow` - Takip et (auth)
- ✅ `DELETE /social/follow/:userId` - Takibi bırak (auth)
- ✅ `GET /social/followers` - Takipçiler (auth)
- ✅ `GET /social/following` - Takip edilenler (auth)
- ✅ `GET /social/search/users` - Kullanıcı arama (auth)
- ✅ `GET /social/suggestions/friends` - Arkadaş önerileri (auth)
- ✅ `GET /social/mutual-friends/:userId` - Ortak arkadaşlar (auth)
- ✅ `POST /social/block` - Engelle (auth)
- ✅ `DELETE /social/block/:userId` - Engeli kaldır (auth)
- ✅ `GET /social/blocked` - Engellenen kullanıcılar (auth)
- ✅ `GET /social/activity` - Aktivite geçmişi (auth)
- ✅ `GET /social/profile/:userId` - Kullanıcı profili (auth)
- ✅ `GET /social/stats` - Sosyal istatistikler (auth)
- ✅ `PUT /social/privacy` - Gizlilik ayarları (auth)
- ✅ `GET /social/privacy` - Gizlilik ayarları (auth)

#### Badge Routes (`/badges`):
- ✅ `GET /badges` - Rozet listesi
- ✅ `GET /badges/:id` - Tekil rozet
- ✅ `GET /badges/categories` - Rozet kategorileri
- ✅ `GET /badges/user` - Kullanıcı rozetleri (auth)
- ✅ `GET /badges/user/progress` - Rozet ilerlemesi (auth)
- ✅ `POST /badges/:id/claim` - Rozet talep et (auth)
- ✅ `POST /badges` - Rozet oluştur (admin)
- ✅ `PUT /badges/:id` - Rozet güncelle (admin)
- ✅ `DELETE /badges/:id` - Rozet sil (admin)

#### Progress Routes (`/progress`):
- ✅ `PUT /progress` - İlerleme güncelle (auth)
- ✅ `GET /progress/content/:contentId` - İçerik ilerlemesi (auth)
- ✅ `GET /progress` - Genel ilerleme (auth)
- ✅ `GET /progress/stats` - İlerleme istatistikleri (auth)
- ✅ `GET /progress/history` - İlerleme geçmişi (auth)
- ✅ `GET /progress/streak` - Seri bilgileri (auth)
- ✅ `POST /progress/sessions` - Çalışma oturumu başlat (auth)
- ✅ `PUT /progress/sessions/:sessionId/end` - Oturum bitir (auth)
- ✅ `GET /progress/sessions` - Çalışma oturumları (auth)
- ✅ `POST /progress/goals` - Hedef oluştur (auth)
- ✅ `GET /progress/goals` - Hedefler (auth)
- ✅ `PUT /progress/goals/:goalId` - Hedef güncelle (auth)
- ✅ `DELETE /progress/goals/:goalId` - Hedef sil (auth)
- ✅ `GET /progress/achievements` - Başarılar (auth)

#### Analytics Routes (`/analytics`):
- ✅ `GET /analytics/users` - Kullanıcı analizi (auth)
- ✅ `GET /analytics/users/activity` - Kullanıcı aktivitesi (auth)
- ✅ `GET /analytics/users/progress` - Kullanıcı ilerlemesi (auth)
- ✅ `GET /analytics/content` - İçerik analizi (auth)
- ✅ `GET /analytics/content/popular` - Popüler içerik analizi (auth)
- ✅ `GET /analytics/content/engagement` - İçerik etkileşimi (auth)
- ✅ `GET /analytics/quizzes` - Quiz analizi (auth)
- ✅ `GET /analytics/quizzes/performance` - Quiz performansı (auth)
- ✅ `GET /analytics/quizzes/completion` - Quiz tamamlama (auth)
- ✅ `GET /analytics/system` - Sistem analizi (auth)
- ✅ `GET /analytics/system/usage` - Sistem kullanımı (auth)

#### Preferences Routes (`/preferences`):
- ✅ `GET /preferences` - Kullanıcı tercihleri
- ✅ `PUT /preferences` - Tercihleri güncelle (auth)

#### Timeline Routes (`/timeline`):
- ✅ `GET /timeline/public` - Genel zaman çizelgesi
- ✅ `GET /timeline` - Kullanıcı zaman çizelgesi (auth)
- ✅ `GET /timeline/feed` - Arkadaş akışı (auth)
- ✅ `POST /timeline` - Zaman çizelgesi girişi oluştur (auth)
- ✅ `PUT /timeline/:id` - Zaman çizelgesi güncelle (auth)
- ✅ `DELETE /timeline/:id` - Zaman çizelgesi sil (auth)
- ✅ `POST /timeline/:id/like` - Beğeni (auth)
- ✅ `DELETE /timeline/:id/like` - Beğeniyi kaldır (auth)
- ✅ `GET /timeline/:id/comments` - Yorumlar
- ✅ `POST /timeline/:id/comments` - Yorum yap (auth)
- ✅ `PUT /timeline/comments/:commentId` - Yorum güncelle (auth)
- ✅ `DELETE /timeline/comments/:commentId` - Yorum sil (auth)

#### Topic Routes (`/topics`):
- ✅ `GET /topics` - Konu listesi
- ✅ `GET /topics/:id` - Tekil konu
- ✅ `POST /topics` - Konu oluştur (auth)
- ✅ `PUT /topics/:id` - Konu güncelle (auth)
- ✅ `DELETE /topics/:id` - Konu sil (auth)
- ✅ `PUT /topics/:id/progress` - Konu ilerlemesi güncelle (auth)
- ✅ `GET /topics/:id/progress` - Konu ilerlemesi (auth)

#### Notification Routes (`/notifications`):
- ✅ `GET /notifications` - Bildirimler (auth)
- ✅ `GET /notifications/unread` - Okunmamış sayısı (auth)
- ✅ `PUT /notifications/:id/read` - Okundu işaretle (auth)
- ✅ `PUT /notifications/read-all` - Tümünü okundu işaretle (auth)
- ✅ `DELETE /notifications/:id` - Bildirimi sil (auth)
- ✅ `GET /notifications/settings` - Bildirim ayarları (auth)
- ✅ `PUT /notifications/settings` - Bildirim ayarları güncelle (auth)

#### Library Routes (`/library`):
- ✅ `GET /library` - Kütüphane listesi (auth)
- ✅ `POST /library/:type/:id` - Kütüphaneye ekle (auth)
- ✅ `DELETE /library/:type/:id` - Kütüphaneden çıkar (auth)
- ✅ `GET /library/folders` - Klasörler (auth)
- ✅ `POST /library/folders` - Klasör oluştur (auth)
- ✅ `PUT /library/folders/:id` - Klasör güncelle (auth)
- ✅ `DELETE /library/folders/:id` - Klasör sil (auth)

#### Version Routes (`/version`):
- ✅ `GET /version` - Versiyon kontrolü (auth)
- ✅ `POST /version` - Yeni versiyon

### ❌ Backend'de Eksik Endpoint'ler:

#### Content Service Eksikleri:
- ❌ `POST /content/:id/like` - İçerik beğeni toggle
- ❌ `POST /content/:id/rate` - İçerik puanlama
- ❌ `GET /content/favorites` - Favori içerikler
- ❌ `GET /content/history` - İzleme geçmişi
- ❌ `POST /content/:id/favorite` - Favoriye ekle
- ❌ `DELETE /content/:id/favorite` - Favoriden çıkar

#### Quiz Service Eksikleri:
- ❌ `GET /quiz/type/:type` - Tipe göre quiz (mobilde var)
- ❌ `POST /quiz/sessions/:sessionId/submit` - Quiz gönder (mobilde var)
- ❌ `GET /quiz/categories` - Quiz kategorileri

#### Tamamen Eksik Servisler:
- ❌ **Goals Service** - Mobil uygulamada ayrı goals service var ama backend'de progress altında
- ❌ **Token Service** - Token sistemi eksik (mobilde ayrı service)
- ❌ **Battle Service** - Savaş sistemi eksik
