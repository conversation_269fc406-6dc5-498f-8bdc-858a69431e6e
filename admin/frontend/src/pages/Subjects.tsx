import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout';
import { apiDel, apiGet, apiPost, apiPut } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  FolderIcon,
  LinkIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface LessonOption {
  id: string;
  title: string;
}

interface SubjectItem {
  id: string;
  lesson_id: string;
  main_subject_id?: string | null;
  title: string;
  description?: string | null;
  is_active: boolean;
  created_at: string;
}

type StatusFilter = 'all' | 'active' | 'inactive';

const Subjects: React.FC = () => {
  const [lessons, setLessons] = useState<LessonOption[]>([]);
  const [subjects, setSubjects] = useState<SubjectItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [lessonFilter, setLessonFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');

  const [showModal, setShowModal] = useState(false);
  const [editing, setEditing] = useState<SubjectItem | null>(null);
  const [form, setForm] = useState({
    lessonId: '',
    parentId: '',
    title: '',
    description: '',
    isActive: true,
  });

  useEffect(() => {
    const bootstrap = async () => {
      try {
        const lessonRes = await apiGet<{ data: LessonOption[] }>('/lessons?limit=200');
        setLessons(lessonRes.data ?? []);
      } catch (error) {
        console.error('Lessons load failed', error);
      }
    };
    bootstrap();
  }, []);

  const loadSubjects = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '200');
      if (search.trim()) params.append('q', search.trim());
      if (lessonFilter) params.append('lesson_id', lessonFilter);
      if (statusFilter === 'active') params.append('is_active', 'true');
      if (statusFilter === 'inactive') params.append('is_active', 'false');

      const response = await apiGet<{ data: SubjectItem[]; total: number }>(`/subjects?${params.toString()}`);
      setSubjects(response.data ?? []);
    } catch (error) {
      console.error('Subjects load failed', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadSubjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lessonFilter, statusFilter]);

  const filteredSubjects = useMemo(() => {
    if (!search.trim()) return subjects;
    const needle = search.trim().toLowerCase();
    return subjects.filter((subject) =>
      subject.title.toLowerCase().includes(needle) ||
      (subject.description ?? '').toLowerCase().includes(needle)
    );
  }, [subjects, search]);

  const resetForm = () => {
    setForm({ lessonId: '', parentId: '', title: '', description: '', isActive: true });
    setEditing(null);
  };

  const openCreate = () => {
    resetForm();
    setShowModal(true);
  };

  const openEdit = (item: SubjectItem) => {
    setEditing(item);
    setForm({
      lessonId: item.lesson_id,
      parentId: item.main_subject_id ?? '',
      title: item.title,
      description: item.description ?? '',
      isActive: item.is_active,
    });
    setShowModal(true);
  };

  const saveSubject = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const payload = {
      lesson_id: form.lessonId,
      main_subject_id: form.parentId || null,
      title: form.title.trim(),
      description: form.description.trim() || null,
      is_active: form.isActive,
    };

    try {
      if (editing) {
        await apiPut(`/subjects/${editing.id}`, payload);
      } else {
        await apiPost('/subjects', payload);
      }
      setShowModal(false);
      resetForm();
      await loadSubjects();
    } catch (error) {
      console.error('Subject save failed', error);
      alert('Konu kaydedilirken bir hata oluştu.');
    }
  };

  const deleteSubject = async (id: string) => {
    if (!window.confirm('Bu konuyu silmek istediğinizden emin misiniz?')) return;
    try {
      await apiDel(`/subjects/${id}`);
      await loadSubjects();
    } catch (error) {
      console.error('Subject delete failed', error);
      alert('Konu silinemedi.');
    }
  };

  const subjectsByLesson = useMemo(() => {
    const map = new Map<string, SubjectItem[]>();
    subjects.forEach((subject) => {
      const list = map.get(subject.lesson_id) ?? [];
      list.push(subject);
      map.set(subject.lesson_id, list);
    });
    return map;
  }, [subjects]);

  const parentOptions = useMemo(() => {
    if (!form.lessonId) return [] as SubjectItem[];
    return (subjectsByLesson.get(form.lessonId) ?? []).filter((subject) => subject.id !== editing?.id);
  }, [subjectsByLesson, form.lessonId, editing]);

  const lessonName = (lessonId: string) => lessons.find((l) => l.id === lessonId)?.title ?? '-';
  const parentName = (parentId?: string | null) => subjects.find((s) => s.id === parentId)?.title ?? '-';

  return (
    <Layout>
      <div className="space-y-6">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Konular</h1>
            <p className="mt-1 text-sm text-gray-300">
              Derslere bağlı konu başlıklarını yönetin, hiyerarşi tanımlayın.
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <div className="inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200">
              <button className={`px-3 py-1.5 ${statusFilter === 'all' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('all')}>
                Tümü
              </button>
              <button className={`px-3 py-1.5 ${statusFilter === 'active' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('active')}>
                Aktif
              </button>
              <button className={`px-3 py-1.5 ${statusFilter === 'inactive' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('inactive')}>
                Pasif
              </button>
            </div>
            <button className="btn-primary flex items-center" onClick={openCreate}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Yeni Konu
            </button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="card flex items-center space-x-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <input
              className="input-field"
              placeholder="Konu ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="card">
            <label className="form-label">Ders Filtrele</label>
            <select
              className="input-field"
              value={lessonFilter}
              onChange={(e) => setLessonFilter(e.target.value)}
            >
              <option value="">Tümü</option>
              {lessons.map((lesson) => (
                <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
              ))}
            </select>
          </div>
          <div className="card">
            <label className="form-label">Durum</label>
            <select
              className="input-field"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as StatusFilter)}
            >
              <option value="all">Tümü</option>
              <option value="active">Aktif</option>
              <option value="inactive">Pasif</option>
            </select>
          </div>
        </div>

        <div className="card p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500" />
              <span className="ml-3 text-gray-300">Konular yükleniyor...</span>
            </div>
          ) : filteredSubjects.length === 0 ? (
            <div className="py-12 text-center text-gray-400">Kriterlere uygun konu bulunamadı.</div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-800/80">
                  <tr>
                    <th className="table-header">Konu</th>
                    <th className="table-header hidden md:table-cell">Ders</th>
                    <th className="table-header hidden lg:table-cell">Üst Konu</th>
                    <th className="table-header hidden sm:table-cell">Durum</th>
                    <th className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-800 bg-slate-900/40">
                  {filteredSubjects.map((subject) => (
                    <tr key={subject.id} className="hover:bg-slate-800/60">
                      <td className="table-cell">
                        <div className="flex items-start space-x-3">
                          <div className="h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center">
                            <FolderIcon className="h-5 w-5 text-primary-400" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-white">{subject.title}</p>
                            {subject.description && (
                              <p className="text-xs text-gray-400 truncate max-w-xs">{subject.description}</p>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell hidden md:table-cell text-sm text-gray-300">{lessonName(subject.lesson_id)}</td>
                      <td className="table-cell hidden lg:table-cell text-sm text-gray-300 flex items-center gap-2">
                        <LinkIcon className="h-4 w-4 text-gray-400" />
                        <span>{parentName(subject.main_subject_id)}</span>
                      </td>
                      <td className="table-cell hidden sm:table-cell">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                          subject.is_active
                            ? 'bg-emerald-500/20 text-emerald-300'
                            : 'bg-rose-500/20 text-rose-300'
                        }`}>
                          {subject.is_active ? (
                            <>
                              <CheckCircleIcon className="h-4 w-4 mr-1" />
                              Aktif
                            </>
                          ) : (
                            <>
                              <XCircleIcon className="h-4 w-4 mr-1" />
                              Pasif
                            </>
                          )}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button onClick={() => openEdit(subject)} className="icon-button text-primary-400 hover:text-primary-200">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button onClick={() => deleteSubject(subject.id)} className="icon-button text-red-400 hover:text-red-200">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <Transition show={showModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
          <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-150" leaveFrom="opacity-100" leaveTo="opacity-0">
            <div className="fixed inset-0 bg-black/60" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0 scale-95" enterTo="opacity-100 scale-100" leave="ease-in duration-150" leaveFrom="opacity-100 scale-100" leaveTo="opacity-0 scale-95">
                <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all">
                  <Dialog.Title className="text-xl font-semibold text-white mb-4">
                    {editing ? 'Konuyu Düzenle' : 'Yeni Konu Oluştur'}
                  </Dialog.Title>
                  <form onSubmit={saveSubject} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="form-label">Ders</label>
                        <select
                          className="input-field"
                          value={form.lessonId}
                          onChange={(e) => setForm((prev) => ({ ...prev, lessonId: e.target.value, parentId: '' }))}
                          required
                        >
                          <option value="">Seçiniz</option>
                          {lessons.map((lesson) => (
                            <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="form-label">Üst Konu (Opsiyonel)</label>
                        <select
                          className="input-field"
                          value={form.parentId}
                          onChange={(e) => setForm((prev) => ({ ...prev, parentId: e.target.value }))}
                          disabled={!form.lessonId}
                        >
                          <option value="">Seçiniz</option>
                          {parentOptions.map((subject) => (
                            <option key={subject.id} value={subject.id}>{subject.title}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="form-label">Başlık</label>
                      <input
                        className="input-field"
                        value={form.title}
                        onChange={(e) => setForm((prev) => ({ ...prev, title: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <label className="form-label">Açıklama</label>
                      <textarea
                        className="input-field"
                        rows={3}
                        value={form.description}
                        onChange={(e) => setForm((prev) => ({ ...prev, description: e.target.value }))}
                        placeholder="Opsiyonel"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <label className="inline-flex items-center space-x-2 text-sm text-gray-200">
                        <input
                          type="checkbox"
                          className="form-checkbox"
                          checked={form.isActive}
                          onChange={(e) => setForm((prev) => ({ ...prev, isActive: e.target.checked }))}
                        />
                        <span>Konu aktif olsun</span>
                      </label>
                      <div className="space-x-3">
                        <button type="button" className="btn-secondary" onClick={() => { setShowModal(false); resetForm(); }}>
                          Vazgeç
                        </button>
                        <button type="submit" className="btn-primary">Kaydet</button>
                      </div>
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </Layout>
  );
};

export default Subjects;
