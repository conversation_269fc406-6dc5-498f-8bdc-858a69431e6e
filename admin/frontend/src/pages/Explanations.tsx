import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout';
import { apiDel, apiGet, apiPost, apiPut } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  BookmarkIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface LessonOption {
  id: string;
  title: string;
}

interface SubjectOption {
  id: string;
  lesson_id: string;
  title: string;
}

interface Explanation {
  id: string;
  lesson_id: string;
  subject_id: string;
  title: string;
  summary?: string;
  body?: string;
  is_published: boolean;
  created_at: string;
}

type PublishFilter = 'all' | 'published' | 'draft';

const Explanations: React.FC = () => {
  const [items, setItems] = useState<Explanation[]>([]);
  const [lessons, setLessons] = useState<LessonOption[]>([]);
  const [subjects, setSubjects] = useState<SubjectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState<PublishFilter>('all');
  const [lessonFilter, setLessonFilter] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('');

  const [showModal, setShowModal] = useState(false);
  const [editing, setEditing] = useState<Explanation | null>(null);
  const [form, setForm] = useState({
    lessonId: '',
    subjectId: '',
    title: '',
    summary: '',
    body: '',
    isPublished: true,
  });

  useEffect(() => {
    const bootstrap = async () => {
      try {
        const [lessonRes, subjectRes] = await Promise.all([
          apiGet<{ data: LessonOption[] }>('/lessons?limit=200'),
          apiGet<{ data: SubjectOption[] }>('/subjects?limit=200'),
        ]);
        setLessons(lessonRes.data ?? []);
        setSubjects(subjectRes.data ?? []);
      } catch (error) {
        console.error('Lookup load failed', error);
      }
    };
    bootstrap();
  }, []);

  const loadExplanations = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '200');
      if (search.trim()) params.append('q', search.trim());
      if (lessonFilter) params.append('lesson_id', lessonFilter);
      if (subjectFilter) params.append('subject_id', subjectFilter);
      if (filter === 'published') params.append('is_published', 'true');
      if (filter === 'draft') params.append('is_published', 'false');

      const response = await apiGet<{ data: Explanation[]; total: number }>(`/explanations?${params.toString()}`);
      setItems(response.data ?? []);
    } catch (error) {
      console.error('Explanations load failed', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadExplanations();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter, lessonFilter, subjectFilter]);

  const filteredItems = useMemo(() => {
    if (!search.trim()) return items;
    const needle = search.trim().toLowerCase();
    return items.filter((item) =>
      item.title.toLowerCase().includes(needle) ||
      (item.summary ?? '').toLowerCase().includes(needle)
    );
  }, [items, search]);

  const resetForm = () => {
    setForm({ lessonId: '', subjectId: '', title: '', summary: '', body: '', isPublished: true });
    setEditing(null);
  };

  const openCreate = () => {
    resetForm();
    setShowModal(true);
  };

  const openEdit = (item: Explanation) => {
    setEditing(item);
    setForm({
      lessonId: item.lesson_id,
      subjectId: item.subject_id,
      title: item.title,
      summary: item.summary ?? '',
      body: item.body ?? '',
      isPublished: item.is_published,
    });
    setShowModal(true);
  };

  const save = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const payload = {
      lesson_id: form.lessonId,
      subject_id: form.subjectId,
      title: form.title.trim(),
      summary: form.summary.trim() || null,
      body: form.body.trim() || null,
      is_published: form.isPublished,
    };

    try {
      if (editing) {
        await apiPut(`/explanations/${editing.id}`, payload);
      } else {
        await apiPost('/explanations', payload);
      }
      setShowModal(false);
      resetForm();
      await loadExplanations();
    } catch (error) {
      console.error('Explanation save failed', error);
      alert('Açıklama kaydedilirken bir hata oluştu.');
    }
  };

  const remove = async (id: string) => {
    if (!window.confirm('Bu açıklamayı silmek istediğinizden emin misiniz?')) return;
    try {
      await apiDel(`/explanations/${id}`);
      await loadExplanations();
    } catch (error) {
      console.error('Explanation delete failed', error);
      alert('Açıklama silinemedi.');
    }
  };

  const subjectsForSelectedLesson = useMemo(() => {
    if (!form.lessonId) return subjects;
    return subjects.filter((subject) => subject.lesson_id === form.lessonId);
  }, [subjects, form.lessonId]);

  return (
    <Layout>
      <div className="space-y-6">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Açıklamalar</h1>
            <p className="mt-1 text-sm text-gray-300">
              Ders ve konu bazlı açıklamaları yönetin, içerikleri düzenleyin.
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <div className="inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200">
              <button className={`px-3 py-1.5 ${filter === 'all' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setFilter('all')}>
                Tümü
              </button>
              <button className={`px-3 py-1.5 ${filter === 'published' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setFilter('published')}>
                Yayında
              </button>
              <button className={`px-3 py-1.5 ${filter === 'draft' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setFilter('draft')}>
                Taslak
              </button>
            </div>
            <button className="btn-primary flex items-center" onClick={openCreate}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Yeni Açıklama
            </button>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="card flex items-center space-x-3">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <input
              className="input-field"
              placeholder="Açıklama ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="card">
            <label className="form-label">Ders Filtrele</label>
            <select
              className="input-field"
              value={lessonFilter}
              onChange={(e) => {
                setLessonFilter(e.target.value);
                setSubjectFilter('');
              }}
            >
              <option value="">Tümü</option>
              {lessons.map((lesson) => (
                <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
              ))}
            </select>
          </div>
          <div className="card">
            <label className="form-label">Konu Filtrele</label>
            <select
              className="input-field"
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              disabled={!lessonFilter}
            >
              <option value="">Tümü</option>
              {subjects
                .filter((subject) => !lessonFilter || subject.lesson_id === lessonFilter)
                .map((subject) => (
                  <option key={subject.id} value={subject.id}>{subject.title}</option>
                ))}
            </select>
          </div>
        </div>

        <div className="card p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500" />
              <span className="ml-3 text-gray-300">Açıklamalar yükleniyor...</span>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="py-12 text-center text-gray-400">Kriterlere uygun açıklama bulunamadı.</div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-800/80">
                  <tr>
                    <th className="table-header">Açıklama</th>
                    <th className="table-header hidden md:table-cell">Ders</th>
                    <th className="table-header hidden lg:table-cell">Konu</th>
                    <th className="table-header hidden md:table-cell">Durum</th>
                    <th className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-800 bg-slate-900/40">
                  {filteredItems.map((item) => {
                    const lesson = lessons.find((l) => l.id === item.lesson_id);
                    const subject = subjects.find((s) => s.id === item.subject_id);
                    return (
                      <tr key={item.id} className="hover:bg-slate-800/60">
                        <td className="table-cell">
                          <div className="flex items-start space-x-3">
                            <div className="h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center">
                              <DocumentTextIcon className="h-5 w-5 text-primary-400" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-white">{item.title}</p>
                              {item.summary && (
                                <p className="text-xs text-gray-400 truncate max-w-xs">{item.summary}</p>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="table-cell hidden md:table-cell text-sm text-gray-300">{lesson?.title ?? '-'}</td>
                        <td className="table-cell hidden lg:table-cell text-sm text-gray-300 flex items-center gap-2">
                          <BookmarkIcon className="h-4 w-4 text-gray-400" />
                          <span>{subject?.title ?? '-'}</span>
                        </td>
                        <td className="table-cell hidden md:table-cell">
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                            item.is_published
                              ? 'bg-emerald-500/20 text-emerald-300'
                              : 'bg-amber-500/20 text-amber-300'
                          }`}>
                            {item.is_published ? 'Yayında' : 'Taslak'}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center space-x-2">
                            <button onClick={() => openEdit(item)} className="icon-button text-primary-400 hover:text-primary-200">
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button onClick={() => remove(item.id)} className="icon-button text-red-400 hover:text-red-200">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <Transition show={showModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
          <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-150" leaveFrom="opacity-100" leaveTo="opacity-0">
            <div className="fixed inset-0 bg-black/60" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0 scale-95" enterTo="opacity-100 scale-100" leave="ease-in duration-150" leaveFrom="opacity-100 scale-100" leaveTo="opacity-0 scale-95">
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all">
                  <Dialog.Title className="text-xl font-semibold text-white mb-4">
                    {editing ? 'Açıklamayı Düzenle' : 'Yeni Açıklama Oluştur'}
                  </Dialog.Title>
                  <form onSubmit={save} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="form-label">Ders</label>
                        <select
                          className="input-field"
                          value={form.lessonId}
                          onChange={(e) => setForm((prev) => ({ ...prev, lessonId: e.target.value, subjectId: '' }))}
                          required
                        >
                          <option value="">Seçiniz</option>
                          {lessons.map((lesson) => (
                            <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="form-label">Konu</label>
                        <select
                          className="input-field"
                          value={form.subjectId}
                          onChange={(e) => setForm((prev) => ({ ...prev, subjectId: e.target.value }))}
                          required
                          disabled={!form.lessonId}
                        >
                          <option value="">Seçiniz</option>
                          {subjectsForSelectedLesson.map((subject) => (
                            <option key={subject.id} value={subject.id}>{subject.title}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="form-label">Başlık</label>
                      <input
                        className="input-field"
                        value={form.title}
                        onChange={(e) => setForm((prev) => ({ ...prev, title: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <label className="form-label">Özet</label>
                      <textarea
                        className="input-field"
                        rows={2}
                        value={form.summary}
                        onChange={(e) => setForm((prev) => ({ ...prev, summary: e.target.value }))}
                        placeholder="Opsiyonel"
                      />
                    </div>
                    <div>
                      <label className="form-label">İçerik</label>
                      <textarea
                        className="input-field"
                        rows={6}
                        value={form.body}
                        onChange={(e) => setForm((prev) => ({ ...prev, body: e.target.value }))}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <label className="inline-flex items-center space-x-2 text-sm text-gray-200">
                        <input
                          type="checkbox"
                          className="form-checkbox"
                          checked={form.isPublished}
                          onChange={(e) => setForm((prev) => ({ ...prev, isPublished: e.target.checked }))}
                        />
                        <span>Açıklama yayımlansın</span>
                      </label>
                      <div className="space-x-3">
                        <button type="button" className="btn-secondary" onClick={() => { setShowModal(false); resetForm(); }}>
                          Vazgeç
                        </button>
                        <button type="submit" className="btn-primary">
                          Kaydet
                        </button>
                      </div>
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </Layout>
  );
};

export default Explanations;
