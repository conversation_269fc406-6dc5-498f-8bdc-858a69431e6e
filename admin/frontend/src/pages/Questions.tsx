import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout';
import { apiDel, apiGet, apiPost, apiPut } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  QuestionMarkCircleIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface LessonOption {
  id: string;
  title: string;
}

interface SubjectOption {
  id: string;
  lesson_id: string;
  title: string;
}

interface ExplanationOption {
  id: string;
  subject_id: string;
  title: string;
}

interface QuestionItem {
  id: string;
  lesson_id: string;
  subject_id: string;
  explanation_id?: string | null;
  type: string;
  difficulty: string;
  source?: string | null;
  stem: string;
  is_published: boolean;
  created_at: string;
}

type StatusFilter = 'all' | 'published' | 'draft';

const questionTypes = [
  { value: 'single_choice', label: '<PERSON><PERSON>' },
  { value: 'multiple_choice', label: '<PERSON><PERSON><PERSON>' },
  { value: 'true_false', label: 'Doğru / Yanlış' },
  { value: 'fill_blank', label: 'Boşluk Doldurma' },
];

const difficultyOptions = [
  { value: 'easy', label: 'Kolay' },
  { value: 'medium', label: 'Orta' },
  { value: 'hard', label: 'Zor' },
];

const Questions: React.FC = () => {
  const [questions, setQuestions] = useState<QuestionItem[]>([]);
  const [lessons, setLessons] = useState<LessonOption[]>([]);
  const [subjects, setSubjects] = useState<SubjectOption[]>([]);
  const [explanationsBySubject, setExplanationsBySubject] = useState<Record<string, ExplanationOption[]>>({});

  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [lessonFilter, setLessonFilter] = useState('');
  const [subjectFilter, setSubjectFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState<StatusFilter>('all');

  const [showModal, setShowModal] = useState(false);
  const [editing, setEditing] = useState<QuestionItem | null>(null);
  const [form, setForm] = useState({
    lessonId: '',
    subjectId: '',
    explanationId: '',
    type: 'single_choice',
    difficulty: 'medium',
    source: '',
    stem: '',
    isPublished: true,
  });

  useEffect(() => {
    const bootstrap = async () => {
      try {
        const [lessonRes, subjectRes] = await Promise.all([
          apiGet<{ data: LessonOption[] }>('/lessons?limit=200'),
          apiGet<{ data: SubjectOption[] }>('/subjects?limit=200'),
        ]);
        setLessons(lessonRes.data ?? []);
        setSubjects(subjectRes.data ?? []);
      } catch (error) {
        console.error('Lookup load failed', error);
      }
    };
    bootstrap();
  }, []);

  const loadQuestions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '200');
      if (search.trim()) params.append('q', search.trim());
      if (lessonFilter) params.append('lesson_id', lessonFilter);
      if (subjectFilter) params.append('subject_id', subjectFilter);
      if (typeFilter) params.append('type', typeFilter);
      if (difficultyFilter) params.append('difficulty', difficultyFilter);
      if (statusFilter === 'published') params.append('is_published', 'true');
      if (statusFilter === 'draft') params.append('is_published', 'false');

      const response = await apiGet<{ data: QuestionItem[]; total: number }>(`/questions?${params.toString()}`);
      setQuestions(response.data ?? []);
    } catch (error) {
      console.error('Questions load failed', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadQuestions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lessonFilter, subjectFilter, typeFilter, difficultyFilter, statusFilter]);

  const filteredQuestions = useMemo(() => {
    if (!search.trim()) return questions;
    const needle = search.trim().toLowerCase();
    return questions.filter((question) =>
      question.stem.toLowerCase().includes(needle) ||
      (question.source ?? '').toLowerCase().includes(needle)
    );
  }, [questions, search]);

  const lessonName = (lessonId: string) => lessons.find((l) => l.id === lessonId)?.title ?? '-';
  const subjectName = (subjectId: string) => subjects.find((s) => s.id === subjectId)?.title ?? '-';
  const explanationName = (explanationId?: string | null) => {
    if (!explanationId) return '-';
    const all = Object.values(explanationsBySubject).flat();
    return all.find((exp) => exp.id === explanationId)?.title ?? '-';
  };

  const ensureExplanations = async (subjectId: string) => {
    if (!subjectId || explanationsBySubject[subjectId]) return;
    try {
      const response = await apiGet<{ data: ExplanationOption[] }>(`/explanations?subject_id=${subjectId}&limit=200`);
      setExplanationsBySubject((prev) => ({ ...prev, [subjectId]: response.data ?? [] }));
    } catch (error) {
      console.error('Explanations load failed', error);
    }
  };

  const resetForm = () => {
    setForm({
      lessonId: '',
      subjectId: '',
      explanationId: '',
      type: 'single_choice',
      difficulty: 'medium',
      source: '',
      stem: '',
      isPublished: true,
    });
    setEditing(null);
  };

  const openCreate = () => {
    resetForm();
    setShowModal(true);
  };

  const openEdit = async (item: QuestionItem) => {
    setEditing(item);
    await ensureExplanations(item.subject_id);
    setForm({
      lessonId: item.lesson_id,
      subjectId: item.subject_id,
      explanationId: item.explanation_id ?? '',
      type: item.type,
      difficulty: item.difficulty,
      source: item.source ?? '',
      stem: item.stem,
      isPublished: item.is_published,
    });
    setShowModal(true);
  };

  const saveQuestion = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const payload = {
      lesson_id: form.lessonId,
      subject_id: form.subjectId,
      explanation_id: form.explanationId || null,
      type: form.type,
      difficulty: form.difficulty,
      source: form.source.trim() || null,
      stem: form.stem.trim(),
      is_published: form.isPublished,
    };

    try {
      if (editing) {
        await apiPut(`/questions/${editing.id}`, payload);
      } else {
        await apiPost('/questions', payload);
      }
      setShowModal(false);
      resetForm();
      await loadQuestions();
    } catch (error) {
      console.error('Question save failed', error);
      alert('Soru kaydedilirken bir hata oluştu.');
    }
  };

  const deleteQuestion = async (id: string) => {
    if (!window.confirm('Bu soruyu silmek istediğinizden emin misiniz?')) return;
    try {
      await apiDel(`/questions/${id}`);
      await loadQuestions();
    } catch (error) {
      console.error('Question delete failed', error);
      alert('Soru silinemedi.');
    }
  };

  const subjectsForLesson = useMemo(() => {
    if (!form.lessonId) return subjects;
    return subjects.filter((subject) => subject.lesson_id === form.lessonId);
  }, [subjects, form.lessonId]);

  const subjectFilterOptions = useMemo(() => {
    if (!lessonFilter) return subjects;
    return subjects.filter((subject) => subject.lesson_id === lessonFilter);
  }, [subjects, lessonFilter]);

  const currentExplanationOptions = form.subjectId ? (explanationsBySubject[form.subjectId] ?? []) : [];

  return (
    <Layout>
      <div className="space-y-6">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Sorular</h1>
            <p className="mt-1 text-sm text-gray-300">Ders ve konu bazlı soru bankanızı yönetin.</p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <div className="inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200">
              <button className={`px-3 py-1.5 ${statusFilter === 'all' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('all')}>
                Tümü
              </button>
              <button className={`px-3 py-1.5 ${statusFilter === 'published' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('published')}>
                Yayında
              </button>
              <button className={`px-3 py-1.5 ${statusFilter === 'draft' ? 'bg-slate-700 text-white' : ''}`} onClick={() => setStatusFilter('draft')}>
                Taslak
              </button>
            </div>
            <button className="btn-primary flex items-center" onClick={openCreate}>
              <PlusIcon className="h-4 w-4 mr-2" />
              Yeni Soru
            </button>
          </div>
        </div>

        <div className="grid gap-4 lg:grid-cols-5 md:grid-cols-3 sm:grid-cols-2">
          <div className="card flex items-center space-x-3 lg:col-span-2">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <input
              className="input-field"
              placeholder="Soru ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="card">
            <label className="form-label">Ders</label>
            <select
              className="input-field"
              value={lessonFilter}
              onChange={(e) => {
                setLessonFilter(e.target.value);
                setSubjectFilter('');
              }}
            >
              <option value="">Tümü</option>
              {lessons.map((lesson) => (
                <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
              ))}
            </select>
          </div>
          <div className="card">
            <label className="form-label">Konu</label>
            <select
              className="input-field"
              value={subjectFilter}
              onChange={(e) => setSubjectFilter(e.target.value)}
              disabled={!lessonFilter}
            >
              <option value="">Tümü</option>
              {subjectFilterOptions.map((subject) => (
                <option key={subject.id} value={subject.id}>{subject.title}</option>
              ))}
            </select>
          </div>
          <div className="card">
            <label className="form-label">Soru Tipi</label>
            <select
              className="input-field"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="">Tümü</option>
              {questionTypes.map((type) => (
                <option key={type.value} value={type.value}>{type.label}</option>
              ))}
            </select>
          </div>
          <div className="card">
            <label className="form-label">Zorluk</label>
            <select
              className="input-field"
              value={difficultyFilter}
              onChange={(e) => setDifficultyFilter(e.target.value)}
            >
              <option value="">Tümü</option>
              {difficultyOptions.map((difficulty) => (
                <option key={difficulty.value} value={difficulty.value}>{difficulty.label}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="card p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500" />
              <span className="ml-3 text-gray-300">Sorular yükleniyor...</span>
            </div>
          ) : filteredQuestions.length === 0 ? (
            <div className="py-12 text-center text-gray-400">Kriterlere uygun soru bulunamadı.</div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-800/80">
                  <tr>
                    <th className="table-header">Soru</th>
                    <th className="table-header hidden lg:table-cell">Ders / Konu</th>
                    <th className="table-header hidden md:table-cell">Tip & Zorluk</th>
                    <th className="table-header hidden md:table-cell">Açıklama</th>
                    <th className="table-header hidden sm:table-cell">Durum</th>
                    <th className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-800 bg-slate-900/40">
                  {filteredQuestions.map((question) => (
                    <tr key={question.id} className="hover:bg-slate-800/60">
                      <td className="table-cell">
                        <div className="flex items-start space-x-3">
                          <div className="h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center">
                            <QuestionMarkCircleIcon className="h-5 w-5 text-primary-400" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-white line-clamp-2 max-w-md">{question.stem}</p>
                            {question.source && (
                              <p className="text-xs text-gray-400 mt-1">Kaynak: {question.source}</p>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell hidden lg:table-cell text-sm text-gray-300">
                        <div className="space-y-1">
                          <p>{lessonName(question.lesson_id)}</p>
                          <p className="text-xs text-gray-400">{subjectName(question.subject_id)}</p>
                        </div>
                      </td>
                      <td className="table-cell hidden md:table-cell text-sm text-gray-300">
                        <div className="space-y-1">
                          <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-slate-700/70 text-slate-200">
                            <TagIcon className="h-4 w-4 mr-1" />
                            {questionTypes.find((t) => t.value === question.type)?.label ?? question.type}
                          </span>
                          <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                            question.difficulty === 'easy'
                              ? 'bg-emerald-500/20 text-emerald-300'
                              : question.difficulty === 'medium'
                              ? 'bg-amber-500/20 text-amber-300'
                              : 'bg-rose-500/20 text-rose-300'
                          }`}>
                            {difficultyOptions.find((d) => d.value === question.difficulty)?.label ?? question.difficulty}
                          </span>
                        </div>
                      </td>
                      <td className="table-cell hidden md:table-cell text-sm text-gray-300">
                        {explanationName(question.explanation_id)}
                      </td>
                      <td className="table-cell hidden sm:table-cell">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                          question.is_published
                            ? 'bg-emerald-500/20 text-emerald-300'
                            : 'bg-amber-500/20 text-amber-300'
                        }`}>
                          {question.is_published ? 'Yayında' : 'Taslak'}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button onClick={() => openEdit(question)} className="icon-button text-primary-400 hover:text-primary-200">
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button onClick={() => deleteQuestion(question.id)} className="icon-button text-red-400 hover:text-red-200">
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <Transition show={showModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
          <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0" enterTo="opacity-100" leave="ease-in duration-150" leaveFrom="opacity-100" leaveTo="opacity-0">
            <div className="fixed inset-0 bg-black/60" />
          </Transition.Child>
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child as={Fragment} enter="ease-out duration-200" enterFrom="opacity-0 scale-95" enterTo="opacity-100 scale-100" leave="ease-in duration-150" leaveFrom="opacity-100 scale-100" leaveTo="opacity-0 scale-95">
                <Dialog.Panel className="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all">
                  <Dialog.Title className="text-xl font-semibold text-white mb-4">
                    {editing ? 'Soruyu Düzenle' : 'Yeni Soru Oluştur'}
                  </Dialog.Title>
                  <form onSubmit={saveQuestion} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <label className="form-label">Ders</label>
                        <select
                          className="input-field"
                          value={form.lessonId}
                          onChange={(e) => {
                            setForm((prev) => ({ ...prev, lessonId: e.target.value, subjectId: '', explanationId: '' }));
                          }}
                          required
                        >
                          <option value="">Seçiniz</option>
                          {lessons.map((lesson) => (
                            <option key={lesson.id} value={lesson.id}>{lesson.title}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="form-label">Konu</label>
                        <select
                          className="input-field"
                          value={form.subjectId}
                          onChange={async (e) => {
                            const value = e.target.value;
                            setForm((prev) => ({ ...prev, subjectId: value, explanationId: '' }));
                            if (value) await ensureExplanations(value);
                          }}
                          required
                          disabled={!form.lessonId}
                        >
                          <option value="">Seçiniz</option>
                          {subjectsForLesson.map((subject) => (
                            <option key={subject.id} value={subject.id}>{subject.title}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="form-label">Açıklama (Opsiyonel)</label>
                        <select
                          className="input-field"
                          value={form.explanationId}
                          onChange={(e) => setForm((prev) => ({ ...prev, explanationId: e.target.value }))}
                          disabled={!form.subjectId}
                        >
                          <option value="">Seçiniz</option>
                          {currentExplanationOptions.map((exp) => (
                            <option key={exp.id} value={exp.id}>{exp.title}</option>
                          ))}
                        </select>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="form-label">Soru Tipi</label>
                          <select
                            className="input-field"
                            value={form.type}
                            onChange={(e) => setForm((prev) => ({ ...prev, type: e.target.value }))}
                            required
                          >
                            {questionTypes.map((type) => (
                              <option key={type.value} value={type.value}>{type.label}</option>
                            ))}
                          </select>
                        </div>
                        <div>
                          <label className="form-label">Zorluk</label>
                          <select
                            className="input-field"
                            value={form.difficulty}
                            onChange={(e) => setForm((prev) => ({ ...prev, difficulty: e.target.value }))}
                            required
                          >
                            {difficultyOptions.map((difficulty) => (
                              <option key={difficulty.value} value={difficulty.value}>{difficulty.label}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="form-label">Soru Metni</label>
                      <textarea
                        className="input-field"
                        rows={5}
                        value={form.stem}
                        onChange={(e) => setForm((prev) => ({ ...prev, stem: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <label className="form-label">Kaynak (Opsiyonel)</label>
                      <input
                        className="input-field"
                        value={form.source}
                        onChange={(e) => setForm((prev) => ({ ...prev, source: e.target.value }))}
                        placeholder="Kitap, deneme, yıl vb."
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <label className="inline-flex items-center space-x-2 text-sm text-gray-200">
                        <input
                          type="checkbox"
                          className="form-checkbox"
                          checked={form.isPublished}
                          onChange={(e) => setForm((prev) => ({ ...prev, isPublished: e.target.checked }))}
                        />
                        <span>Soru yayımlansın</span>
                      </label>
                      <div className="space-x-3">
                        <button type="button" className="btn-secondary" onClick={() => { setShowModal(false); resetForm(); }}>
                          Vazgeç
                        </button>
                        <button type="submit" className="btn-primary">Kaydet</button>
                      </div>
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </Layout>
  );
};

export default Questions;
