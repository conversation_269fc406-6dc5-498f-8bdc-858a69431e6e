import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet } from '../lib/api';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ServerStackIcon
} from '@heroicons/react/24/outline';

interface LogItem {
  id: string;
  title: string;
  message: string;
  entity: string;
  type: string;
  proto?: string;
  ip?: string;
  created_at: string;
}

const Logs: React.FC = () => {
  const [logs, setLogs] = useState<LogItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [entityFilter, setEntityFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');

  const loadLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '200');
      if (entityFilter) params.append('entity', entityFilter);
      if (typeFilter) params.append('type', typeFilter);
      if (search.trim()) params.append('q', search.trim());

      const response = await apiGet<{ data: LogItem[]; total: number }>(`/logs?${params.toString()}`);
      setLogs(response.data ?? []);
    } catch (error) {
      console.error('Logs load failed', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLogs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [entityFilter, typeFilter]);

  const filteredLogs = useMemo(() => {
    if (!search.trim()) return logs;
    const needle = search.trim().toLowerCase();
    return logs.filter((log) =>
      log.title.toLowerCase().includes(needle) ||
      (log.message ?? '').toLowerCase().includes(needle) ||
      (log.entity ?? '').toLowerCase().includes(needle)
    );
  }, [logs, search]);

  const entities = useMemo(() => Array.from(new Set(logs.map((log) => log.entity))).filter(Boolean), [logs]);
  const types = useMemo(() => Array.from(new Set(logs.map((log) => log.type))).filter(Boolean), [logs]);

  return (
    <Layout>
      <div className="space-y-6">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Sistem Logları</h1>
            <p className="mt-1 text-sm text-gray-300">Uygulama olaylarını takip edin, hataları hızlıca tespit edin.</p>
          </div>
          <button className="btn-secondary flex items-center space-x-2" onClick={loadLogs}>
            <ServerStackIcon className="h-5 w-5" />
            <span>Yenile</span>
          </button>
        </div>

        <div className="grid gap-4 md:grid-cols-3">
          <div className="card flex items-center space-x-3 md:col-span-2">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            <input
              className="input-field"
              placeholder="Log ara..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="card flex items-center space-x-3">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="input-field"
              value={entityFilter}
              onChange={(e) => setEntityFilter(e.target.value)}
            >
              <option value="">Tüm Entity'ler</option>
              {entities.map((entity) => (
                <option key={entity} value={entity}>{entity}</option>
              ))}
            </select>
          </div>
          <div className="card flex items-center space-x-3">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              className="input-field"
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
            >
              <option value="">Tüm Tipler</option>
              {types.map((type) => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="card p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500" />
              <span className="ml-3 text-gray-300">Loglar yükleniyor...</span>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="py-12 text-center text-gray-400">Kayıt bulunamadı.</div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-800/80">
                  <tr>
                    <th className="table-header">Başlık</th>
                    <th className="table-header hidden md:table-cell">Entity</th>
                    <th className="table-header hidden md:table-cell">Tip</th>
                    <th className="table-header hidden lg:table-cell">IP</th>
                    <th className="table-header">Mesaj</th>
                    <th className="table-header hidden md:table-cell">Zaman</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-800 bg-slate-900/40">
                  {filteredLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-slate-800/60">
                      <td className="table-cell text-sm text-white font-medium">{log.title}</td>
                      <td className="table-cell hidden md:table-cell text-sm text-gray-300">{log.entity || '-'}</td>
                      <td className="table-cell hidden md:table-cell">{log.type || '-'}</td>
                      <td className="table-cell hidden lg:table-cell text-sm text-gray-300">{log.ip || '-'}</td>
                      <td className="table-cell text-xs text-gray-200 whitespace-pre-wrap max-w-xl">{log.message}</td>
                      <td className="table-cell hidden md:table-cell text-xs text-gray-400">
                        {new Date(log.created_at).toLocaleString('tr-TR')}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Logs;
