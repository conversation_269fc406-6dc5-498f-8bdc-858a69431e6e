import React, { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import { apiGet } from '../lib/api';
import {
  UsersIcon,
  BookOpenIcon,
  FolderIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
  ServerStackIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface Stats {
  users: number;
  lessons: number;
  subjects: number;
  explanations: number;
  questions: number;
  logs: number;
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<Stats>({
    users: 0,
    lessons: 0,
    subjects: 0,
    explanations: 0,
    questions: 0,
    logs: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      const [usersRes, lessonsRes, subjectsRes, explanationsRes, questionsRes, logsRes] = await Promise.all([
        apiGet<{ total: number }>('/users?limit=1'),
        apiGet<{ total: number }>('/lessons?limit=1'),
        apiGet<{ total: number }>('/subjects?limit=1'),
        apiGet<{ total: number }>('/explanations?limit=1'),
        apiGet<{ total: number }>('/questions?limit=1'),
        apiGet<{ total: number }>('/logs?limit=1'),
      ]);
      setStats({
        users: usersRes.total ?? 0,
        lessons: lessonsRes.total ?? 0,
        subjects: subjectsRes.total ?? 0,
        explanations: explanationsRes.total ?? 0,
        questions: questionsRes.total ?? 0,
        logs: logsRes.total ?? 0,
      });
    } catch (error) {
      console.error('Dashboard stats failed', error);
    } finally {
      setLoading(false);
    }
  };

  const statCards = [
    {
      name: 'Kullanıcılar',
      value: stats.users,
      icon: UsersIcon,
      gradient: 'from-blue-500 to-cyan-500',
      iconBg: 'bg-blue-500/20',
      href: '/users',
    },
    {
      name: 'Dersler',
      value: stats.lessons,
      icon: BookOpenIcon,
      gradient: 'from-purple-500 to-pink-500',
      iconBg: 'bg-purple-500/20',
      href: '/lessons',
    },
    {
      name: 'Konular',
      value: stats.subjects,
      icon: FolderIcon,
      gradient: 'from-emerald-500 to-teal-500',
      iconBg: 'bg-emerald-500/20',
      href: '/subjects',
    },
    {
      name: 'Açıklamalar',
      value: stats.explanations,
      icon: DocumentTextIcon,
      gradient: 'from-rose-500 to-red-500',
      iconBg: 'bg-rose-500/20',
      href: '/explanations',
    },
    {
      name: 'Sorular',
      value: stats.questions,
      icon: QuestionMarkCircleIcon,
      gradient: 'from-indigo-500 to-violet-500',
      iconBg: 'bg-indigo-500/20',
      href: '/questions',
    },
    {
      name: 'Loglar',
      value: stats.logs,
      icon: ServerStackIcon,
      gradient: 'from-amber-500 to-orange-500',
      iconBg: 'bg-amber-500/20',
      href: '/logs',
    },
  ];

  return (
    <Layout>
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="mt-4 text-lg text-slate-300 max-w-2xl mx-auto">
            KPSS Plus yönetim paneline hoş geldiniz. İçerikleri ve sistemi buradan yönetebilirsiniz.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3">
          {statCards.map((card) => {
            const Icon = card.icon;
            return (
              <a
                key={card.name}
                href={card.href}
                className="group relative bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 hover:bg-slate-800/80 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-slate-400 uppercase tracking-wider">
                      {card.name}
                    </p>
                    <p className="mt-2 text-3xl font-bold text-white">
                      {loading ? (
                        <div className="animate-pulse bg-slate-700 h-8 w-20 rounded-lg"></div>
                      ) : (
                        <span className={`bg-gradient-to-r ${card.gradient} bg-clip-text text-transparent`}>
                          {card.value.toLocaleString()}
                        </span>
                      )}
                    </p>
                  </div>
                  <div className={`${card.iconBg} p-3 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className={`h-8 w-8 bg-gradient-to-r ${card.gradient} bg-clip-text text-transparent`} />
                  </div>
                </div>
                <div className="mt-4 flex items-center text-sm text-slate-400">
                  <span>Son güncelleme: {loading ? 'yükleniyor' : 'az önce'}</span>
                </div>
              </a>
            );
          })}
        </div>

        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Hızlı İşlemler
            </h3>
            <p className="mt-2 text-slate-400">Sık kullanılan yönetim ekranlarına hızlı erişim</p>
          </div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {[
              { href: '/lessons', title: 'Yeni Ders', description: 'Yeni ders ekleyin', icon: BookOpenIcon },
              { href: '/subjects', title: 'Yeni Konu', description: 'Derslere konu ekleyin', icon: FolderIcon },
              { href: '/explanations', title: 'Açıklama Ekle', description: 'Konu içerikleri oluşturun', icon: DocumentTextIcon },
              { href: '/questions', title: 'Soru Ekle', description: 'Soru bankasına yeni kayıt', icon: QuestionMarkCircleIcon },
            ].map((action) => {
              const Icon = action.icon;
              return (
                <a
                  key={action.href}
                  href={action.href}
                  className="group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-blue-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-white mb-2">
                      {action.title}
                    </h3>
                    <p className="text-sm text-slate-400">
                      {action.description}
                    </p>
                  </div>
                </a>
              );
            })}
          </div>
        </div>

        <div className="bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-white">API Aktivitesi</h3>
            <p className="text-sm text-slate-400 mt-1">
              Loglar üzerinden sistem trafiğini izleyin ve sorunları hızla görün.
            </p>
          </div>
          <a
            href="/logs"
            className="btn-secondary flex items-center space-x-2"
          >
            <PlusIcon className="h-5 w-5" />
            <span>Logları Görüntüle</span>
          </a>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;
