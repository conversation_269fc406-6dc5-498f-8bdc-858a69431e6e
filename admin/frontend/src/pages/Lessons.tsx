import React, { useEffect, useMemo, useState } from 'react';
import Layout from '../components/Layout';
import { apiDel, apiGet, apiPost, apiPut } from '../lib/api';
import {
  MagnifyingGlassIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  BookOpenIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface Lesson {
  id: string;
  title: string;
  description?: string;
  body?: string;
  is_published: boolean;
  created_at: string;
  updated_at: string;
}

type PublishFilter = 'all' | 'published' | 'draft';

const Lessons: React.FC = () => {
  const [items, setItems] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const [filter, setFilter] = useState<PublishFilter>('all');

  const [showModal, setShowModal] = useState(false);
  const [editing, setEditing] = useState<Lesson | null>(null);
  const [form, setForm] = useState({
    title: '',
    description: '',
    body: '',
    isPublished: true,
  });

  const loadLessons = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      params.append('limit', '200');
      if (search.trim()) params.append('q', search.trim());
      if (filter === 'published') params.append('published', 'true');
      if (filter === 'draft') params.append('published', 'false');

      const response = await apiGet<{ data: Lesson[]; total: number }>(`/lessons?${params.toString()}`);
      setItems(response.data ?? []);
    } catch (error) {
      console.error('Lessons load failed', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLessons();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filter]);

  const filteredItems = useMemo(() => {
    if (!search.trim()) return items;
    const needle = search.trim().toLowerCase();
    return items.filter((lesson) =>
      lesson.title.toLowerCase().includes(needle) ||
      (lesson.description ?? '').toLowerCase().includes(needle)
    );
  }, [items, search]);

  const resetForm = () => {
    setForm({ title: '', description: '', body: '', isPublished: true });
    setEditing(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (lesson: Lesson) => {
    setEditing(lesson);
    setForm({
      title: lesson.title,
      description: lesson.description ?? '',
      body: lesson.body ?? '',
      isPublished: lesson.is_published,
    });
    setShowModal(true);
  };

  const saveLesson = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const payload = {
      title: form.title.trim(),
      description: form.description.trim() || null,
      body: form.body.trim() || null,
      is_published: form.isPublished,
    };

    try {
      if (editing) {
        await apiPut(`/lessons/${editing.id}`, payload);
      } else {
        await apiPost('/lessons', payload);
      }
      setShowModal(false);
      resetForm();
      await loadLessons();
    } catch (error) {
      console.error('Lesson save failed', error);
      alert('Ders kaydedilirken bir hata oluştu.');
    }
  };

  const deleteLesson = async (id: string) => {
    if (!window.confirm('Bu dersi silmek istediğinizden emin misiniz?')) return;
    try {
      await apiDel(`/lessons/${id}`);
      await loadLessons();
    } catch (error) {
      console.error('Lesson delete failed', error);
      alert('Ders silinemedi.');
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="sm:flex sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-100">Dersler</h1>
            <p className="mt-1 text-sm text-gray-300">
              Tüm dersler için başlık, açıklama ve durum yönetimini buradan yapabilirsiniz.
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <div className="inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200">
              <button
                className={`px-3 py-1.5 ${filter === 'all' ? 'bg-slate-700 text-white' : ''}`}
                onClick={() => setFilter('all')}
              >
                Tümü
              </button>
              <button
                className={`px-3 py-1.5 ${filter === 'published' ? 'bg-slate-700 text-white' : ''}`}
                onClick={() => setFilter('published')}
              >
                Yayında
              </button>
              <button
                className={`px-3 py-1.5 ${filter === 'draft' ? 'bg-slate-700 text-white' : ''}`}
                onClick={() => setFilter('draft')}
              >
                Taslak
              </button>
            </div>
            <button
              type="button"
              onClick={openCreateModal}
              className="btn-primary flex items-center"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Yeni Ders
            </button>
          </div>
        </div>

        <div className="card">
          <div className="flex items-center">
            <div className="relative flex-1">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                className="input-field pl-10"
                placeholder="Ders ara..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
              />
            </div>
          </div>
        </div>

        <div className="card p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500" />
              <span className="ml-3 text-gray-300">Dersler yükleniyor...</span>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="py-12 text-center text-gray-400">
              Kriterlere uygun ders bulunamadı.
            </div>
          ) : (
            <div className="table-container">
              <table className="min-w-full divide-y divide-slate-700">
                <thead className="bg-slate-800/80">
                  <tr>
                    <th className="table-header">Ders</th>
                    <th className="table-header hidden md:table-cell">Açıklama</th>
                    <th className="table-header hidden md:table-cell">Oluşturulma</th>
                    <th className="table-header">Durum</th>
                    <th className="table-header">İşlemler</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-slate-800 bg-slate-900/40">
                  {filteredItems.map((lesson) => (
                    <tr key={lesson.id} className="hover:bg-slate-800/60">
                      <td className="table-cell">
                        <div className="flex items-start space-x-3">
                          <div className="h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center">
                            <BookOpenIcon className="h-5 w-5 text-primary-400" />
                          </div>
                          <div>
                            <p className="text-sm font-medium text-white">{lesson.title}</p>
                            {lesson.body && (
                              <p className="text-xs text-gray-400 truncate max-w-xs">{lesson.body}</p>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="table-cell hidden md:table-cell text-sm text-gray-300">
                        {lesson.description || '-'}
                      </td>
                      <td className="table-cell hidden md:table-cell text-xs text-gray-400">
                        <div className="flex items-center space-x-2">
                          <ClockIcon className="h-4 w-4" />
                          <span>{new Date(lesson.created_at).toLocaleDateString('tr-TR')}</span>
                        </div>
                      </td>
                      <td className="table-cell">
                        <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ${
                          lesson.is_published
                            ? 'bg-emerald-500/20 text-emerald-300'
                            : 'bg-amber-500/20 text-amber-300'
                        }`}>
                          <CheckCircleIcon className="h-4 w-4 mr-1" />
                          {lesson.is_published ? 'Yayında' : 'Taslak'}
                        </span>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => openEditModal(lesson)}
                            className="icon-button text-primary-400 hover:text-primary-200"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => deleteLesson(lesson.id)}
                            className="icon-button text-red-400 hover:text-red-200"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      <Transition show={showModal} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={() => setShowModal(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-200"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-150"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black/60" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-200"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-150"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-3xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all">
                  <Dialog.Title className="text-xl font-semibold text-white mb-4">
                    {editing ? 'Dersi Düzenle' : 'Yeni Ders Oluştur'}
                  </Dialog.Title>
                  <form onSubmit={saveLesson} className="space-y-6">
                    <div>
                      <label className="form-label">Başlık</label>
                      <input
                        type="text"
                        className="input-field"
                        value={form.title}
                        onChange={(e) => setForm((prev) => ({ ...prev, title: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <label className="form-label">Kısa Açıklama</label>
                      <textarea
                        className="input-field"
                        rows={2}
                        value={form.description}
                        onChange={(e) => setForm((prev) => ({ ...prev, description: e.target.value }))}
                        placeholder="Opsiyonel"
                      />
                    </div>
                    <div>
                      <label className="form-label">İçerik</label>
                      <textarea
                        className="input-field"
                        rows={6}
                        value={form.body}
                        onChange={(e) => setForm((prev) => ({ ...prev, body: e.target.value }))}
                        placeholder="Ders metni veya açıklaması"
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <label className="inline-flex items-center space-x-2 text-sm text-gray-200">
                        <input
                          type="checkbox"
                          className="form-checkbox"
                          checked={form.isPublished}
                          onChange={(e) => setForm((prev) => ({ ...prev, isPublished: e.target.checked }))}
                        />
                        <span>Ders yayımlansın</span>
                      </label>
                      <div className="space-x-3">
                        <button
                          type="button"
                          className="btn-secondary"
                          onClick={() => {
                            setShowModal(false);
                            resetForm();
                          }}
                        >
                          Vazgeç
                        </button>
                        <button type="submit" className="btn-primary">
                          Kaydet
                        </button>
                      </div>
                    </div>
                  </form>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </Layout>
  );
};

export default Lessons;
