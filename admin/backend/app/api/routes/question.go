package routes

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/question"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// QuestionRoutes registers endpoints for question management.
func QuestionRoutes(r *gin.RouterGroup, s question.Service) {
	r.GET("/questions", listQuestions(s))
	r.POST("/questions", createQuestion(s))
	r.GET("/questions/:id", getQuestion(s))
	r.PUT("/questions/:id", updateQuestion(s))
	r.DELETE("/questions/:id", deleteQuestion(s))
}

func listQuestions(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit, offset, order := parseListParams(c, 50, 200)
		opts := question.ListOptions{
			Limit:      limit,
			Offset:     offset,
			Order:      order,
			Search:     strings.TrimSpace(c.Query("q")),
			Type:       strings.TrimSpace(c.Query("type")),
			Difficulty: strings.TrimSpace(c.Query("difficulty")),
		}
		if v := strings.TrimSpace(c.Query("lesson_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
				return
			}
			opts.LessonID = &parsed
		}
		if v := strings.TrimSpace(c.Query("subject_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
				return
			}
			opts.SubjectID = &parsed
		}
		if v := strings.TrimSpace(c.Query("explanation_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation_id"})
				return
			}
			opts.ExplanationID = &parsed
		}
		if published, ok := parseOptionalBool(c.Query("is_published")); ok {
			opts.Published = published
		}
		list, total, err := s.List(opts)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.QuestionListResponse{Data: list, Total: total})
	}
}

func getQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid question id"})
			return
		}
		entity, err := s.Get(id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.QuestionResponse{Data: *entity})
	}
}

func createQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.CreateQuestionRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		lessonID, err := uuid.Parse(strings.TrimSpace(body.LessonID))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
			return
		}
		subjectID, err := uuid.Parse(strings.TrimSpace(body.SubjectID))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
			return
		}
		var explanationID *uuid.UUID
		if body.ExplanationID != nil && strings.TrimSpace(*body.ExplanationID) != "" {
			parsed, err := uuid.Parse(strings.TrimSpace(*body.ExplanationID))
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation_id"})
				return
			}
			explanationID = &parsed
		}
		input := &question.CreateInput{
			LessonID:      lessonID,
			SubjectID:     subjectID,
			ExplanationID: explanationID,
			Type:          body.Type,
			Difficulty:    body.Difficulty,
			Source:        body.Source,
			Stem:          body.Stem,
			IsPublished:   body.IsPublished,
		}
		created, err := s.Create(input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, dtos.QuestionResponse{Data: *created})
	}
}

func updateQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid question id"})
			return
		}

		payload, err := readBody(c)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		var body dtos.UpdateQuestionRequest
		if err := json.Unmarshal(payload, &body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		var raw map[string]any
		if err := json.Unmarshal(payload, &raw); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		var lessonID *uuid.UUID
		if body.LessonID != nil {
			parsed, err := uuid.Parse(strings.TrimSpace(*body.LessonID))
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
				return
			}
			lessonID = &parsed
		}
		var subjectID *uuid.UUID
		if body.SubjectID != nil {
			parsed, err := uuid.Parse(strings.TrimSpace(*body.SubjectID))
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
				return
			}
			subjectID = &parsed
		}
		var explanationID *uuid.UUID
		explanationIDSet := false
		if _, ok := raw["explanation_id"]; ok {
			explanationIDSet = true
			if body.ExplanationID != nil && strings.TrimSpace(*body.ExplanationID) != "" {
				parsed, err := uuid.Parse(strings.TrimSpace(*body.ExplanationID))
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation_id"})
					return
				}
				explanationID = &parsed
			} else {
				explanationID = nil
			}
		}
		input := &question.UpdateInput{
			LessonID:         lessonID,
			SubjectID:        subjectID,
			ExplanationID:    explanationID,
			ExplanationIDSet: explanationIDSet,
			Type:             body.Type,
			Difficulty:       body.Difficulty,
			Source:           body.Source,
			Stem:             body.Stem,
			IsPublished:      body.IsPublished,
		}
		updated, err := s.Update(id, input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.QuestionResponse{Data: *updated})
	}
}

func deleteQuestion(s question.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid question id"})
			return
		}
		if err := s.Delete(id); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.MessageResponse{Message: "deleted"})
	}
}

func readBody(c *gin.Context) ([]byte, error) {
	defer c.Request.Body.Close()
	data, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return nil, err
	}
	c.Request.Body = io.NopCloser(bytes.NewBuffer(data))
	return data, nil
}
