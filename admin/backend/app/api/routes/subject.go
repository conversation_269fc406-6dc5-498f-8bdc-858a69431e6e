package routes

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/subject"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// SubjectRoutes registers subject management endpoints.
func SubjectRoutes(r *gin.RouterGroup, s subject.Service) {
	r.GET("/subjects", listSubjects(s))
	r.POST("/subjects", createSubject(s))
	r.GET("/subjects/:id", getSubject(s))
	r.PUT("/subjects/:id", updateSubject(s))
	r.DELETE("/subjects/:id", deleteSubject(s))
}

func listSubjects(s subject.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit, offset, order := parseListParams(c, 50, 200)
		var lessonID *uuid.UUID
		if v := strings.TrimSpace(c.Query("lesson_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
				return
			}
			lessonID = &parsed
		}
		var mainSubjectID *uuid.UUID
		if v := strings.TrimSpace(c.Query("main_subject_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid main_subject_id"})
				return
			}
			mainSubjectID = &parsed
		}
		active, _ := parseOptionalBool(c.Query("is_active"))
		opts := subject.ListOptions{
			LessonID:      lessonID,
			MainSubjectID: mainSubjectID,
			Search:        strings.TrimSpace(c.Query("q")),
			Active:        active,
			Limit:         limit,
			Offset:        offset,
			Order:         order,
		}
		list, total, err := s.List(opts)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.SubjectListResponse{Data: list, Total: total})
	}
}

func getSubject(s subject.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}
		entity, err := s.Get(id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.SubjectResponse{Data: *entity})
	}
}

func createSubject(s subject.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.CreateSubjectRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		lessonID, err := uuid.Parse(strings.TrimSpace(body.LessonID))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
			return
		}
		var mainSubjectID *uuid.UUID
		if body.MainSubjectID != nil && strings.TrimSpace(*body.MainSubjectID) != "" {
			parsed, err := uuid.Parse(strings.TrimSpace(*body.MainSubjectID))
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid main_subject_id"})
				return
			}
			mainSubjectID = &parsed
		}
		input := &subject.CreateInput{
			LessonID:      lessonID,
			MainSubjectID: mainSubjectID,
			Title:         body.Title,
			Description:   body.Description,
			IsActive:      body.IsActive,
		}
		created, err := s.Create(input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, dtos.SubjectResponse{Data: *created})
	}
}

func updateSubject(s subject.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}
		var body dtos.UpdateSubjectRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		var lessonID *uuid.UUID
		if body.LessonID != nil {
			if strings.TrimSpace(*body.LessonID) == "" {
				lessonID = nil
			} else {
				parsed, err := uuid.Parse(strings.TrimSpace(*body.LessonID))
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
					return
				}
				lessonID = &parsed
			}
		}
		var mainSubjectID *uuid.UUID
		mainSubjectIDSet := false
		if body.MainSubjectID != nil {
			mainSubjectIDSet = true
			trimmed := strings.TrimSpace(*body.MainSubjectID)
			if trimmed == "" {
				mainSubjectID = nil
			} else {
				parsed, err := uuid.Parse(trimmed)
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid main_subject_id"})
					return
				}
				mainSubjectID = &parsed
			}
		}
		input := &subject.UpdateInput{
			LessonID:         lessonID,
			MainSubjectID:    mainSubjectID,
			MainSubjectIDSet: mainSubjectIDSet,
			Title:            body.Title,
			Description:      body.Description,
			IsActive:         body.IsActive,
		}
		updated, err := s.Update(id, input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.SubjectResponse{Data: *updated})
	}
}

func deleteSubject(s subject.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject id"})
			return
		}
		if err := s.Delete(id); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.MessageResponse{Message: "deleted"})
	}
}
