package routes

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	logDomain "github.com/nocytech/kpss-plus-admin/pkg/domains/log"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

// LogRoutes registers admin-facing log listing endpoints.
func LogRoutes(r *gin.RouterGroup, s logDomain.Service) {
	r.GET("/logs", listLogs(s))
}

// LogInternalRoutes registers machine-to-machine log creation endpoints.
func LogInternalRoutes(r *gin.RouterGroup, s logDomain.Service) {
	r.POST("/logs", createLog(s))
}

func listLogs(s logDomain.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit, offset, order := parseListParams(c, 50, 200)
		opts := logDomain.ListOptions{
			Entity: strings.TrimSpace(c.Query("entity")),
			Type:   strings.TrimSpace(c.Query("type")),
			Search: strings.TrimSpace(c.Query("q")),
			Limit:  limit,
			Offset: offset,
			Order:  order,
		}
		list, total, err := s.List(opts)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.LogListResponse{Data: list, Total: total})
	}
}

func createLog(s logDomain.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body entities.Log
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		if body.Ip == "" {
			body.Ip = c.ClientIP()
		}
		if body.Proto == "" && c.Request != nil {
			body.Proto = c.Request.Proto
		}
		if err := s.Create(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, dtos.LogResponse{Data: body})
	}
}
