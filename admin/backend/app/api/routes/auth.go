package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/auth"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// RegisterAuthRoutes wires public and admin-only auth endpoints
func RegisterAuthRoutes(public *gin.RouterGroup, adminOnly *gin.RouterGroup, s auth.Service) {
	// Public

	public.POST("/auth/login", loginHandler(s))

	// Admin management (admin role required)
	adminOnly.GET("/auth/admins", listAdminsHandler(s))
	adminOnly.POST("/auth/admins", createAdminHandler(s))
	adminOnly.PUT("/auth/admins/:id", updateAdminHandler(s))
	adminOnly.DELETE("/auth/admins/:id", deleteAdminHandler(s))
}

func loginHandler(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.AuthLoginRequest
		if err := c.ShouldBindJSON(&body); err != nil || body.Username == "" || body.Password == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid body"})
			return
		}
		response, err := s.Login(body.Username, body.Password)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid credentials"})
			return
		}
		c.JSON(200, response)
	}
}

func listAdminsHandler(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		list, err := s.ListAdmins()
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		// map to dto
		out := make([]dtos.AdminResponse, 0, len(list))
		for _, u := range list {
			out = append(out, dtos.AdminResponse{ID: u.ID.String(), Username: u.Username, Role: u.Role})
		}
		c.JSON(200, dtos.AdminListResponse{Data: out})
	}
}

func createAdminHandler(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.AdminCreateRequest
		if err := c.ShouldBindJSON(&body); err != nil || body.Username == "" || body.Password == "" || body.Role == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "username, password, role required"})
			return
		}
		u, err := s.CreateAdmin(body.Username, body.Password, body.Role)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(201, dtos.AdminResponse{ID: u.ID.String(), Username: u.Username, Role: u.Role})
	}
}

func updateAdminHandler(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id := c.Param("id")
		var body dtos.AdminUpdateRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid body"})
			return
		}
		if err := s.UpdateAdmin(id, body.Username, body.Password, body.Role); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, dtos.MessageResponse{Message: "updated"})
	}
}

func deleteAdminHandler(s auth.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		if err := s.DeleteAdmin(c.Param("id")); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, dtos.MessageResponse{Message: "deleted"})
	}
}
