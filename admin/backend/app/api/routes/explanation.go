package routes

import (
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/explanation"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/explanationimport"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// ExplanationRoutes registers explanation management endpoints.
const maxImportFileSize int64 = 25 << 20 // 25MB

func ExplanationRoutes(r *gin.RouterGroup, s explanation.Service, importSvc explanationimport.Service) {
	r.GET("/explanations", listExplanations(s))
	r.POST("/explanations", createExplanation(s))
	r.POST("/explanations/import", importExplanations(importSvc))
	r.GET("/explanations/:id", getExplanation(s))
	r.PUT("/explanations/:id", updateExplanation(s))
	r.DELETE("/explanations/:id", deleteExplanation(s))
}

func importExplanations(importSvc explanationimport.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		lessonIDStr := strings.TrimSpace(c.PostForm("lesson_id"))
		if lessonIDStr == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "lesson_id is required"})
			return
		}
		lessonID, err := uuid.Parse(lessonIDStr)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
			return
		}

		fileHeader, err := c.FormFile("file")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "pdf file is required"})
			return
		}
		if fileHeader.Size == 0 {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "pdf file is empty"})
			return
		}
		if maxImportFileSize > 0 && fileHeader.Size > maxImportFileSize {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("pdf file exceeds %dMB limit", maxImportFileSize>>20),
			})
			return
		}

		file, err := fileHeader.Open()
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		defer file.Close()

		data, err := io.ReadAll(file)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": "unable to read pdf"})
			return
		}

		result, err := importSvc.ImportFromPDF(c.Request.Context(), lessonID, fileHeader.Filename, data)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadGateway, gin.H{"error": err.Error()})
			return
		}

		payload := dtos.ExplanationImportPayload{}
		if len(result.Payload.DersNotlari) > 0 {
			payload.DersNotlari = make([]dtos.ExplanationImportTopic, 0, len(result.Payload.DersNotlari))
			for _, topic := range result.Payload.DersNotlari {
				dTopic := dtos.ExplanationImportTopic{
					UstKonu:      topic.UstKonu,
					AltBasliklar: make([]dtos.ExplanationImportSubheading, 0, len(topic.AltBasliklar)),
				}
				for _, sub := range topic.AltBasliklar {
					dTopic.AltBasliklar = append(dTopic.AltBasliklar, dtos.ExplanationImportSubheading{
						Baslik: sub.Baslik,
						Ozet:   sub.Ozet,
					})
				}
				payload.DersNotlari = append(payload.DersNotlari, dTopic)
			}
		}

		c.JSON(http.StatusOK, dtos.ExplanationImportResponse{
			Subjects:     result.Subjects,
			Explanations: result.Explanations,
			Payload:      payload,
		})
	}
}

func listExplanations(s explanation.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit, offset, order := parseListParams(c, 50, 200)
		var lessonID *uuid.UUID
		if v := strings.TrimSpace(c.Query("lesson_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
				return
			}
			lessonID = &parsed
		}
		var subjectID *uuid.UUID
		if v := strings.TrimSpace(c.Query("subject_id")); v != "" {
			parsed, err := uuid.Parse(v)
			if err != nil {
				c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
				return
			}
			subjectID = &parsed
		}
		published, _ := parseOptionalBool(c.Query("is_published"))
		opts := explanation.ListOptions{
			LessonID:  lessonID,
			SubjectID: subjectID,
			Published: published,
			Search:    strings.TrimSpace(c.Query("q")),
			Limit:     limit,
			Offset:    offset,
			Order:     order,
		}
		list, total, err := s.List(opts)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.ExplanationListResponse{Data: list, Total: total})
	}
}

func getExplanation(s explanation.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation id"})
			return
		}
		entity, err := s.Get(id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.ExplanationResponse{Data: *entity})
	}
}

func createExplanation(s explanation.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.CreateExplanationRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		lessonID, err := uuid.Parse(strings.TrimSpace(body.LessonID))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
			return
		}
		subjectID, err := uuid.Parse(strings.TrimSpace(body.SubjectID))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
			return
		}
		input := &explanation.CreateInput{
			LessonID:    lessonID,
			SubjectID:   subjectID,
			Title:       body.Title,
			Summary:     body.Summary,
			Body:        body.Body,
			IsPublished: body.IsPublished,
		}
		created, err := s.Create(input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, dtos.ExplanationResponse{Data: *created})
	}
}

func updateExplanation(s explanation.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation id"})
			return
		}
		var body dtos.UpdateExplanationRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		var lessonID *uuid.UUID
		if body.LessonID != nil {
			trimmed := strings.TrimSpace(*body.LessonID)
			if trimmed != "" {
				parsed, err := uuid.Parse(trimmed)
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson_id"})
					return
				}
				lessonID = &parsed
			}
		}
		var subjectID *uuid.UUID
		if body.SubjectID != nil {
			trimmed := strings.TrimSpace(*body.SubjectID)
			if trimmed != "" {
				parsed, err := uuid.Parse(trimmed)
				if err != nil {
					c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid subject_id"})
					return
				}
				subjectID = &parsed
			}
		}
		input := &explanation.UpdateInput{
			LessonID:    lessonID,
			SubjectID:   subjectID,
			Title:       body.Title,
			Summary:     body.Summary,
			Body:        body.Body,
			IsPublished: body.IsPublished,
		}
		updated, err := s.Update(id, input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.ExplanationResponse{Data: *updated})
	}
}

func deleteExplanation(s explanation.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid explanation id"})
			return
		}
		if err := s.Delete(id); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.MessageResponse{Message: "deleted"})
	}
}
