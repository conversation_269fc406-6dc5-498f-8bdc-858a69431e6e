package routes

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/lesson"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// LessonRoutes registers lesson CRUD endpoints.
func LessonRoutes(r *gin.RouterGroup, s lesson.Service) {
	r.GET("/lessons", listLessons(s))
	r.POST("/lessons", createLesson(s))
	r.GET("/lessons/:id", getLesson(s))
	r.PUT("/lessons/:id", updateLesson(s))
	r.DELETE("/lessons/:id", deleteLesson(s))
}

func listLessons(s lesson.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		limit, offset, order := parseListParams(c, 50, 200)
		published, _ := parseOptionalBool(c.Query("published"))
		opts := lesson.ListOptions{
			Search:    strings.TrimSpace(c.Query("q")),
			Published: published,
			Limit:     limit,
			Offset:    offset,
			Order:     order,
		}
		list, total, err := s.List(opts)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.LessonListResponse{Data: list, Total: total})
	}
}

func getLesson(s lesson.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson id"})
			return
		}
		lessonEntity, err := s.Get(id)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.LessonResponse{Data: *lessonEntity})
	}
}

func createLesson(s lesson.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		var body dtos.CreateLessonRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		if strings.TrimSpace(body.Title) == "" {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "title is required"})
			return
		}
		input := &lesson.CreateInput{
			Title:       body.Title,
			Description: body.Description,
			Body:        body.Body,
			IsPublished: body.IsPublished,
		}
		created, err := s.Create(input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusCreated, dtos.LessonResponse{Data: *created})
	}
}

func updateLesson(s lesson.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson id"})
			return
		}
		var body dtos.UpdateLessonRequest
		if err := c.ShouldBindJSON(&body); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		input := &lesson.UpdateInput{
			Title:       body.Title,
			Description: body.Description,
			Body:        body.Body,
			IsPublished: body.IsPublished,
		}
		updated, err := s.Update(id, input)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.LessonResponse{Data: *updated})
	}
}

func deleteLesson(s lesson.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		id, err := uuid.Parse(c.Param("id"))
		if err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": "invalid lesson id"})
			return
		}
		if err := s.Delete(id); err != nil {
			c.AbortWithStatusJSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		c.JSON(http.StatusOK, dtos.MessageResponse{Message: "deleted"})
	}
}
