package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/user"
	"github.com/nocytech/kpss-plus-admin/pkg/dtos"
)

// RegisterUserRoutes wires user-related admin endpoints
func RegisterUserRoutes(authGroup *gin.RouterGroup, s user.Service) {
	authGroup.GET("/users", listUsersHandler(s))
}

func listUsersHandler(s user.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		q := c.Query("q")
		limit, offset, order := parseListParams(c, 50, 200)
		list, total, err := s.List(q, limit, offset, order)
		if err != nil {
			c.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}
		c.JSON(200, dtos.UserListResponse{Data: list, Total: total})
	}
}

// parseListParams is a light copy of server helper to avoid circular deps
