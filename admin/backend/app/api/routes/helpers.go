package routes

import (
	"strconv"
	"strings"
)

// parseListParams normalizes limit/offset/order parameters.
func parseListParams(paramGetter interface{ Query(string) string }, defLimit, maxLimit int) (int, int, string) {
	limit := defLimit
	if v := paramGetter.Query("limit"); v != "" {
		if n, err := strconv.Atoi(v); err == nil {
			if n < 1 {
				n = 1
			}
			if n > maxLimit {
				n = maxLimit
			}
			limit = n
		}
	}
	offset := 0
	if v := paramGetter.Query("offset"); v != "" {
		if n, err := strconv.Atoi(v); err == nil && n >= 0 {
			offset = n
		}
	}
	order := "created_at DESC"
	if s := strings.TrimSpace(paramGetter.Query("sort")); s != "" {
		switch s {
		case "created_at.asc":
			order = "created_at ASC"
		case "created_at.desc":
			order = "created_at DESC"
		}
	}
	return limit, offset, order
}

// parseOptionalBool converts textual booleans to pointer form.
func parseOptionalBool(raw string) (*bool, bool) {
	if raw == "" {
		return nil, false
	}
	if raw == "1" || strings.EqualFold(raw, "true") || strings.EqualFold(raw, "yes") {
		v := true
		return &v, true
	}
	if raw == "0" || strings.EqualFold(raw, "false") || strings.EqualFold(raw, "no") {
		v := false
		return &v, true
	}
	return nil, false
}
