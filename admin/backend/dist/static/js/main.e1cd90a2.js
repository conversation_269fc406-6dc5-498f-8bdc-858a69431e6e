/*! For license information please see main.e1cd90a2.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),a=n(43),l=n(950);function i(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(i(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,m=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),h=Symbol.for("react.portal"),v=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),b=Symbol.for("react.profiler"),y=Symbol.for("react.provider"),x=Symbol.for("react.consumer"),w=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),S=Symbol.for("react.suspense"),j=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),E=Symbol.for("react.lazy");Symbol.for("react.scope");var C=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var T=Symbol.iterator;function O(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=T&&e[T]||e["@@iterator"])?e:null}var L=Symbol.for("react.client.reference");function R(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===L?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case v:return"Fragment";case b:return"Profiler";case g:return"StrictMode";case S:return"Suspense";case j:return"SuspenseList";case C:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case h:return"Portal";case w:return(e.displayName||"Context")+".Provider";case x:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:R(e.type)||"Memo";case E:t=e._payload,e=e._init;try{return R(e(t))}catch(Bl){}}return null}var _=Array.isArray,A=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=l.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z={pending:!1,data:null,method:null,action:null},D=[],M=-1;function I(e){return{current:e}}function U(e){0>M||(e.current=D[M],D[M]=null,M--)}function B(e,t){M++,D[M]=e.current,e.current=t}var H=I(null),W=I(null),q=I(null),V=I(null);function K(e,t){switch(B(q,t),B(W,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?id(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=id(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(H),B(H,e)}function $(){U(H),U(W),U(q)}function Q(e){null!==e.memoizedState&&B(V,e);var t=H.current,n=od(t,e.type);t!==n&&(B(W,e),B(H,n))}function Y(e){W.current===e&&(U(H),U(W)),V.current===e&&(U(V),Gd._currentValue=z)}var G=Object.prototype.hasOwnProperty,X=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,le=r.unstable_NormalPriority,ie=r.unstable_LowPriority,oe=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"===typeof se&&ue(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(t){}}var me=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(pe(e)/he|0)|0},pe=Math.log,he=Math.LN2;var ve=256,ge=4194304;function be(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function ye(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,l=e.suspendedLanes,i=e.pingedLanes;e=e.warmLanes;var o=134217727&r;return 0!==o?0!==(r=o&~l)?a=be(r):0!==(i&=o)?a=be(i):n||0!==(n=o&~e)&&(a=be(n)):0!==(o=r&~l)?a=be(o):0!==i?a=be(i):n||0!==(n=r&~e)&&(a=be(n)),0===a?0:0!==t&&t!==a&&0===(t&l)&&((l=a&-a)>=(n=t&-t)||32===l&&0!==(4194048&n))?t:a}function xe(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=ve;return 0===(4194048&(ve<<=1))&&(ve=256),e}function Se(){var e=ge;return 0===(62914560&(ge<<=1))&&(ge=4194304),e}function je(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ne(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ee(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-me(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ce(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-me(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Te(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Oe(){var e=F.p;return 0!==e?e:void 0===(e=window.event)?32:ff(e.type)}var Le=Math.random().toString(36).slice(2),Re="__reactFiber$"+Le,_e="__reactProps$"+Le,Ae="__reactContainer$"+Le,Fe="__reactEvents$"+Le,ze="__reactListeners$"+Le,De="__reactHandles$"+Le,Me="__reactResources$"+Le,Ie="__reactMarker$"+Le;function Ue(e){delete e[Re],delete e[_e],delete e[Fe],delete e[ze],delete e[De]}function Be(e){var t=e[Re];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Re]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wd(e);null!==e;){if(n=e[Re])return n;e=wd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Re]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function We(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(i(33))}function qe(e){var t=e[Me];return t||(t=e[Me]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[Ie]=!0}var Ke=new Set,$e={};function Qe(e,t){Ye(e,t),Ye(e+"Capture",t)}function Ye(e,t){for($e[e]=t,e=0;e<t.length;e++)Ke.add(t[e])}var Ge,Xe,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,G.call(et,a)||!G.call(Ze,a)&&(Je.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Ge)try{throw Error()}catch(Bl){var t=Bl.stack.trim().match(/\n( *(at )?)/);Ge=t&&t[1]||"",Xe=-1<Bl.stack.indexOf("\n    at")?" (<anonymous>)":-1<Bl.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Xe}var lt=!1;function it(e,t){if(!e||lt)return"";lt=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(Bl){var r=Bl}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(l){r=l}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var l=r.DetermineComponentFrameRoot(),i=l[0],o=l[1];if(i&&o){var s=i.split("\n"),u=o.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{lt=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return it(e.type,!1);case 11:return it(e.type.render,!1);case 1:return it(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(Bl){return"\nError generating stack: "+Bl.message+"\n"+Bl.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function mt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(zo){return e.body}}var pt=/[\n"\\]/g;function ht(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function vt(e,t,n,r,a,l,i,o){e.name="",null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.type=i:e.removeAttribute("type"),null!=t?"number"===i?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==i&&"reset"!==i||e.removeAttribute("value"),null!=t?bt(e,i,ut(t)):null!=n?bt(e,i,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=l&&(e.defaultChecked=!!l),null!=a&&(e.checked=a&&"function"!==typeof a&&"symbol"!==typeof a),null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.name=""+ut(o):e.removeAttribute("name")}function gt(e,t,n,r,a,l,i,o){if(null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.type=l),null!=t||null!=n){if(!("submit"!==l&&"reset"!==l||void 0!==t&&null!==t))return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,o||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:a)&&"symbol"!==typeof r&&!!r,e.checked=o?e.checked:!!r,e.defaultChecked=!!r,null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i&&(e.name=i)}function bt(e,t,n){"number"===t&&mt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function yt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function xt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(i(92));if(_(r)){if(1<r.length)throw Error(i(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function jt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Nt(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(i(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&jt(e,a,r)}else for(var l in t)t.hasOwnProperty(l)&&jt(e,l,t[l])}function Et(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ct=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tt(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ot=null;function Lt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Rt=null,_t=null;function At(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[_e]||null;e:switch(e=t.stateNode,t.type){case"input":if(vt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[_e]||null;if(!a)throw Error(i(90));vt(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":xt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&yt(e,!!n.multiple,t,!1)}}}var Ft=!1;function zt(e,t,n){if(Ft)return e(t,n);Ft=!0;try{return e(t)}finally{if(Ft=!1,(null!==Rt||null!==_t)&&(Wu(),Rt&&(t=Rt,e=_t,_t=Rt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[_e]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(i(231,t,typeof n));return n}var Mt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),It=!1;if(Mt)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){It=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(zo){It=!1}var Bt=null,Ht=null,Wt=null;function qt(){if(Wt)return Wt;var e,t,n=Ht,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[l-t];t++);return Wt=a.slice(e,1<t?1-t:void 0)}function Vt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Kt(){return!0}function $t(){return!1}function Qt(e){function t(t,n,r,a,l){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Kt:$t,this.isPropagationStopped=$t,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Kt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Kt)},persist:function(){},isPersistent:Kt}),t}var Yt,Gt,Xt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Qt(Jt),en=f({},Jt,{view:0,detail:0}),tn=Qt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Xt&&(Xt&&"mousemove"===e.type?(Yt=e.screenX-Xt.screenX,Gt=e.screenY-Xt.screenY):Gt=Yt=0,Xt=e),Yt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),rn=Qt(nn),an=Qt(f({},nn,{dataTransfer:0})),ln=Qt(f({},en,{relatedTarget:0})),on=Qt(f({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Qt(f({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Qt(f({},Jt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function pn(){return mn}var hn=Qt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Vt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Vt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Vt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),vn=Qt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),gn=Qt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),bn=Qt(f({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),yn=Qt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),xn=Qt(f({},Jt,{newState:0,oldState:0})),wn=[9,13,27,32],kn=Mt&&"CompositionEvent"in window,Sn=null;Mt&&"documentMode"in document&&(Sn=document.documentMode);var jn=Mt&&"TextEvent"in window&&!Sn,Nn=Mt&&(!kn||Sn&&8<Sn&&11>=Sn),En=String.fromCharCode(32),Cn=!1;function Pn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var On=!1;var Ln={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Ln[e.type]:"textarea"===t}function _n(e,t,n,r){Rt?_t?_t.push(r):_t=[r]:Rt=r,0<(t=Kc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var An=null,Fn=null;function zn(e){Mc(e,0)}function Dn(e){if(ft(We(e)))return e}function Mn(e,t){if("change"===e)return t}var In=!1;if(Mt){var Un;if(Mt){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Un=Bn}else Un=!1;In=Un&&(!document.documentMode||9<document.documentMode)}function Wn(){An&&(An.detachEvent("onpropertychange",qn),Fn=An=null)}function qn(e){if("value"===e.propertyName&&Dn(Fn)){var t=[];_n(t,Fn,e,Lt(e)),zt(zn,t)}}function Vn(e,t,n){"focusin"===e?(Wn(),Fn=n,(An=t).attachEvent("onpropertychange",qn)):"focusout"===e&&Wn()}function Kn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(Fn)}function $n(e,t){if("click"===e)return Dn(t)}function Qn(e,t){if("input"===e||"change"===e)return Dn(t)}var Yn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Gn(e,t){if(Yn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!G.call(t,a)||!Yn(e[a],t[a]))return!1}return!0}function Xn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,r=Xn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Xn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=mt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=mt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=Mt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,lr=null,ir=!1;function or(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;ir||null==rr||rr!==mt(r)||("selectionStart"in(r=rr)&&tr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},lr&&Gn(lr,r)||(lr=r,0<(r=Kc(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}Mt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var mr=fr("animationend"),pr=fr("animationiteration"),hr=fr("animationstart"),vr=fr("transitionrun"),gr=fr("transitionstart"),br=fr("transitioncancel"),yr=fr("transitionend"),xr=new Map,wr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function kr(e,t){xr.set(e,t),Qe(t,[e])}wr.push("scrollEnd");var Sr=new WeakMap;function jr(e,t){if("object"===typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Nr=[],Er=0,Cr=0;function Pr(){for(var e=Er,t=Cr=Er=0;t<e;){var n=Nr[t];Nr[t++]=null;var r=Nr[t];Nr[t++]=null;var a=Nr[t];Nr[t++]=null;var l=Nr[t];if(Nr[t++]=null,null!==r&&null!==a){var i=r.pending;null===i?a.next=a:(a.next=i.next,i.next=a),r.pending=a}0!==l&&Rr(n,a,l)}}function Tr(e,t,n,r){Nr[Er++]=e,Nr[Er++]=t,Nr[Er++]=n,Nr[Er++]=r,Cr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Or(e,t,n,r){return Tr(e,t,n,r),_r(e)}function Lr(e,t){return Tr(e,null,null,t),_r(e)}function Rr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,l=e.return;null!==l;)l.childLanes|=n,null!==(r=l.alternate)&&(r.childLanes|=n),22===l.tag&&(null===(e=l.stateNode)||1&e._visibility||(a=!0)),e=l,l=l.return;return 3===e.tag?(l=e.stateNode,a&&null!==t&&(a=31-me(n),null===(r=(e=l.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),l):null}function _r(e){if(50<Au)throw Au=0,Fu=null,Error(i(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={};function Fr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zr(e,t,n,r){return new Fr(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Mr(e,t){var n=e.alternate;return null===n?((n=zr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ir(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,r,a,l){var o=0;if(r=e,"function"===typeof e)Dr(e)&&(o=1);else if("string"===typeof e)o=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case C:return(e=zr(31,n,t,a)).elementType=C,e.lanes=l,e;case v:return Br(n.children,a,l,t);case g:o=8,a|=24;break;case b:return(e=zr(12,n,t,2|a)).elementType=b,e.lanes=l,e;case S:return(e=zr(13,n,t,a)).elementType=S,e.lanes=l,e;case j:return(e=zr(19,n,t,a)).elementType=j,e.lanes=l,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case y:case w:o=10;break e;case x:o=9;break e;case k:o=11;break e;case N:o=14;break e;case E:o=16,r=null;break e}o=29,n=Error(i(130,null===e?"null":typeof e,"")),r=null}return(t=zr(o,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Br(e,t,n,r){return(e=zr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=zr(6,e,null,t)).lanes=n,e}function Wr(e,t,n){return(t=zr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var qr=[],Vr=0,Kr=null,$r=0,Qr=[],Yr=0,Gr=null,Xr=1,Jr="";function Zr(e,t){qr[Vr++]=$r,qr[Vr++]=Kr,Kr=e,$r=t}function ea(e,t,n){Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Gr,Gr=e;var r=Xr;e=Jr;var a=32-me(r)-1;r&=~(1<<a),n+=1;var l=32-me(t)+a;if(30<l){var i=a-a%5;l=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Xr=1<<32-me(t)+a|n<<a|r,Jr=l+e}else Xr=1<<l|n<<a|r,Jr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Kr;)Kr=qr[--Vr],qr[Vr]=null,$r=qr[--Vr],qr[Vr]=null;for(;e===Gr;)Gr=Qr[--Yr],Qr[Yr]=null,Jr=Qr[--Yr],Qr[Yr]=null,Xr=Qr[--Yr],Qr[Yr]=null}var ra=null,aa=null,la=!1,ia=null,oa=!1,sa=Error(i(519));function ua(e){throw ha(jr(Error(i(418,"")),e)),sa}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Re]=e,t[_e]=r,n){case"dialog":Ic("cancel",t),Ic("close",t);break;case"iframe":case"object":case"embed":Ic("load",t);break;case"video":case"audio":for(n=0;n<zc.length;n++)Ic(zc[n],t);break;case"source":Ic("error",t);break;case"img":case"image":case"link":Ic("error",t),Ic("load",t);break;case"details":Ic("toggle",t);break;case"input":Ic("invalid",t),gt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Ic("invalid",t);break;case"textarea":Ic("invalid",t),wt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Jc(t.textContent,n)?(null!=r.popover&&(Ic("beforetoggle",t),Ic("toggle",t)),null!=r.onScroll&&Ic("scroll",t),null!=r.onScrollEnd&&Ic("scrollend",t),null!=r.onClick&&(t.onclick=Zc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(oa=!1);case 27:case 3:return void(oa=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!la)return da(e),la=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||sd(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(i(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,hd(e.type)?(e=xd,xd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function ma(){aa=ra=null,la=!1}function pa(){var e=ia;return null!==e&&(null===wu?wu=e:wu.push.apply(wu,e),ia=null),e}function ha(e){null===ia?ia=[e]:ia.push(e)}var va=I(null),ga=null,ba=null;function ya(e,t,n){B(va,t._currentValue),t._currentValue=n}function xa(e){e._currentValue=va.current,U(va)}function wa(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function ka(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var l=a.dependencies;if(null!==l){var o=a.child;l=l.firstContext;e:for(;null!==l;){var s=l;l=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),wa(l.return,n,e),r||(o=null);break e}l=s.next}}else if(18===a.tag){if(null===(o=a.return))throw Error(i(341));o.lanes|=n,null!==(l=o.alternate)&&(l.lanes|=n),wa(o,n,e),o=null}else o=a.child;if(null!==o)o.return=a;else for(o=a;null!==o;){if(o===e){o=null;break}if(null!==(a=o.sibling)){a.return=o.return,o=a;break}o=o.return}a=o}}function Sa(e,t,n,r){e=null;for(var a=t,l=!1;null!==a;){if(!l)if(0!==(524288&a.flags))l=!0;else if(0!==(262144&a.flags))break;if(10===a.tag){var o=a.alternate;if(null===o)throw Error(i(387));if(null!==(o=o.memoizedProps)){var s=a.type;Yn(a.pendingProps.value,o.value)||(null!==e?e.push(s):e=[s])}}else if(a===V.current){if(null===(o=a.alternate))throw Error(i(387));o.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Gd):e=[Gd])}a=a.return}null!==e&&ka(t,e,n,r),t.flags|=262144}function ja(e){for(e=e.firstContext;null!==e;){if(!Yn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Na(e){ga=e,ba=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ea(e){return Pa(ga,e)}function Ca(e,t){return null===ga&&Na(e),Pa(e,t)}function Pa(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===ba){if(null===e)throw Error(i(308));ba=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else ba=ba.next=t;return n}var Ta="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},Oa=r.unstable_scheduleCallback,La=r.unstable_NormalPriority,Ra={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function _a(){return{controller:new Ta,data:new Map,refCount:0}}function Aa(e){e.refCount--,0===e.refCount&&Oa(La,function(){e.controller.abort()})}var Fa=null,za=0,Da=0,Ma=null;function Ia(){if(0===--za&&null!==Fa){null!==Ma&&(Ma.status="fulfilled");var e=Fa;Fa=null,Da=0,Ma=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=A.S;A.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Fa){var n=Fa=[];za=0,Da=Lc(),Ma={status:"pending",value:void 0,then:function(e){n.push(e)}}}za++,t.then(Ia,Ia)}(0,t),null!==Ua&&Ua(e,t)};var Ba=I(null);function Ha(){var e=Ba.current;return null!==e?e:lu.pooledCache}function Wa(e,t){B(Ba,null===t?Ba.current:t.pool)}function qa(){var e=Ha();return null===e?null:{parent:Ra._currentValue,pool:e}}var Va=Error(i(460)),Ka=Error(i(474)),$a=Error(i(542)),Qa={then:function(){}};function Ya(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ga(){}function Xa(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ga,Ga),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e;default:if("string"===typeof t.status)t.then(Ga,Ga);else{if(null!==(e=lu)&&100<e.shellSuspendCounter)throw Error(i(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw el(e=t.reason),e}throw Ja=t,Va}}var Ja=null;function Za(){if(null===Ja)throw Error(i(459));var e=Ja;return Ja=null,e}function el(e){if(e===Va||e===$a)throw Error(i(483))}var tl=!1;function nl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function rl(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function al(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function ll(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&au)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=_r(e),Rr(e,null,n),t}return Tr(e,r,t,n),_r(e)}function il(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ce(e,n)}}function ol(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var i={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===l?a=l=i:l=l.next=i,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var sl=!1;function ul(){if(sl){if(null!==Ma)throw Ma}}function cl(e,t,n,r){sl=!1;var a=e.updateQueue;tl=!1;var l=a.firstBaseUpdate,i=a.lastBaseUpdate,o=a.shared.pending;if(null!==o){a.shared.pending=null;var s=o,u=s.next;s.next=null,null===i?l=u:i.next=u,i=s;var c=e.alternate;null!==c&&((o=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===o?c.firstBaseUpdate=u:o.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(i=0,c=u=s=null,o=l;;){var m=-536870913&o.lane,p=m!==o.lane;if(p?(ou&m)===m:(r&m)===m){0!==m&&m===Da&&(sl=!0),null!==c&&(c=c.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var h=e,v=o;m=t;var g=n;switch(v.tag){case 1:if("function"===typeof(h=v.payload)){d=h.call(g,d,m);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(m="function"===typeof(h=v.payload)?h.call(g,d,m):h)||void 0===m)break e;d=f({},d,m);break e;case 2:tl=!0}}null!==(m=o.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=a.callbacks)?a.callbacks=[m]:p.push(m))}else p={lane:m,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,i|=m;if(null===(o=o.next)){if(null===(o=a.shared.pending))break;o=(p=o).next,p.next=null,a.lastBaseUpdate=p,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===l&&(a.shared.lanes=0),hu|=i,e.lanes=i,e.memoizedState=d}}function dl(e,t){if("function"!==typeof e)throw Error(i(191,e));e.call(t)}function fl(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)dl(n[e],t)}var ml=I(null),pl=I(0);function hl(e,t){B(pl,e=mu),B(ml,t),mu=e|t.baseLanes}function vl(){B(pl,mu),B(ml,ml.current)}function gl(){mu=pl.current,U(ml),U(pl)}var bl=0,yl=null,xl=null,wl=null,kl=!1,Sl=!1,jl=!1,Nl=0,El=0,Cl=null,Pl=0;function Tl(){throw Error(i(321))}function Ol(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Yn(e[n],t[n]))return!1;return!0}function Ll(e,t,n,r,a,l){return bl=l,yl=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?Ki:$i,jl=!1,l=n(r,a),jl=!1,Sl&&(l=_l(t,n,r,a)),Rl(e),l}function Rl(e){A.H=Vi;var t=null!==xl&&null!==xl.next;if(bl=0,wl=xl=yl=null,kl=!1,El=0,Cl=null,t)throw Error(i(300));null===e||Po||null!==(e=e.dependencies)&&ja(e)&&(Po=!0)}function _l(e,t,n,r){yl=e;var a=0;do{if(Sl&&(Cl=null),El=0,Sl=!1,25<=a)throw Error(i(301));if(a+=1,wl=xl=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}A.H=Qi,l=t(n,r)}while(Sl);return l}function Al(){var e=A.H,t=e.useState()[0];return t="function"===typeof t.then?Ul(t):t,e=e.useState()[0],(null!==xl?xl.memoizedState:null)!==e&&(yl.flags|=1024),t}function Fl(){var e=0!==Nl;return Nl=0,e}function zl(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Dl(e){if(kl){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}kl=!1}bl=0,wl=xl=yl=null,Sl=!1,El=Nl=0,Cl=null}function Ml(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===wl?yl.memoizedState=wl=e:wl=wl.next=e,wl}function Il(){if(null===xl){var e=yl.alternate;e=null!==e?e.memoizedState:null}else e=xl.next;var t=null===wl?yl.memoizedState:wl.next;if(null!==t)wl=t,xl=e;else{if(null===e){if(null===yl.alternate)throw Error(i(467));throw Error(i(310))}e={memoizedState:(xl=e).memoizedState,baseState:xl.baseState,baseQueue:xl.baseQueue,queue:xl.queue,next:null},null===wl?yl.memoizedState=wl=e:wl=wl.next=e}return wl}function Ul(e){var t=El;return El+=1,null===Cl&&(Cl=[]),e=Xa(Cl,e,t),t=yl,null===(null===wl?t.memoizedState:wl.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?Ki:$i),e}function Hl(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Ul(e);if(e.$$typeof===w)return Ea(e)}throw Error(i(438,String(e)))}function Wl(e){var t=null,n=yl.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=yl.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},yl.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=P;return t.index++,n}function ql(e,t){return"function"===typeof t?t(e):t}function Vl(e){return Kl(Il(),xl,e)}function Kl(e,t,n){var r=e.queue;if(null===r)throw Error(i(311));r.lastRenderedReducer=n;var a=e.baseQueue,l=r.pending;if(null!==l){if(null!==a){var o=a.next;a.next=l.next,l.next=o}t.baseQueue=a=l,r.pending=null}if(l=e.baseState,null===a)e.memoizedState=l;else{var s=o=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(ou&f)===f:(bl&f)===f){var m=c.revertLane;if(0===m)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Da&&(d=!0);else{if((bl&m)===m){c=c.next,m===Da&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,o=l):u=u.next=f,yl.lanes|=m,hu|=m}f=c.action,jl&&n(l,f),l=c.hasEagerState?c.eagerState:n(l,f)}else m={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=m,o=l):u=u.next=m,yl.lanes|=f,hu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?o=l:u.next=s,!Yn(l,e.memoizedState)&&(Po=!0,d&&null!==(n=Ma)))throw n;e.memoizedState=l,e.baseState=o,e.baseQueue=u,r.lastRenderedState=l}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function $l(e){var t=Il(),n=t.queue;if(null===n)throw Error(i(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var o=a=a.next;do{l=e(l,o.action),o=o.next}while(o!==a);Yn(l,t.memoizedState)||(Po=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function Ql(e,t,n){var r=yl,a=Il(),l=la;if(l){if(void 0===n)throw Error(i(407));n=n()}else n=t();var o=!Yn((xl||a).memoizedState,n);if(o&&(a.memoizedState=n,Po=!0),a=a.queue,gi(2048,8,Xl.bind(null,r,a,e),[e]),a.getSnapshot!==t||o||null!==wl&&1&wl.memoizedState.tag){if(r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,a,n,t),null),null===lu)throw Error(i(349));l||0!==(124&bl)||Yl(r,t,n)}return n}function Yl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=yl.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},yl.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Gl(e,t,n,r){t.value=n,t.getSnapshot=r,Jl(t)&&Zl(e)}function Xl(e,t,n){return n(function(){Jl(t)&&Zl(e)})}function Jl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Yn(e,n)}catch(r){return!0}}function Zl(e){var t=Lr(e,2);null!==t&&Mu(t,e,2)}function ei(e){var t=Ml();if("function"===typeof e){var n=e;if(e=n(),jl){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:e},t}function ti(e,t,n,r){return e.baseState=n,Kl(e,xl,"function"===typeof r?r:ql)}function ni(e,t,n,r,a){if(Hi(e))throw Error(i(485));if(null!==(e=t.action)){var l={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==A.T?n(!0):l.isTransition=!1,r(l),null===(n=t.pending)?(l.next=t.pending=l,ri(t,l)):(l.next=n.next,t.pending=n.next=l)}}function ri(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var l=A.T,i={};A.T=i;try{var o=n(a,r),s=A.S;null!==s&&s(i,o),ai(e,t,o)}catch(u){ii(e,t,u)}finally{A.T=l}}else try{ai(e,t,l=n(a,r))}catch(c){ii(e,t,c)}}function ai(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){li(e,t,n)},function(n){return ii(e,t,n)}):li(e,t,n)}function li(e,t,n){t.status="fulfilled",t.value=n,oi(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ri(e,n)))}function ii(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,oi(t),t=t.next}while(t!==r)}e.action=null}function oi(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function si(e,t){return t}function ui(e,t){if(la){var n=lu.formState;if(null!==n){e:{var r=yl;if(la){if(aa){t:{for(var a=aa,l=oa;8!==a.nodeType;){if(!l){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(l=a.data)||"F"===l?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Ml()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:si,lastRenderedState:t},n.queue=r,n=Ii.bind(null,yl,r),r.dispatch=n,r=ei(!1),l=Bi.bind(null,yl,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Ml()).queue=a,n=ni.bind(null,yl,a,l,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function ci(e){return di(Il(),xl,e)}function di(e,t,n){if(t=Kl(e,t,si)[0],e=Vl(ql)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var r=Ul(t)}catch(Bl){if(Bl===Va)throw $a;throw Bl}else r=t;var a=(t=Il()).queue,l=a.dispatch;return n!==t.memoizedState&&(yl.flags|=2048,pi(9,{destroy:void 0,resource:void 0},fi.bind(null,a,n),null)),[r,l,e]}function fi(e,t){e.action=t}function mi(e){var t=Il(),n=xl;if(null!==n)return di(t,n,e);Il(),t=t.memoizedState;var r=(n=Il()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function pi(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=yl.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},yl.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function hi(){return Il().memoizedState}function vi(e,t,n,r){var a=Ml();r=void 0===r?null:r,yl.flags|=e,a.memoizedState=pi(1|t,{destroy:void 0,resource:void 0},n,r)}function gi(e,t,n,r){var a=Il();r=void 0===r?null:r;var l=a.memoizedState.inst;null!==xl&&null!==r&&Ol(r,xl.memoizedState.deps)?a.memoizedState=pi(t,l,n,r):(yl.flags|=e,a.memoizedState=pi(1|t,l,n,r))}function bi(e,t){vi(8390656,8,e,t)}function yi(e,t){gi(2048,8,e,t)}function xi(e,t){return gi(4,2,e,t)}function wi(e,t){return gi(4,4,e,t)}function ki(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function Si(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gi(4,4,ki.bind(null,t,e),n)}function ji(){}function Ni(e,t){var n=Il();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&Ol(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ei(e,t){var n=Il();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&Ol(t,r[1]))return r[0];if(r=e(),jl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Ci(e,t,n){return void 0===n||0!==(1073741824&bl)?e.memoizedState=t:(e.memoizedState=n,e=Du(),yl.lanes|=e,hu|=e,n)}function Pi(e,t,n,r){return Yn(n,t)?n:null!==ml.current?(e=Ci(e,n,r),Yn(e,t)||(Po=!0),e):0===(42&bl)?(Po=!0,e.memoizedState=n):(e=Du(),yl.lanes|=e,hu|=e,t)}function Ti(e,t,n,r,a){var l=F.p;F.p=0!==l&&8>l?l:8;var i=A.T,o={};A.T=o,Bi(e,!1,t,n);try{var s=a(),u=A.S;if(null!==u&&u(o,s),null!==s&&"object"===typeof s&&"function"===typeof s.then)Ui(e,t,function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),r}(s,r),zu());else Ui(e,t,r,zu())}catch(c){Ui(e,t,{then:function(){},status:"rejected",reason:c},zu())}finally{F.p=l,A.T=i}}function Oi(){}function Li(e,t,n,r){if(5!==e.tag)throw Error(i(476));var a=Ri(e).queue;Ti(e,a,t,z,null===n?Oi:function(){return _i(e),n(r)})}function Ri(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:z,baseState:z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:z},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:ql,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function _i(e){Ui(e,Ri(e).next.queue,{},zu())}function Ai(){return Ea(Gd)}function Fi(){return Il().memoizedState}function zi(){return Il().memoizedState}function Di(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=zu(),r=ll(t,e=al(n),n);return null!==r&&(Mu(r,t,n),il(r,t,n)),t={cache:_a()},void(e.payload=t)}t=t.return}}function Mi(e,t,n){var r=zu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hi(e)?Wi(t,n):null!==(n=Or(e,t,n,r))&&(Mu(n,e,r),qi(n,t,r))}function Ii(e,t,n){Ui(e,t,n,zu())}function Ui(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hi(e))Wi(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var i=t.lastRenderedState,o=l(i,n);if(a.hasEagerState=!0,a.eagerState=o,Yn(o,i))return Tr(e,t,a,0),null===lu&&Pr(),!1}catch(s){}if(null!==(n=Or(e,t,a,r)))return Mu(n,e,r),qi(n,t,r),!0}return!1}function Bi(e,t,n,r){if(r={lane:2,revertLane:Lc(),action:r,hasEagerState:!1,eagerState:null,next:null},Hi(e)){if(t)throw Error(i(479))}else null!==(t=Or(e,n,r,2))&&Mu(t,e,2)}function Hi(e){var t=e.alternate;return e===yl||null!==t&&t===yl}function Wi(e,t){Sl=kl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function qi(e,t,n){if(0!==(4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ce(e,n)}}var Vi={readContext:Ea,use:Hl,useCallback:Tl,useContext:Tl,useEffect:Tl,useImperativeHandle:Tl,useLayoutEffect:Tl,useInsertionEffect:Tl,useMemo:Tl,useReducer:Tl,useRef:Tl,useState:Tl,useDebugValue:Tl,useDeferredValue:Tl,useTransition:Tl,useSyncExternalStore:Tl,useId:Tl,useHostTransitionStatus:Tl,useFormState:Tl,useActionState:Tl,useOptimistic:Tl,useMemoCache:Tl,useCacheRefresh:Tl},Ki={readContext:Ea,use:Hl,useCallback:function(e,t){return Ml().memoizedState=[e,void 0===t?null:t],e},useContext:Ea,useEffect:bi,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,vi(4194308,4,ki.bind(null,t,e),n)},useLayoutEffect:function(e,t){return vi(4194308,4,e,t)},useInsertionEffect:function(e,t){vi(4,2,e,t)},useMemo:function(e,t){var n=Ml();t=void 0===t?null:t;var r=e();if(jl){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Ml();if(void 0!==n){var a=n(t);if(jl){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Mi.bind(null,yl,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Ml().memoizedState=e},useState:function(e){var t=(e=ei(e)).queue,n=Ii.bind(null,yl,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ji,useDeferredValue:function(e,t){return Ci(Ml(),e,t)},useTransition:function(){var e=ei(!1);return e=Ti.bind(null,yl,e.queue,!0,!1),Ml().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=yl,a=Ml();if(la){if(void 0===n)throw Error(i(407));n=n()}else{if(n=t(),null===lu)throw Error(i(349));0!==(124&ou)||Yl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,bi(Xl.bind(null,r,l,e),[e]),r.flags|=2048,pi(9,{destroy:void 0,resource:void 0},Gl.bind(null,r,l,n,t),null),n},useId:function(){var e=Ml(),t=lu.identifierPrefix;if(la){var n=Jr;t="\xab"+t+"R"+(n=(Xr&~(1<<32-me(Xr)-1)).toString(32)+n),0<(n=Nl++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Pl++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:Ai,useFormState:ui,useActionState:ui,useOptimistic:function(e){var t=Ml();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bi.bind(null,yl,!0,n),n.dispatch=t,[e,t]},useMemoCache:Wl,useCacheRefresh:function(){return Ml().memoizedState=Di.bind(null,yl)}},$i={readContext:Ea,use:Hl,useCallback:Ni,useContext:Ea,useEffect:yi,useImperativeHandle:Si,useInsertionEffect:xi,useLayoutEffect:wi,useMemo:Ei,useReducer:Vl,useRef:hi,useState:function(){return Vl(ql)},useDebugValue:ji,useDeferredValue:function(e,t){return Pi(Il(),xl.memoizedState,e,t)},useTransition:function(){var e=Vl(ql)[0],t=Il().memoizedState;return["boolean"===typeof e?e:Ul(e),t]},useSyncExternalStore:Ql,useId:Fi,useHostTransitionStatus:Ai,useFormState:ci,useActionState:ci,useOptimistic:function(e,t){return ti(Il(),0,e,t)},useMemoCache:Wl,useCacheRefresh:zi},Qi={readContext:Ea,use:Hl,useCallback:Ni,useContext:Ea,useEffect:yi,useImperativeHandle:Si,useInsertionEffect:xi,useLayoutEffect:wi,useMemo:Ei,useReducer:$l,useRef:hi,useState:function(){return $l(ql)},useDebugValue:ji,useDeferredValue:function(e,t){var n=Il();return null===xl?Ci(n,e,t):Pi(n,xl.memoizedState,e,t)},useTransition:function(){var e=$l(ql)[0],t=Il().memoizedState;return["boolean"===typeof e?e:Ul(e),t]},useSyncExternalStore:Ql,useId:Fi,useHostTransitionStatus:Ai,useFormState:mi,useActionState:mi,useOptimistic:function(e,t){var n=Il();return null!==xl?ti(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Wl,useCacheRefresh:zi},Yi=null,Gi=0;function Xi(e){var t=Gi;return Gi+=1,null===Yi&&(Yi=[]),Xa(Yi,e,t)}function Ji(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zi(e,t){if(t.$$typeof===m)throw Error(i(525));throw e=Object.prototype.toString.call(t),Error(i(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function eo(e){return(0,e._init)(e._payload)}function to(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=Mr(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===v?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===E&&eo(l)===t.type)?(Ji(t=a(t,n.props),n),t.return=e,t):(Ji(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Wr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Br(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Ji(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case h:return(t=Wr(t,e.mode,n)).return=e,t;case E:return f(e,t=(0,t._init)(t._payload),n)}if(_(t)||O(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return f(e,Xi(t),n);if(t.$$typeof===w)return f(e,Ca(e,t),n);Zi(e,t)}return null}function m(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===a?u(e,t,n,r):null;case h:return n.key===a?c(e,t,n,r):null;case E:return m(e,t,n=(a=n._init)(n._payload),r)}if(_(n)||O(n))return null!==a?null:d(e,t,n,r,null);if("function"===typeof n.then)return m(e,t,Xi(n),r);if(n.$$typeof===w)return m(e,t,Ca(e,n),r);Zi(e,n)}return null}function g(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case p:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case E:return g(e,t,n,r=(0,r._init)(r._payload),a)}if(_(r)||O(r))return d(t,e=e.get(n)||null,r,a,null);if("function"===typeof r.then)return g(e,t,n,Xi(r),a);if(r.$$typeof===w)return g(e,t,n,Ca(t,r),a);Zi(t,r)}return null}function b(s,u,c,d){if("object"===typeof c&&null!==c&&c.type===v&&null===c.key&&(c=c.props.children),"object"===typeof c&&null!==c){switch(c.$$typeof){case p:e:{for(var y=c.key;null!==u;){if(u.key===y){if((y=c.type)===v){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===y||"object"===typeof y&&null!==y&&y.$$typeof===E&&eo(y)===u.type){n(s,u.sibling),Ji(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===v?((d=Br(c.props.children,s.mode,d,c.key)).return=s,s=d):(Ji(d=Ur(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return o(s);case h:e:{for(y=c.key;null!==u;){if(u.key===y){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Wr(c,s.mode,d)).return=s,s=d}return o(s);case E:return b(s,u,c=(y=c._init)(c._payload),d)}if(_(c))return function(a,i,o,s){for(var u=null,c=null,d=i,p=i=0,h=null;null!==d&&p<o.length;p++){d.index>p?(h=d,d=null):h=d.sibling;var v=m(a,d,o[p],s);if(null===v){null===d&&(d=h);break}e&&d&&null===v.alternate&&t(a,d),i=l(v,i,p),null===c?u=v:c.sibling=v,c=v,d=h}if(p===o.length)return n(a,d),la&&Zr(a,p),u;if(null===d){for(;p<o.length;p++)null!==(d=f(a,o[p],s))&&(i=l(d,i,p),null===c?u=d:c.sibling=d,c=d);return la&&Zr(a,p),u}for(d=r(d);p<o.length;p++)null!==(h=g(d,a,p,o[p],s))&&(e&&null!==h.alternate&&d.delete(null===h.key?p:h.key),i=l(h,i,p),null===c?u=h:c.sibling=h,c=h);return e&&d.forEach(function(e){return t(a,e)}),la&&Zr(a,p),u}(s,u,c,d);if(O(c)){if("function"!==typeof(y=O(c)))throw Error(i(150));return function(a,o,s,u){if(null==s)throw Error(i(151));for(var c=null,d=null,p=o,h=o=0,v=null,b=s.next();null!==p&&!b.done;h++,b=s.next()){p.index>h?(v=p,p=null):v=p.sibling;var y=m(a,p,b.value,u);if(null===y){null===p&&(p=v);break}e&&p&&null===y.alternate&&t(a,p),o=l(y,o,h),null===d?c=y:d.sibling=y,d=y,p=v}if(b.done)return n(a,p),la&&Zr(a,h),c;if(null===p){for(;!b.done;h++,b=s.next())null!==(b=f(a,b.value,u))&&(o=l(b,o,h),null===d?c=b:d.sibling=b,d=b);return la&&Zr(a,h),c}for(p=r(p);!b.done;h++,b=s.next())null!==(b=g(p,a,h,b.value,u))&&(e&&null!==b.alternate&&p.delete(null===b.key?h:b.key),o=l(b,o,h),null===d?c=b:d.sibling=b,d=b);return e&&p.forEach(function(e){return t(a,e)}),la&&Zr(a,h),c}(s,u,c=y.call(c),d)}if("function"===typeof c.then)return b(s,u,Xi(c),d);if(c.$$typeof===w)return b(s,u,Ca(s,c),d);Zi(s,c)}return"string"===typeof c&&""!==c||"number"===typeof c||"bigint"===typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=Hr(c,s.mode,d)).return=s,s=d),o(s)):n(s,u)}return function(e,t,n,r){try{Gi=0;var a=b(e,t,n,r);return Yi=null,a}catch(Bl){if(Bl===Va||Bl===$a)throw Bl;var l=zr(29,Bl,null,e.mode);return l.lanes=r,l.return=e,l}}}var no=to(!0),ro=to(!1),ao=I(null),lo=null;function io(e){var t=e.alternate;B(co,1&co.current),B(ao,e),null===lo&&(null===t||null!==ml.current||null!==t.memoizedState)&&(lo=e)}function oo(e){if(22===e.tag){if(B(co,co.current),B(ao,e),null===lo){var t=e.alternate;null!==t&&null!==t.memoizedState&&(lo=e)}}else so()}function so(){B(co,co.current),B(ao,ao.current)}function uo(e){U(ao),lo===e&&(lo=null),U(co)}var co=I(0);function fo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||bd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function mo(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var po={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=zu(),a=al(r);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Mu(t,e,r),il(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=zu(),a=al(r);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=ll(e,a,r))&&(Mu(t,e,r),il(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=zu(),r=al(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=ll(e,r,n))&&(Mu(t,e,n),il(t,e,n))}};function ho(e,t,n,r,a,l,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,i):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(a,l))}function vo(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&po.enqueueReplaceState(t,t.state,null)}function go(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var bo="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function yo(e){bo(e)}function xo(e){console.error(e)}function wo(e){bo(e)}function ko(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function So(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function jo(e,t,n){return(n=al(n)).tag=3,n.payload={element:null},n.callback=function(){ko(e,t)},n}function No(e){return(e=al(e)).tag=3,e}function Eo(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"===typeof a){var l=r.value;e.payload=function(){return a(l)},e.callback=function(){So(t,n,r)}}var i=n.stateNode;null!==i&&"function"===typeof i.componentDidCatch&&(e.callback=function(){So(t,n,r),"function"!==typeof a&&(null===Eu?Eu=new Set([this]):Eu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Co=Error(i(461)),Po=!1;function To(e,t,n,r){t.child=null===e?ro(t,null,n,r):no(t,e.child,n,r)}function Oo(e,t,n,r,a){n=n.render;var l=t.ref;if("ref"in r){var i={};for(var o in r)"ref"!==o&&(i[o]=r[o])}else i=r;return Na(t),r=Ll(e,t,n,i,l,a),o=Fl(),null===e||Po?(la&&o&&ta(t),t.flags|=1,To(e,t,r,a),t.child):(zl(e,t,a),Xo(e,t,a))}function Lo(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Dr(l)||void 0!==l.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Ro(e,t,l,r,a))}if(l=e.child,!Jo(e,a)){var i=l.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(i,r)&&e.ref===t.ref)return Xo(e,t,a)}return t.flags|=1,(e=Mr(l,r)).ref=t.ref,e.return=t,t.child=e}function Ro(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(Gn(l,r)&&e.ref===t.ref){if(Po=!1,t.pendingProps=r=l,!Jo(e,a))return t.lanes=e.lanes,Xo(e,t,a);0!==(131072&e.flags)&&(Po=!0)}}return Do(e,t,n,r,a)}function _o(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(a=t.child=e.child,l=0;null!==a;)l=l|a.lanes|a.childLanes,a=a.sibling;t.childLanes=l&~r}else t.childLanes=0,t.child=null;return Ao(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ao(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Wa(0,null!==l?l.cachePool:null),null!==l?hl(t,l):vl(),oo(t)}else null!==l?(Wa(0,l.cachePool),hl(t,l),so(),t.memoizedState=null):(null!==e&&Wa(0,null),vl(),so());return To(e,t,a,n),t.child}function Ao(e,t,n,r){var a=Ha();return a=null===a?null:{parent:Ra._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Wa(0,null),vl(),oo(t),null!==e&&Sa(e,t,r,!0),null}function Fo(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(i(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Do(e,t,n,r,a){return Na(t),n=Ll(e,t,n,r,void 0,a),r=Fl(),null===e||Po?(la&&r&&ta(t),t.flags|=1,To(e,t,n,a),t.child):(zl(e,t,a),Xo(e,t,a))}function Mo(e,t,n,r,a,l){return Na(t),t.updateQueue=null,n=_l(t,r,n,a),Rl(e),r=Fl(),null===e||Po?(la&&r&&ta(t),t.flags|=1,To(e,t,n,l),t.child):(zl(e,t,l),Xo(e,t,l))}function Io(e,t,n,r,a){if(Na(t),null===t.stateNode){var l=Ar,i=n.contextType;"object"===typeof i&&null!==i&&(l=Ea(i)),l=new n(r,l),t.memoizedState=null!==l.state&&void 0!==l.state?l.state:null,l.updater=po,t.stateNode=l,l._reactInternals=t,(l=t.stateNode).props=r,l.state=t.memoizedState,l.refs={},nl(t),i=n.contextType,l.context="object"===typeof i&&null!==i?Ea(i):Ar,l.state=t.memoizedState,"function"===typeof(i=n.getDerivedStateFromProps)&&(mo(t,n,i,r),l.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof l.getSnapshotBeforeUpdate||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||(i=l.state,"function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount(),i!==l.state&&po.enqueueReplaceState(l,l.state,null),cl(t,r,l,a),ul(),l.state=t.memoizedState),"function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){l=t.stateNode;var o=t.memoizedProps,s=go(n,o);l.props=s;var u=l.context,c=n.contextType;i=Ar,"object"===typeof c&&null!==c&&(i=Ea(c));var d=n.getDerivedStateFromProps;c="function"===typeof d||"function"===typeof l.getSnapshotBeforeUpdate,o=t.pendingProps!==o,c||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(o||u!==i)&&vo(t,l,r,i),tl=!1;var f=t.memoizedState;l.state=f,cl(t,r,l,a),ul(),u=t.memoizedState,o||f!==u||tl?("function"===typeof d&&(mo(t,n,d,r),u=t.memoizedState),(s=tl||ho(t,n,s,r,f,u,i))?(c||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=i,r=s):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,rl(e,t),c=go(n,i=t.memoizedProps),l.props=c,d=t.pendingProps,f=l.context,u=n.contextType,s=Ar,"object"===typeof u&&null!==u&&(s=Ea(u)),(u="function"===typeof(o=n.getDerivedStateFromProps)||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==s)&&vo(t,l,r,s),tl=!1,f=t.memoizedState,l.state=f,cl(t,r,l,a),ul();var m=t.memoizedState;i!==d||f!==m||tl||null!==e&&null!==e.dependencies&&ja(e.dependencies)?("function"===typeof o&&(mo(t,n,o,r),m=t.memoizedState),(c=tl||ho(t,n,c,r,f,m,s)||null!==e&&null!==e.dependencies&&ja(e.dependencies))?(u||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,m,s),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,m,s)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=m),l.props=r,l.state=m,l.context=s,r=c):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return l=r,Fo(e,t),r=0!==(128&t.flags),l||r?(l=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:l.render(),t.flags|=1,null!==e&&r?(t.child=no(t,e.child,null,a),t.child=no(t,null,n,a)):To(e,t,n,a),t.memoizedState=l.state,e=t.child):e=Xo(e,t,a),e}function Uo(e,t,n,r){return ma(),t.flags|=256,To(e,t,n,r),t.child}var Bo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ho(e){return{baseLanes:e,cachePool:qa()}}function Wo(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=bu),e}function qo(e,t,n){var r,a=t.pendingProps,l=!1,o=0!==(128&t.flags);if((r=o)||(r=(null===e||null!==e.memoizedState)&&0!==(2&co.current)),r&&(l=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(la){if(l?io(t):so(),la){var s,u=aa;if(s=u){e:{for(s=u,u=oa;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Gr?{id:Xr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(s=zr(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return bd(u)?t.lanes=32:t.lanes=536870912,null;uo(t)}return u=a.children,a=a.fallback,l?(so(),u=Ko({mode:"hidden",children:u},l=t.mode),a=Br(a,l,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(l=t.child).memoizedState=Ho(n),l.childLanes=Wo(e,r,n),t.memoizedState=Bo,a):(io(t),Vo(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(o)256&t.flags?(io(t),t.flags&=-257,t=$o(e,t,n)):null!==t.memoizedState?(so(),t.child=e.child,t.flags|=128,t=null):(so(),l=a.fallback,u=t.mode,a=Ko({mode:"visible",children:a.children},u),(l=Br(l,u,n,null)).flags|=2,a.return=t,l.return=t,a.sibling=l,t.child=a,no(t,e.child,null,n),(a=t.child).memoizedState=Ho(n),a.childLanes=Wo(e,r,n),t.memoizedState=Bo,t=l);else if(io(t),bd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(i(419))).stack="",a.digest=r,ha({value:a,source:null,stack:null}),t=$o(e,t,n)}else if(Po||Sa(e,t,n,!1),r=0!==(n&e.childLanes),Po||r){if(null!==(r=lu)&&(0!==(a=0!==((a=0!==(42&(a=n&-n))?1:Pe(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane))throw s.retryLane=a,Lr(e,a),Mu(r,e,a),Co;"$?"===u.data||Yu(),t=$o(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=yd(u.nextSibling),ra=t,la=!0,ia=null,oa=!1,null!==e&&(Qr[Yr++]=Xr,Qr[Yr++]=Jr,Qr[Yr++]=Gr,Xr=e.id,Jr=e.overflow,Gr=t),(t=Vo(t,a.children)).flags|=4096);return t}return l?(so(),l=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=Mr(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?l=Mr(c,l):(l=Br(l,u,n,null)).flags|=2,l.return=t,a.return=t,a.sibling=l,t.child=a,a=l,l=t.child,null===(u=e.child.memoizedState)?u=Ho(n):(null!==(s=u.cachePool)?(c=Ra._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=qa(),u={baseLanes:u.baseLanes|n,cachePool:s}),l.memoizedState=u,l.childLanes=Wo(e,r,n),t.memoizedState=Bo,a):(io(t),e=(n=e.child).sibling,(n=Mr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Vo(e,t){return(t=Ko({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Ko(e,t){return(e=zr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $o(e,t,n){return no(t,e.child,null,n),(e=Vo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Qo(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wa(e.return,t,n)}function Yo(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Go(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(To(e,t,r.children,n),0!==(2&(r=co.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Qo(e,n,t);else if(19===e.tag)Qo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(co,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===fo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Yo(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===fo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Yo(t,!0,n,null,l);break;case"together":Yo(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),hu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(i(153));if(null!==t.child){for(n=Mr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Mr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Jo(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!ja(e))}function Zo(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Po=!0;else{if(!Jo(e,n)&&0===(128&t.flags))return Po=!1,function(e,t,n){switch(t.tag){case 3:K(t,t.stateNode.containerInfo),ya(0,Ra,e.memoizedState.cache),ma();break;case 27:case 5:Q(t);break;case 4:K(t,t.stateNode.containerInfo);break;case 10:ya(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(io(t),t.flags|=128,null):0!==(n&t.child.childLanes)?qo(e,t,n):(io(t),null!==(e=Xo(e,t,n))?e.sibling:null);io(t);break;case 19:var a=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Go(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(co,co.current),r)break;return null;case 22:case 23:return t.lanes=0,_o(e,t,n);case 24:ya(0,Ra,e.memoizedState.cache)}return Xo(e,t,n)}(e,t,n);Po=0!==(131072&e.flags)}else Po=!1,la&&0!==(1048576&t.flags)&&ea(t,$r,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((a=r.$$typeof)===k){t.tag=11,t=Oo(null,t,r,e,n);break e}if(a===N){t.tag=14,t=Lo(null,t,r,e,n);break e}}throw t=R(r)||r,Error(i(306,t,""))}Dr(r)?(e=go(r,e),t.tag=1,t=Io(null,t,r,e,n)):(t.tag=0,t=Do(null,t,r,e,n))}return t;case 0:return Do(e,t,t.type,t.pendingProps,n);case 1:return Io(e,t,r=t.type,a=go(r,t.pendingProps),n);case 3:e:{if(K(t,t.stateNode.containerInfo),null===e)throw Error(i(387));r=t.pendingProps;var l=t.memoizedState;a=l.element,rl(e,t),cl(t,r,null,n);var o=t.memoizedState;if(r=o.cache,ya(0,Ra,r),r!==l.cache&&ka(t,[Ra],n,!0),ul(),r=o.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Uo(e,t,r,n);break e}if(r!==a){ha(a=jr(Error(i(424)),t)),t=Uo(e,t,r,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(aa=yd(e.firstChild),ra=t,la=!0,ia=null,oa=!0,n=ro(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ma(),r===a){t=Xo(e,t,n);break e}To(e,t,r,n)}t=t.child}return t;case 26:return Fo(e,t),null===e?(n=Od(t.type,null,t.pendingProps,null))?t.memoizedState=n:la||(n=t.type,e=t.pendingProps,(r=ld(q.current).createElement(n))[Re]=t,r[_e]=e,nd(r,n,e),Ve(r),t.stateNode=r):t.memoizedState=Od(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Q(t),null===e&&la&&(r=t.stateNode=kd(t.type,t.pendingProps,q.current),ra=t,oa=!0,a=aa,hd(t.type)?(xd=a,aa=yd(r.firstChild)):aa=a),To(e,t,t.pendingProps.children,n),Fo(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&la&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ie])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(l=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(l!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((l=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&l&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var l=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===l)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,oa))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),oa=!1,a=!0):a=!1),a||ua(t)),Q(t),a=t.type,l=t.pendingProps,o=null!==e?e.memoizedProps:null,r=l.children,sd(a,l)?r=null:null!==o&&sd(a,o)&&(t.flags|=32),null!==t.memoizedState&&(a=Ll(e,t,Al,null,null,n),Gd._currentValue=a),Fo(e,t),To(e,t,r,n),t.child;case 6:return null===e&&la&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,oa))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return qo(e,t,n);case 4:return K(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=no(t,null,r,n):To(e,t,r,n),t.child;case 11:return Oo(e,t,t.type,t.pendingProps,n);case 7:return To(e,t,t.pendingProps,n),t.child;case 8:case 12:return To(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ya(0,t.type,r.value),To(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Na(t),r=r(a=Ea(a)),t.flags|=1,To(e,t,r,n),t.child;case 14:return Lo(e,t,t.type,t.pendingProps,n);case 15:return Ro(e,t,t.type,t.pendingProps,n);case 19:return Go(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Ko(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Mr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return _o(e,t,n);case 24:return Na(t),r=Ea(Ra),null===e?(null===(a=Ha())&&(a=lu,l=_a(),a.pooledCache=l,l.refCount++,null!==l&&(a.pooledCacheLanes|=n),a=l),t.memoizedState={parent:r,cache:a},nl(t),ya(0,Ra,a)):(0!==(e.lanes&n)&&(rl(e,t),cl(t,null,null,n),ul()),a=e.memoizedState,l=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ya(0,Ra,r)):(r=l.cache,ya(0,Ra,r),r!==a.cache&&ka(t,[Ra],n,!0))),To(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(i(156,t.tag))}function es(e){e.flags|=4}function ts(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Wd(t)){if(null!==(t=ao.current)&&((4194048&ou)===ou?null!==lo:(62914560&ou)!==ou&&0===(536870912&ou)||t!==lo))throw Ja=Qa,Ka;e.flags|=8192}}function ns(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,yu|=t)}function rs(e,t){if(!la)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function as(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ls(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return as(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),xa(Ra),$(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?es(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pa())),as(t),null;case 26:return n=t.memoizedState,null===e?(es(t),null!==n?(as(t),ts(t,n)):(as(t),t.flags&=-16777217)):n?n!==e.memoizedState?(es(t),as(t),ts(t,n)):(as(t),t.flags&=-16777217):(e.memoizedProps!==r&&es(t),as(t),t.flags&=-16777217),null;case 27:Y(t),n=q.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&es(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return as(t),null}e=H.current,fa(t)?ca(t):(e=kd(a,r,n),t.stateNode=e,es(t))}return as(t),null;case 5:if(Y(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&es(t);else{if(!r){if(null===t.stateNode)throw Error(i(166));return as(t),null}if(e=H.current,fa(t))ca(t);else{switch(a=ld(q.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Re]=t,e[_e]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(nd(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&es(t)}}return as(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&es(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(i(166));if(e=q.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Re]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Jc(e.nodeValue,n)))||ua(t)}else(e=ld(e).createTextNode(r))[Re]=t,t.stateNode=e}return as(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(i(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(i(317));a[Re]=t}else ma(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;as(t),a=!1}else a=pa(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(uo(t),t):(uo(t),null)}if(uo(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var l=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(l=r.memoizedState.cachePool.pool),l!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),ns(t,t.updateQueue),as(t),null;case 4:return $(),null===e&&Hc(t.stateNode.containerInfo),as(t),null;case 10:return xa(t.type),as(t),null;case 19:if(U(co),null===(a=t.memoizedState))return as(t),null;if(r=0!==(128&t.flags),null===(l=a.rendering))if(r)rs(a,!1);else{if(0!==pu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=fo(e))){for(t.flags|=128,rs(a,!1),e=l.updateQueue,t.updateQueue=e,ns(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ir(n,e),n=n.sibling;return B(co,1&co.current|2),t.child}e=e.sibling}null!==a.tail&&te()>ju&&(t.flags|=128,r=!0,rs(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=fo(l))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,ns(t,e),rs(a,!0),null===a.tail&&"hidden"===a.tailMode&&!l.alternate&&!la)return as(t),null}else 2*te()-a.renderingStartTime>ju&&536870912!==n&&(t.flags|=128,r=!0,rs(a,!1),t.lanes=4194304);a.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=a.last)?e.sibling=l:t.child=l,a.last=l)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=co.current,B(co,r?1&e|2:1&e),t):(as(t),null);case 22:case 23:return uo(t),gl(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(as(t),6&t.subtreeFlags&&(t.flags|=8192)):as(t),null!==(n=t.updateQueue)&&ns(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&U(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),xa(Ra),as(t),null;case 25:case 30:return null}throw Error(i(156,t.tag))}function is(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return xa(Ra),$(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Y(t),null;case 13:if(uo(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(i(340));ma()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return U(co),null;case 4:return $(),null;case 10:return xa(t.type),null;case 22:case 23:return uo(t),gl(),null!==e&&U(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return xa(Ra),null;default:return null}}function os(e,t){switch(na(t),t.tag){case 3:xa(Ra),$();break;case 26:case 27:case 5:Y(t);break;case 4:$();break;case 13:uo(t);break;case 19:U(co);break;case 10:xa(t.type);break;case 22:case 23:uo(t),gl(),null!==e&&U(Ba);break;case 24:xa(Ra)}}function ss(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var l=n.create,i=n.inst;r=l(),i.destroy=r}n=n.next}while(n!==a)}}catch(o){fc(t,t.return,o)}}function us(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var l=a.next;r=l;do{if((r.tag&e)===e){var i=r.inst,o=i.destroy;if(void 0!==o){i.destroy=void 0,a=t;var s=n,u=o;try{u()}catch(c){fc(a,s,c)}}}r=r.next}while(r!==l)}}catch(c){fc(t,t.return,c)}}function cs(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fl(t,n)}catch(r){fc(e,e.return,r)}}}function ds(e,t,n){n.props=go(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){fc(e,t,r)}}function fs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"===typeof n?e.refCleanup=n(r):n.current=r}}catch(a){fc(e,t,a)}}function ms(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(a){fc(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(l){fc(e,t,l)}else n.current=null}function ps(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){fc(e,e.return,a)}}function hs(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,l=null,o=null,s=null,u=null,c=null,d=null;for(p in n){var f=n[p];if(n.hasOwnProperty(p)&&null!=f)switch(p){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(p)||ed(e,t,p,null,r,f)}}for(var m in r){var p=r[m];if(f=n[m],r.hasOwnProperty(m)&&(null!=p||null!=f))switch(m){case"type":l=p;break;case"name":a=p;break;case"checked":c=p;break;case"defaultChecked":d=p;break;case"value":o=p;break;case"defaultValue":s=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(i(137,t));break;default:p!==f&&ed(e,t,m,p,r,f)}}return void vt(e,o,s,u,c,d,l,a);case"select":for(l in p=o=s=m=null,n)if(u=n[l],n.hasOwnProperty(l)&&null!=u)switch(l){case"value":break;case"multiple":p=u;default:r.hasOwnProperty(l)||ed(e,t,l,null,r,u)}for(a in r)if(l=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=l||null!=u))switch(a){case"value":m=l;break;case"defaultValue":s=l;break;case"multiple":o=l;default:l!==u&&ed(e,t,a,l,r,u)}return t=s,n=o,r=p,void(null!=m?yt(e,!!n,m,!1):!!r!==!!n&&(null!=t?yt(e,!!n,t,!0):yt(e,!!n,n?[]:"",!1)));case"textarea":for(s in p=m=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:ed(e,t,s,null,r,a)}for(o in r)if(a=r[o],l=n[o],r.hasOwnProperty(o)&&(null!=a||null!=l))switch(o){case"value":m=a;break;case"defaultValue":p=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(i(91));break;default:a!==l&&ed(e,t,o,a,r,l)}return void xt(e,m,p);case"option":for(var h in n)if(m=n[h],n.hasOwnProperty(h)&&null!=m&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else ed(e,t,h,null,r,m);for(u in r)if(m=r[u],p=n[u],r.hasOwnProperty(u)&&m!==p&&(null!=m||null!=p))if("selected"===u)e.selected=m&&"function"!==typeof m&&"symbol"!==typeof m;else ed(e,t,u,m,r,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var v in n)m=n[v],n.hasOwnProperty(v)&&null!=m&&!r.hasOwnProperty(v)&&ed(e,t,v,null,r,m);for(c in r)if(m=r[c],p=n[c],r.hasOwnProperty(c)&&m!==p&&(null!=m||null!=p))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(i(137,t));break;default:ed(e,t,c,m,r,p)}return;default:if(Et(t)){for(var g in n)m=n[g],n.hasOwnProperty(g)&&void 0!==m&&!r.hasOwnProperty(g)&&td(e,t,g,void 0,r,m);for(d in r)m=r[d],p=n[d],!r.hasOwnProperty(d)||m===p||void 0===m&&void 0===p||td(e,t,d,m,r,p);return}}for(var b in n)m=n[b],n.hasOwnProperty(b)&&null!=m&&!r.hasOwnProperty(b)&&ed(e,t,b,null,r,m);for(f in r)m=r[f],p=n[f],!r.hasOwnProperty(f)||m===p||null==m&&null==p||ed(e,t,f,m,r,p)}(r,e.type,n,t),r[_e]=t}catch(a){fc(e,e.return,a)}}function vs(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&hd(e.type)||4===e.tag}function gs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||vs(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&hd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function bs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zc));else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(bs(e,t,n),e=e.sibling;null!==e;)bs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&hd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function xs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);nd(t,r,n),t[Re]=e,t[_e]=n}catch(l){fc(e,e.return,l)}}var ws=!1,ks=!1,Ss=!1,js="function"===typeof WeakSet?WeakSet:Set,Ns=null;function Es(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Is(e,n),4&r&&ss(5,n);break;case 1:if(Is(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){fc(n,n.return,i)}else{var a=go(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){fc(n,n.return,o)}}64&r&&cs(n),512&r&&fs(n,n.return);break;case 3:if(Is(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fl(e,t)}catch(i){fc(n,n.return,i)}}break;case 27:null===t&&4&r&&xs(n);case 26:case 5:Is(e,n),null===t&&4&r&&ps(n),512&r&&fs(n,n.return);break;case 12:Is(e,n);break;case 13:Is(e,n),4&r&&Rs(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=vc.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||ws)){t=null!==t&&null!==t.memoizedState||ks,a=ws;var l=ks;ws=r,(ks=t)&&!l?Bs(e,n,0!==(8772&n.subtreeFlags)):Is(e,n),ws=a,ks=l}break;case 30:break;default:Is(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&Ue(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ps=null,Ts=!1;function Os(e,t,n){for(n=n.child;null!==n;)Ls(e,t,n),n=n.sibling}function Ls(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(l){}switch(n.tag){case 26:ks||ms(n,t),Os(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ks||ms(n,t);var r=Ps,a=Ts;hd(n.type)&&(Ps=n.stateNode,Ts=!1),Os(e,t,n),Sd(n.stateNode),Ps=r,Ts=a;break;case 5:ks||ms(n,t);case 6:if(r=Ps,a=Ts,Ps=null,Os(e,t,n),Ts=a,null!==(Ps=r))if(Ts)try{(9===Ps.nodeType?Ps.body:"HTML"===Ps.nodeName?Ps.ownerDocument.body:Ps).removeChild(n.stateNode)}catch(i){fc(n,t,i)}else try{Ps.removeChild(n.stateNode)}catch(i){fc(n,t,i)}break;case 18:null!==Ps&&(Ts?(vd(9===(e=Ps).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Of(e)):vd(Ps,n.stateNode));break;case 4:r=Ps,a=Ts,Ps=n.stateNode.containerInfo,Ts=!0,Os(e,t,n),Ps=r,Ts=a;break;case 0:case 11:case 14:case 15:ks||us(2,n,t),ks||us(4,n,t),Os(e,t,n);break;case 1:ks||(ms(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&ds(n,t,r)),Os(e,t,n);break;case 21:Os(e,t,n);break;case 22:ks=(r=ks)||null!==n.memoizedState,Os(e,t,n),ks=r;break;default:Os(e,t,n)}}function Rs(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Of(e)}catch(n){fc(t,t.return,n)}}function _s(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new js),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new js),t;default:throw Error(i(435,e.tag))}}(e);t.forEach(function(t){var r=gc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function As(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],l=e,o=t,s=o;e:for(;null!==s;){switch(s.tag){case 27:if(hd(s.type)){Ps=s.stateNode,Ts=!1;break e}break;case 5:Ps=s.stateNode,Ts=!1;break e;case 3:case 4:Ps=s.stateNode.containerInfo,Ts=!0;break e}s=s.return}if(null===Ps)throw Error(i(160));Ls(l,o,a),Ps=null,Ts=!1,null!==(l=a.alternate)&&(l.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)zs(t,e),t=t.sibling}var Fs=null;function zs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:As(t,e),Ds(e),4&r&&(us(3,e,e.return),ss(3,e),us(5,e,e.return));break;case 1:As(t,e),Ds(e),512&r&&(ks||null===n||ms(n,n.return)),64&r&&ws&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var a=Fs;if(As(t,e),Ds(e),512&r&&(ks||null===n||ms(n,n.return)),4&r){var l=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(l=a.getElementsByTagName("title")[0])||l[Ie]||l[Re]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=a.createElement(r),a.head.insertBefore(l,a.querySelector("head > title"))),nd(l,r,n),l[Re]=e,Ve(l),r=l;break e;case"link":var o=Bd("link","href",a).get(r+(n.href||""));if(o)for(var s=0;s<o.length;s++)if((l=o[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){o.splice(s,1);break t}nd(l=a.createElement(r),r,n),a.head.appendChild(l);break;case"meta":if(o=Bd("meta","content",a).get(r+(n.content||"")))for(s=0;s<o.length;s++)if((l=o[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){o.splice(s,1);break t}nd(l=a.createElement(r),r,n),a.head.appendChild(l);break;default:throw Error(i(468,r))}l[Re]=e,Ve(l),r=l}e.stateNode=r}else Hd(a,e.type,e.stateNode);else e.stateNode=zd(a,r,e.memoizedProps);else l!==r?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===r?Hd(a,e.type,e.stateNode):zd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&hs(e,e.memoizedProps,n.memoizedProps)}break;case 27:As(t,e),Ds(e),512&r&&(ks||null===n||ms(n,n.return)),null!==n&&4&r&&hs(e,e.memoizedProps,n.memoizedProps);break;case 5:if(As(t,e),Ds(e),512&r&&(ks||null===n||ms(n,n.return)),32&e.flags){a=e.stateNode;try{kt(a,"")}catch(p){fc(e,e.return,p)}}4&r&&null!=e.stateNode&&hs(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(Ss=!0);break;case 6:if(As(t,e),Ds(e),4&r){if(null===e.stateNode)throw Error(i(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(p){fc(e,e.return,p)}}break;case 3:if(Ud=null,a=Fs,Fs=Ed(t.containerInfo),As(t,e),Fs=a,Ds(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Of(t.containerInfo)}catch(p){fc(e,e.return,p)}Ss&&(Ss=!1,Ms(e));break;case 4:r=Fs,Fs=Ed(e.stateNode.containerInfo),As(t,e),Ds(e),Fs=r;break;case 12:default:As(t,e),Ds(e);break;case 13:As(t,e),Ds(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(Su=te()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r)));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=ws,d=ks;if(ws=c||a,ks=d||u,As(t,e),ks=d,ws=c,Ds(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||ws||ks||Us(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(l=u.stateNode,a)"function"===typeof(o=l.style).setProperty?o.setProperty("display","none","important"):o.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,m=void 0!==f&&null!==f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==m||"boolean"===typeof m?"":(""+m).trim()}}catch(p){fc(u,u.return,p)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(p){fc(u,u.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,_s(e,n))));break;case 19:As(t,e),Ds(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r)));case 30:case 21:}}function Ds(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(vs(r)){n=r;break}r=r.return}if(null==n)throw Error(i(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,gs(e),a);break;case 5:var l=n.stateNode;32&n.flags&&(kt(l,""),n.flags&=-33),ys(e,gs(e),l);break;case 3:case 4:var o=n.stateNode.containerInfo;bs(e,gs(e),o);break;default:throw Error(i(161))}}catch(s){fc(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Ms(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Ms(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Is(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Es(e,t.alternate,t),t=t.sibling}function Us(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:us(4,t,t.return),Us(t);break;case 1:ms(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&ds(t,t.return,n),Us(t);break;case 27:Sd(t.stateNode);case 26:case 5:ms(t,t.return),Us(t);break;case 22:null===t.memoizedState&&Us(t);break;default:Us(t)}e=e.sibling}}function Bs(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,l=t,i=l.flags;switch(l.tag){case 0:case 11:case 15:Bs(a,l,n),ss(4,l);break;case 1:if(Bs(a,l,n),"function"===typeof(a=(r=l).stateNode).componentDidMount)try{a.componentDidMount()}catch(u){fc(r,r.return,u)}if(null!==(a=(r=l).updateQueue)){var o=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)dl(s[a],o)}catch(u){fc(r,r.return,u)}}n&&64&i&&cs(l),fs(l,l.return);break;case 27:xs(l);case 26:case 5:Bs(a,l,n),n&&null===r&&4&i&&ps(l),fs(l,l.return);break;case 12:Bs(a,l,n);break;case 13:Bs(a,l,n),n&&4&i&&Rs(a,l);break;case 22:null===l.memoizedState&&Bs(a,l,n),fs(l,l.return);break;case 30:break;default:Bs(a,l,n)}t=t.sibling}}function Hs(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Aa(n))}function Ws(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e))}function qs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Vs(e,t,n,r),t=t.sibling}function Vs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:qs(e,t,n,r),2048&a&&ss(9,t);break;case 1:case 13:default:qs(e,t,n,r);break;case 3:qs(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Aa(e)));break;case 12:if(2048&a){qs(e,t,n,r),e=t.stateNode;try{var l=t.memoizedProps,i=l.id,o=l.onPostCommit;"function"===typeof o&&o(i,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){fc(t,t.return,s)}}else qs(e,t,n,r);break;case 23:break;case 22:l=t.stateNode,i=t.alternate,null!==t.memoizedState?2&l._visibility?qs(e,t,n,r):$s(e,t):2&l._visibility?qs(e,t,n,r):(l._visibility|=2,Ks(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&a&&Hs(i,t);break;case 24:qs(e,t,n,r),2048&a&&Ws(t.alternate,t)}}function Ks(e,t,n,r,a){for(a=a&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var l=e,i=t,o=n,s=r,u=i.flags;switch(i.tag){case 0:case 11:case 15:Ks(l,i,o,s,a),ss(8,i);break;case 23:break;case 22:var c=i.stateNode;null!==i.memoizedState?2&c._visibility?Ks(l,i,o,s,a):$s(l,i):(c._visibility|=2,Ks(l,i,o,s,a)),a&&2048&u&&Hs(i.alternate,i);break;case 24:Ks(l,i,o,s,a),a&&2048&u&&Ws(i.alternate,i);break;default:Ks(l,i,o,s,a)}t=t.sibling}}function $s(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:$s(n,r),2048&a&&Hs(r.alternate,r);break;case 24:$s(n,r),2048&a&&Ws(r.alternate,r);break;default:$s(n,r)}t=t.sibling}}var Qs=8192;function Ys(e){if(e.subtreeFlags&Qs)for(e=e.child;null!==e;)Gs(e),e=e.sibling}function Gs(e){switch(e.tag){case 26:Ys(e),e.flags&Qs&&null!==e.memoizedState&&function(e,t,n){if(null===qd)throw Error(i(475));var r=qd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var a=Ld(n.href),l=e.querySelector(Rd(a));if(l)return null!==(e=l._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Kd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=l,void Ve(l);l=e.ownerDocument||e,n=_d(n),(a=jd.get(a))&&Md(n,a),Ve(l=l.createElement("link"));var o=l;o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),nd(l,"link",n),t.instance=l}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Kd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Fs,e.memoizedState,e.memoizedProps);break;case 5:default:Ys(e);break;case 3:case 4:var t=Fs;Fs=Ed(e.stateNode.containerInfo),Ys(e),Fs=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Qs,Qs=16777216,Ys(e),Qs=t):Ys(e))}}function Xs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Js(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ns=r,tu(r,e)}Xs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Zs(e),e=e.sibling}function Zs(e){switch(e.tag){case 0:case 11:case 15:Js(e),2048&e.flags&&us(9,e,e.return);break;case 3:case 12:default:Js(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,eu(e)):Js(e)}}function eu(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Ns=r,tu(r,e)}Xs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:us(8,t,t.return),eu(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,eu(t));break;default:eu(t)}e=e.sibling}}function tu(e,t){for(;null!==Ns;){var n=Ns;switch(n.tag){case 0:case 11:case 15:us(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Aa(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Ns=r;else e:for(n=e;null!==Ns;){var a=(r=Ns).sibling,l=r.return;if(Cs(r),r===n){Ns=null;break e}if(null!==a){a.return=l,Ns=a;break e}Ns=l}}}var nu={getCacheForType:function(e){var t=Ea(Ra),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},ru="function"===typeof WeakMap?WeakMap:Map,au=0,lu=null,iu=null,ou=0,su=0,uu=null,cu=!1,du=!1,fu=!1,mu=0,pu=0,hu=0,vu=0,gu=0,bu=0,yu=0,xu=null,wu=null,ku=!1,Su=0,ju=1/0,Nu=null,Eu=null,Cu=0,Pu=null,Tu=null,Ou=0,Lu=0,Ru=null,_u=null,Au=0,Fu=null;function zu(){if(0!==(2&au)&&0!==ou)return ou&-ou;if(null!==A.T){return 0!==Da?Da:Lc()}return Oe()}function Du(){0===bu&&(bu=0===(536870912&ou)||la?ke():536870912);var e=ao.current;return null!==e&&(e.flags|=32),bu}function Mu(e,t,n){(e!==lu||2!==su&&9!==su)&&null===e.cancelPendingCommit||(Vu(e,0),Hu(e,ou,bu,!1)),Ne(e,n),0!==(2&au)&&e===lu||(e===lu&&(0===(2&au)&&(vu|=n),4===pu&&Hu(e,ou,bu,!1)),jc(e))}function Iu(e,t,n){if(0!==(6&au))throw Error(i(327));for(var r=!n&&0===(124&t)&&0===(t&e.expiredLanes)||xe(e,t),a=r?function(e,t){var n=au;au|=2;var r=$u(),a=Qu();lu!==e||ou!==t?(Nu=null,ju=te()+500,Vu(e,t)):du=xe(e,t);e:for(;;)try{if(0!==su&&null!==iu){t=iu;var l=uu;t:switch(su){case 1:su=0,uu=null,tc(e,t,l,1);break;case 2:case 9:if(Ya(l)){su=0,uu=null,ec(t);break}t=function(){2!==su&&9!==su||lu!==e||(su=7),jc(e)},l.then(t,t);break e;case 3:su=7;break e;case 4:su=5;break e;case 7:Ya(l)?(su=0,uu=null,ec(t)):(su=0,uu=null,tc(e,t,l,7));break;case 5:var o=null;switch(iu.tag){case 26:o=iu.memoizedState;case 5:case 27:var s=iu;if(!o||Wd(o)){su=0,uu=null;var u=s.sibling;if(null!==u)iu=u;else{var c=s.return;null!==c?(iu=c,nc(c)):iu=null}break t}}su=0,uu=null,tc(e,t,l,5);break;case 6:su=0,uu=null,tc(e,t,l,6);break;case 8:qu(),pu=6;break e;default:throw Error(i(462))}}Ju();break}catch(d){Ku(e,d)}return ba=ga=null,A.H=r,A.A=a,au=n,null!==iu?0:(lu=null,ou=0,Pr(),pu)}(e,t):Gu(e,t,!0),l=r;;){if(0===a){du&&!r&&Hu(e,t,0,!1);break}if(n=e.current.alternate,!l||Bu(n)){if(2===a){if(l=t,e.errorRecoveryDisabledLanes&l)var o=0;else o=0!==(o=-536870913&e.pendingLanes)?o:536870912&o?536870912:0;if(0!==o){t=o;e:{var s=e;a=xu;var u=s.current.memoizedState.isDehydrated;if(u&&(Vu(s,o).flags|=256),2!==(o=Gu(s,o,!1))){if(fu&&!u){s.errorRecoveryDisabledLanes|=l,vu|=l,a=4;break e}l=wu,wu=a,null!==l&&(null===wu?wu=l:wu.push.apply(wu,l))}a=o}if(l=!1,2!==a)continue}}if(1===a){Vu(e,0),Hu(e,t,0,!0);break}e:{switch(r=e,l=a){case 0:case 1:throw Error(i(345));case 4:if((4194048&t)!==t)break;case 6:Hu(r,t,bu,!cu);break e;case 2:wu=null;break;case 3:case 5:break;default:throw Error(i(329))}if((62914560&t)===t&&10<(a=Su+300-te())){if(Hu(r,t,bu,!cu),0!==ye(r,0,!0))break e;r.timeoutHandle=cd(Uu.bind(null,r,n,wu,Nu,ku,t,bu,vu,yu,cu,l,2,-0,0),a)}else Uu(r,n,wu,Nu,ku,t,bu,vu,yu,cu,l,0,-0,0)}break}a=Gu(e,t,!1),l=!1}jc(e)}function Uu(e,t,n,r,a,l,o,s,u,c,d,f,m,p){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||16785408===(16785408&f))&&(qd={stylesheets:null,count:0,unsuspend:Vd},Gs(t),null!==(f=function(){if(null===qd)throw Error(i(475));var e=qd;return e.stylesheets&&0===e.count&&Qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(ac.bind(null,e,t,l,n,r,a,o,s,u,d,1,m,p)),void Hu(e,l,o,!c);ac(e,t,l,n,r,a,o,s,u)}function Bu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!Yn(l(),a))return!1}catch(i){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Hu(e,t,n,r){t&=~gu,t&=~vu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var l=31-me(a),i=1<<l;r[l]=-1,a&=~i}0!==n&&Ee(e,n,t)}function Wu(){return 0!==(6&au)||(Nc(0,!1),!1)}function qu(){if(null!==iu){if(0===su)var e=iu.return;else ba=ga=null,Dl(e=iu),Yi=null,Gi=0,e=iu;for(;null!==e;)os(e.alternate,e),e=e.return;iu=null}}function Vu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,dd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),qu(),lu=e,iu=n=Mr(e.current,null),ou=t,su=0,uu=null,cu=!1,du=xe(e,t),fu=!1,yu=bu=gu=vu=hu=pu=0,wu=xu=null,ku=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-me(r),l=1<<a;t|=e[a],r&=~l}return mu=t,Pr(),n}function Ku(e,t){yl=null,A.H=Vi,t===Va||t===$a?(t=Za(),su=3):t===Ka?(t=Za(),su=4):su=t===Co?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,uu=t,null===iu&&(pu=1,ko(e,jr(t,e.current)))}function $u(){var e=A.H;return A.H=Vi,null===e?Vi:e}function Qu(){var e=A.A;return A.A=nu,e}function Yu(){pu=4,cu||(4194048&ou)!==ou&&null!==ao.current||(du=!0),0===(134217727&hu)&&0===(134217727&vu)||null===lu||Hu(lu,ou,bu,!1)}function Gu(e,t,n){var r=au;au|=2;var a=$u(),l=Qu();lu===e&&ou===t||(Nu=null,Vu(e,t)),t=!1;var i=pu;e:for(;;)try{if(0!==su&&null!==iu){var o=iu,s=uu;switch(su){case 8:qu(),i=6;break e;case 3:case 2:case 9:case 6:null===ao.current&&(t=!0);var u=su;if(su=0,uu=null,tc(e,o,s,u),n&&du){i=0;break e}break;default:u=su,su=0,uu=null,tc(e,o,s,u)}}Xu(),i=pu;break}catch(c){Ku(e,c)}return t&&e.shellSuspendCounter++,ba=ga=null,au=r,A.H=a,A.A=l,null===iu&&(lu=null,ou=0,Pr()),i}function Xu(){for(;null!==iu;)Zu(iu)}function Ju(){for(;null!==iu&&!Z();)Zu(iu)}function Zu(e){var t=Zo(e.alternate,e,mu);e.memoizedProps=e.pendingProps,null===t?nc(e):iu=t}function ec(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Mo(n,t,t.pendingProps,t.type,void 0,ou);break;case 11:t=Mo(n,t,t.pendingProps,t.type.render,t.ref,ou);break;case 5:Dl(t);default:os(n,t),t=Zo(n,t=iu=Ir(t,mu),mu)}e.memoizedProps=e.pendingProps,null===t?nc(e):iu=t}function tc(e,t,n,r){ba=ga=null,Dl(t),Yi=null,Gi=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&Sa(t,n,a,!0),null!==(n=ao.current)){switch(n.tag){case 13:return null===lo?Yu():null===n.alternate&&0===pu&&(pu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),mc(e,r,a)),!1;case 22:return n.flags|=65536,r===Qa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),mc(e,r,a)),!1}throw Error(i(435,n.tag))}return mc(e,r,a),Yu(),!1}if(la)return null!==(t=ao.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ha(jr(e=Error(i(422),{cause:r}),n))):(r!==sa&&ha(jr(t=Error(i(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=jr(r,n),ol(e,a=jo(e.stateNode,r,a)),4!==pu&&(pu=2)),!1;var l=Error(i(520),{cause:r});if(l=jr(l,n),null===xu?xu=[l]:xu.push(l),4!==pu&&(pu=2),null===t)return!0;r=jr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,ol(n,e=jo(n.stateNode,r,e)),!1;case 1:if(t=n.type,l=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==l&&"function"===typeof l.componentDidCatch&&(null===Eu||!Eu.has(l))))return n.flags|=65536,a&=-a,n.lanes|=a,Eo(a=No(a),e,n,r),ol(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,ou))return pu=1,ko(e,jr(n,e.current)),void(iu=null)}catch(l){if(null!==a)throw iu=a,l;return pu=1,ko(e,jr(n,e.current)),void(iu=null)}32768&t.flags?(la||1===r?e=!0:du||0!==(536870912&ou)?e=!1:(cu=e=!0,(2===r||9===r||3===r||6===r)&&(null!==(r=ao.current)&&13===r.tag&&(r.flags|=16384))),rc(t,e)):nc(t)}function nc(e){var t=e;do{if(0!==(32768&t.flags))return void rc(t,cu);e=t.return;var n=ls(t.alternate,t,mu);if(null!==n)return void(iu=n);if(null!==(t=t.sibling))return void(iu=t);iu=t=e}while(null!==t);0===pu&&(pu=5)}function rc(e,t){do{var n=is(e.alternate,e);if(null!==n)return n.flags&=32767,void(iu=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(iu=e);iu=e=n}while(null!==e);pu=6,iu=null}function ac(e,t,n,r,a,l,o,s,u){e.cancelPendingCommit=null;do{uc()}while(0!==Cu);if(0!==(6&au))throw Error(i(327));if(null!==t){if(t===e.current)throw Error(i(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,l){var i=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var o=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=i&~n;0<n;){var c=31-me(n),d=1<<c;o[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var m=f[c];null!==m&&(m.lane&=-536870913)}n&=~d}0!==r&&Ee(e,r,0),0!==l&&0===a&&0!==e.tag&&(e.suspendedLanes|=l&~(i&~t))}(e,n,l|=Cr,o,s,u),e===lu&&(iu=lu=null,ou=0),Tu=t,Pu=e,Ou=n,Lu=l,Ru=a,_u=r,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,X(le,function(){return cc(),null})):(e.callbackNode=null,e.callbackPriority=0),r=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||r){r=A.T,A.T=null,a=F.p,F.p=2,o=au,au|=4;try{!function(e,t){if(e=e.containerInfo,rd=af,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(v){n=null;break e}var o=0,s=-1,u=-1,c=0,d=0,f=e,m=null;t:for(;;){for(var p;f!==n||0!==a&&3!==f.nodeType||(s=o+a),f!==l||0!==r&&3!==f.nodeType||(u=o+r),3===f.nodeType&&(o+=f.nodeValue.length),null!==(p=f.firstChild);)m=f,f=p;for(;;){if(f===e)break t;if(m===n&&++c===a&&(s=o),m===l&&++d===r&&(u=o),null!==(p=f.nextSibling))break;m=(f=m).parentNode}f=p}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ad={focusedElem:e,selectionRange:n},af=!1,Ns=t;null!==Ns;)if(e=(t=Ns).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Ns=e;else for(;null!==Ns;){switch(l=(t=Ns).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==l){e=void 0,n=t,a=l.memoizedProps,l=l.memoizedState,r=n.stateNode;try{var h=go(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,l),r.__reactInternalSnapshotBeforeUpdate=e}catch(g){fc(n,n.return,g)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))gd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(i(163))}if(null!==(e=t.sibling)){e.return=t.return,Ns=e;break}Ns=t.return}}(e,t)}finally{au=o,F.p=a,A.T=r}}Cu=1,lc(),ic(),oc()}}function lc(){if(1===Cu){Cu=0;var e=Pu,t=Tu,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=A.T,A.T=null;var r=F.p;F.p=2;var a=au;au|=4;try{zs(t,e);var l=ad,i=er(e.containerInfo),o=l.focusedElem,s=l.selectionRange;if(i!==o&&o&&o.ownerDocument&&Zn(o.ownerDocument.documentElement,o)){if(null!==s&&tr(o)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in o)o.selectionStart=u,o.selectionEnd=Math.min(c,o.value.length);else{var d=o.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var m=f.getSelection(),p=o.textContent.length,h=Math.min(s.start,p),v=void 0===s.end?h:Math.min(s.end,p);!m.extend&&h>v&&(i=v,v=h,h=i);var g=Jn(o,h),b=Jn(o,v);if(g&&b&&(1!==m.rangeCount||m.anchorNode!==g.node||m.anchorOffset!==g.offset||m.focusNode!==b.node||m.focusOffset!==b.offset)){var y=d.createRange();y.setStart(g.node,g.offset),m.removeAllRanges(),h>v?(m.addRange(y),m.extend(b.node,b.offset)):(y.setEnd(b.node,b.offset),m.addRange(y))}}}}for(d=[],m=o;m=m.parentNode;)1===m.nodeType&&d.push({element:m,left:m.scrollLeft,top:m.scrollTop});for("function"===typeof o.focus&&o.focus(),o=0;o<d.length;o++){var x=d[o];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}af=!!rd,ad=rd=null}finally{au=a,F.p=r,A.T=n}}e.current=t,Cu=2}}function ic(){if(2===Cu){Cu=0;var e=Pu,t=Tu,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=A.T,A.T=null;var r=F.p;F.p=2;var a=au;au|=4;try{Es(e,t.alternate,t)}finally{au=a,F.p=r,A.T=n}}Cu=3}}function oc(){if(4===Cu||3===Cu){Cu=0,ee();var e=Pu,t=Tu,n=Ou,r=_u;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Cu=5:(Cu=0,Tu=Pu=null,sc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Eu=null),Te(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,128===(128&t.current.flags))}catch(s){}if(null!==r){t=A.T,a=F.p,F.p=2,A.T=null;try{for(var l=e.onRecoverableError,i=0;i<r.length;i++){var o=r[i];l(o.value,{componentStack:o.stack})}}finally{A.T=t,F.p=a}}0!==(3&Ou)&&uc(),jc(e),a=e.pendingLanes,0!==(4194090&n)&&0!==(42&a)?e===Fu?Au++:(Au=0,Fu=e):Au=0,Nc(0,!1)}}function sc(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Aa(t)))}function uc(e){return lc(),ic(),oc(),cc()}function cc(){if(5!==Cu)return!1;var e=Pu,t=Lu;Lu=0;var n=Te(Ou),r=A.T,a=F.p;try{F.p=32>n?32:n,A.T=null,n=Ru,Ru=null;var l=Pu,o=Ou;if(Cu=0,Tu=Pu=null,Ou=0,0!==(6&au))throw Error(i(331));var s=au;if(au|=4,Zs(l.current),Vs(l,l.current,o,n),au=s,Nc(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,l)}catch(u){}return!0}finally{F.p=a,A.T=r,sc(e,t)}}function dc(e,t,n){t=jr(n,t),null!==(e=ll(e,t=jo(e.stateNode,t,2),2))&&(Ne(e,2),jc(e))}function fc(e,t,n){if(3===e.tag)dc(e,e,n);else for(;null!==t;){if(3===t.tag){dc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Eu||!Eu.has(r))){e=jr(n,e),null!==(r=ll(t,n=No(2),2))&&(Eo(n,r,t,e),Ne(r,2),jc(r));break}}t=t.return}}function mc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new ru;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(fu=!0,a.add(n),e=pc.bind(null,e,t,n),t.then(e,e))}function pc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,lu===e&&(ou&n)===n&&(4===pu||3===pu&&(62914560&ou)===ou&&300>te()-Su?0===(2&au)&&Vu(e,0):gu|=n,yu===ou&&(yu=0)),jc(e)}function hc(e,t){0===t&&(t=Se()),null!==(e=Lr(e,t))&&(Ne(e,t),jc(e))}function vc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),hc(e,n)}function gc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(i(314))}null!==r&&r.delete(t),hc(e,n)}var bc=null,yc=null,xc=!1,wc=!1,kc=!1,Sc=0;function jc(e){e!==yc&&null===e.next&&(null===yc?bc=yc=e:yc=yc.next=e),wc=!0,xc||(xc=!0,md(function(){0!==(6&au)?X(re,Ec):Cc()}))}function Nc(e,t){if(!kc&&wc){kc=!0;do{for(var n=!1,r=bc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var l=0;else{var i=r.suspendedLanes,o=r.pingedLanes;l=(1<<31-me(42|e)+1)-1,l=201326741&(l&=a&~(i&~o))?201326741&l|1:l?2|l:0}0!==l&&(n=!0,Oc(r,l))}else l=ou,0===(3&(l=ye(r,r===lu?l:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||xe(r,l)||(n=!0,Oc(r,l));r=r.next}}while(n);kc=!1}}function Ec(){Cc()}function Cc(){wc=xc=!1;var e=0;0!==Sc&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==ud&&(ud=e,!0);return ud=null,!1}()&&(e=Sc),Sc=0);for(var t=te(),n=null,r=bc;null!==r;){var a=r.next,l=Pc(r,t);0===l?(r.next=null,null===n?bc=a:n.next=a,null===a&&(yc=n)):(n=r,(0!==e||0!==(3&l))&&(wc=!0)),r=a}Nc(e,!1)}function Pc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=-62914561&e.pendingLanes;0<l;){var i=31-me(l),o=1<<i,s=a[i];-1===s?0!==(o&n)&&0===(o&r)||(a[i]=we(o,t)):s<=t&&(e.expiredLanes|=o),l&=~o}if(n=ou,n=ye(e,e===(t=lu)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===su||9===su)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||xe(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&J(r),Te(n)){case 2:case 8:n=ae;break;case 32:default:n=le;break;case 268435456:n=oe}return r=Tc.bind(null,e),n=X(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function Tc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(uc()&&e.callbackNode!==n)return null;var r=ou;return 0===(r=ye(e,e===lu?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Iu(e,r,t),Pc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Tc.bind(null,e):null)}function Oc(e,t){if(uc())return null;Iu(e,t,!0)}function Lc(){return 0===Sc&&(Sc=ke()),Sc}function Rc(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Tt(""+e)}function _c(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ac=0;Ac<wr.length;Ac++){var Fc=wr[Ac];kr(Fc.toLowerCase(),"on"+(Fc[0].toUpperCase()+Fc.slice(1)))}kr(mr,"onAnimationEnd"),kr(pr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(vr,"onTransitionRun"),kr(gr,"onTransitionStart"),kr(br,"onTransitionCancel"),kr(yr,"onTransitionEnd"),Ye("onMouseEnter",["mouseout","mouseover"]),Ye("onMouseLeave",["mouseout","mouseover"]),Ye("onPointerEnter",["pointerout","pointerover"]),Ye("onPointerLeave",["pointerout","pointerover"]),Qe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Qe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Qe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Qe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Qe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zc));function Mc(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var i=r.length-1;0<=i;i--){var o=r[i],s=o.instance,u=o.currentTarget;if(o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=u;try{l(a)}catch(c){bo(c)}a.currentTarget=null,l=s}else for(i=0;i<r.length;i++){if(s=(o=r[i]).instance,u=o.currentTarget,o=o.listener,s!==l&&a.isPropagationStopped())break e;l=o,a.currentTarget=u;try{l(a)}catch(c){bo(c)}a.currentTarget=null,l=s}}}}function Ic(e,t){var n=t[Fe];void 0===n&&(n=t[Fe]=new Set);var r=e+"__bubble";n.has(r)||(Wc(t,e,2,!1),n.add(r))}function Uc(e,t,n){var r=0;t&&(r|=4),Wc(n,e,r,t)}var Bc="_reactListening"+Math.random().toString(36).slice(2);function Hc(e){if(!e[Bc]){e[Bc]=!0,Ke.forEach(function(t){"selectionchange"!==t&&(Dc.has(t)||Uc(t,!1,e),Uc(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Bc]||(t[Bc]=!0,Uc("selectionchange",!1,t))}}function Wc(e,t,n,r){switch(ff(t)){case 2:var a=lf;break;case 8:a=of;break;default:a=sf}n=a.bind(null,t,n,e),a=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function qc(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var u=i.tag;if((3===u||4===u)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=Be(o)))return;if(5===(u=i.tag)||6===u||26===u||27===u){r=l=i;continue e}o=o.parentNode}}r=r.return}zt(function(){var r=l,a=Lt(n),i=[];e:{var o=xr.get(e);if(void 0!==o){var u=Zt,c=e;switch(e){case"keypress":if(0===Vt(n))break e;case"keydown":case"keyup":u=hn;break;case"focusin":c="focus",u=ln;break;case"focusout":c="blur",u=ln;break;case"beforeblur":case"afterblur":u=ln;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=gn;break;case mr:case pr:case hr:u=on;break;case yr:u=bn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=yn;break;case"copy":case"cut":case"paste":u=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=vn;break;case"toggle":case"beforetoggle":u=xn}var d=0!==(4&t),f=!d&&("scroll"===e||"scrollend"===e),m=d?null!==o?o+"Capture":null:o;d=[];for(var p,h=r;null!==h;){var v=h;if(p=v.stateNode,5!==(v=v.tag)&&26!==v&&27!==v||null===p||null===m||null!=(v=Dt(h,m))&&d.push(Vc(h,v,p)),f)break;h=h.return}0<d.length&&(o=new u(o,c,null,n,a),i.push({event:o,listeners:d}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===Ot||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[Ae])&&(u||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,v="onMouseLeave",m="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(d=vn,v="onPointerLeave",m="onPointerEnter",h="pointer"),f=null==u?o:We(u),p=null==c?o:We(c),(o=new d(v,h+"leave",u,n,a)).target=f,o.relatedTarget=p,v=null,Be(a)===r&&((d=new d(m,h+"enter",c,n,a)).target=p,d.relatedTarget=f,v=d),f=v,u&&c)e:{for(m=c,h=0,p=d=u;p;p=$c(p))h++;for(p=0,v=m;v;v=$c(v))p++;for(;0<h-p;)d=$c(d),h--;for(;0<p-h;)m=$c(m),p--;for(;h--;){if(d===m||null!==m&&d===m.alternate)break e;d=$c(d),m=$c(m)}d=null}else d=null;null!==u&&Qc(i,o,u,d,!1),null!==c&&null!==f&&Qc(i,f,c,d,!0)}if("select"===(u=(o=r?We(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===u&&"file"===o.type)var g=Mn;else if(Rn(o))if(In)g=Qn;else{g=Kn;var b=Vn}else!(u=o.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&Et(r.elementType)&&(g=Mn):g=$n;switch(g&&(g=g(e,r))?_n(i,g,n,a):(b&&b(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&bt(o,"number",o.value)),b=r?We(r):window,e){case"focusin":(Rn(b)||"true"===b.contentEditable)&&(rr=b,ar=r,lr=null);break;case"focusout":lr=ar=rr=null;break;case"mousedown":ir=!0;break;case"contextmenu":case"mouseup":case"dragend":ir=!1,or(i,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":or(i,n,a)}var y;if(kn)e:{switch(e){case"compositionstart":var x="onCompositionStart";break e;case"compositionend":x="onCompositionEnd";break e;case"compositionupdate":x="onCompositionUpdate";break e}x=void 0}else On?Pn(e,n)&&(x="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(x="onCompositionStart");x&&(Nn&&"ko"!==n.locale&&(On||"onCompositionStart"!==x?"onCompositionEnd"===x&&On&&(y=qt()):(Ht="value"in(Bt=a)?Bt.value:Bt.textContent,On=!0)),0<(b=Kc(r,x)).length&&(x=new un(x,e,null,n,a),i.push({event:x,listeners:b}),y?x.data=y:null!==(y=Tn(n))&&(x.data=y))),(y=jn?function(e,t){switch(e){case"compositionend":return Tn(t);case"keypress":return 32!==t.which?null:(Cn=!0,En);case"textInput":return(e=t.data)===En&&Cn?null:e;default:return null}}(e,n):function(e,t){if(On)return"compositionend"===e||!kn&&Pn(e,t)?(e=qt(),Wt=Ht=Bt=null,On=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(x=Kc(r,"onBeforeInput")).length&&(b=new un("onBeforeInput","beforeinput",null,n,a),i.push({event:b,listeners:x}),b.data=y)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var l=Rc((a[_e]||null).action),i=r.submitter;i&&null!==(t=(t=i[_e]||null)?Rc(t.formAction):i.getAttribute("formAction"))&&(l=t,i=null);var o=new Zt("action","action",null,r,a);e.push({event:o,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==Sc){var e=i?_c(a,i):new FormData(a);Li(n,{pending:!0,data:e,method:a.method,action:l},null,e)}}else"function"===typeof l&&(o.preventDefault(),e=i?_c(a,i):new FormData(a),Li(n,{pending:!0,data:e,method:a.method,action:l},l,e))},currentTarget:a}]})}}(i,e,r,n,a)}Mc(i,t)})}function Vc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===l||(null!=(a=Dt(e,n))&&r.unshift(Vc(e,a,l)),null!=(a=Dt(e,t))&&r.push(Vc(e,a,l))),3===e.tag)return r;e=e.return}return[]}function $c(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Qc(e,t,n,r,a){for(var l=t._reactName,i=[];null!==n&&n!==r;){var o=n,s=o.alternate,u=o.stateNode;if(o=o.tag,null!==s&&s===r)break;5!==o&&26!==o&&27!==o||null===u||(s=u,a?null!=(u=Dt(n,l))&&i.unshift(Vc(n,u,s)):a||null!=(u=Dt(n,l))&&i.push(Vc(n,u,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Yc=/\r\n?/g,Gc=/\u0000|\uFFFD/g;function Xc(e){return("string"===typeof e?e:""+e).replace(Yc,"\n").replace(Gc,"")}function Jc(e,t){return t=Xc(t),Xc(e)===t}function Zc(){}function ed(e,t,n,r,a,l){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Nt(e,r,l);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof l&&("formAction"===n?("input"!==t&&ed(e,t,"name",a.name,a,null),ed(e,t,"formEncType",a.formEncType,a,null),ed(e,t,"formMethod",a.formMethod,a,null),ed(e,t,"formTarget",a.formTarget,a,null)):(ed(e,t,"encType",a.encType,a,null),ed(e,t,"method",a.method,a,null),ed(e,t,"target",a.target,a,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=Tt(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Zc);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=Tt(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Ic("beforetoggle",e),Ic("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Ct.get(n)||n,r)}}function td(e,t,n,r,a,l){switch(n){case"style":Nt(e,r,l);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(i(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(i(60));e.innerHTML=n}}break;case"children":"string"===typeof r?kt(e,r):("number"===typeof r||"bigint"===typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&Ic("scroll",e);break;case"onScrollEnd":null!=r&&Ic("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Zc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:$e.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"===typeof(l=null!=(l=e[_e]||null)?l[n]:null)&&e.removeEventListener(t,l,a),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!==typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function nd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Ic("error",e),Ic("load",e);var r,a=!1,l=!1;for(r in n)if(n.hasOwnProperty(r)){var o=n[r];if(null!=o)switch(r){case"src":a=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:ed(e,t,r,o,n,null)}}return l&&ed(e,t,"srcSet",n.srcSet,n,null),void(a&&ed(e,t,"src",n.src,n,null));case"input":Ic("invalid",e);var s=r=o=l=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":l=d;break;case"type":o=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(i(137,t));break;default:ed(e,t,a,d,n,null)}}return gt(e,r,s,u,c,o,l,!1),void dt(e);case"select":for(l in Ic("invalid",e),a=o=r=null,n)if(n.hasOwnProperty(l)&&null!=(s=n[l]))switch(l){case"value":r=s;break;case"defaultValue":o=s;break;case"multiple":a=s;default:ed(e,t,l,s,n,null)}return t=r,n=o,e.multiple=!!a,void(null!=t?yt(e,!!a,t,!1):null!=n&&yt(e,!!a,n,!0));case"textarea":for(o in Ic("invalid",e),r=l=a=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":a=s;break;case"defaultValue":l=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(i(91));break;default:ed(e,t,o,s,n,null)}return wt(e,a,l,r),void dt(e);case"option":for(u in n)if(n.hasOwnProperty(u)&&null!=(a=n[u]))if("selected"===u)e.selected=a&&"function"!==typeof a&&"symbol"!==typeof a;else ed(e,t,u,a,n,null);return;case"dialog":Ic("beforetoggle",e),Ic("toggle",e),Ic("cancel",e),Ic("close",e);break;case"iframe":case"object":Ic("load",e);break;case"video":case"audio":for(a=0;a<zc.length;a++)Ic(zc[a],e);break;case"image":Ic("error",e),Ic("load",e);break;case"details":Ic("toggle",e);break;case"embed":case"source":case"link":Ic("error",e),Ic("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(i(137,t));default:ed(e,t,c,a,n,null)}return;default:if(Et(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(a=n[d])&&td(e,t,d,a,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(a=n[s])&&ed(e,t,s,a,n,null))}var rd=null,ad=null;function ld(e){return 9===e.nodeType?e:e.ownerDocument}function id(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function sd(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ud=null;var cd="function"===typeof setTimeout?setTimeout:void 0,dd="function"===typeof clearTimeout?clearTimeout:void 0,fd="function"===typeof Promise?Promise:void 0,md="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof fd?function(e){return fd.resolve(null).then(e).catch(pd)}:cd;function pd(e){setTimeout(function(){throw e})}function hd(e){return"head"===e}function vd(e,t){var n=t,r=0,a=0;do{var l=n.nextSibling;if(e.removeChild(n),l&&8===l.nodeType)if("/$"===(n=l.data)){if(0<r&&8>r){n=r;var i=e.ownerDocument;if(1&n&&Sd(i.documentElement),2&n&&Sd(i.body),4&n)for(Sd(n=i.head),i=n.firstChild;i;){var o=i.nextSibling,s=i.nodeName;i[Ie]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===i.rel.toLowerCase()||n.removeChild(i),i=o}}if(0===a)return e.removeChild(l),void Of(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=l}while(n);Of(t)}function gd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gd(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function bd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var xd=null;function wd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function kd(e,t,n){switch(t=ld(n),e){case"html":if(!(e=t.documentElement))throw Error(i(452));return e;case"head":if(!(e=t.head))throw Error(i(453));return e;case"body":if(!(e=t.body))throw Error(i(454));return e;default:throw Error(i(451))}}function Sd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var jd=new Map,Nd=new Set;function Ed(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=F.d;F.d={f:function(){var e=Cd.f(),t=Wu();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?_i(t):Cd.r(e)},D:function(e){Cd.D(e),Td("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Td("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Pd;if(r&&e&&t){var a='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(a+='[imagesizes="'+ht(n.imageSizes)+'"]')):a+='[href="'+ht(e)+'"]';var l=a;switch(t){case"style":l=Ld(e);break;case"script":l=Ad(e)}jd.has(l)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),jd.set(l,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Rd(l))||"script"===t&&r.querySelector(Fd(l))||(nd(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Pd;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',l=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":l=Ad(e)}if(!jd.has(l)&&(e=f({rel:"modulepreload",href:e},t),jd.set(l,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Fd(l)))return}nd(r=n.createElement("link"),"link",e),Ve(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Pd;if(n&&e){var r=qe(n).hoistableScripts,a=Ad(e),l=r.get(a);l||((l=n.querySelector(Fd(a)))||(e=f({src:e,async:!0},t),(t=jd.get(a))&&Id(e,t),Ve(l=n.createElement("script")),nd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}},S:function(e,t,n){Cd.S(e,t,n);var r=Pd;if(r&&e){var a=qe(r).hoistableStyles,l=Ld(e);t=t||"default";var i=a.get(l);if(!i){var o={loading:0,preload:null};if(i=r.querySelector(Rd(l)))o.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=jd.get(l))&&Md(e,n);var s=i=r.createElement("link");Ve(s),nd(s,"link",e),s._p=new Promise(function(e,t){s.onload=e,s.onerror=t}),s.addEventListener("load",function(){o.loading|=1}),s.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Dd(i,t,r)}i={type:"stylesheet",instance:i,count:1,state:o},a.set(l,i)}}},M:function(e,t){Cd.M(e,t);var n=Pd;if(n&&e){var r=qe(n).hoistableScripts,a=Ad(e),l=r.get(a);l||((l=n.querySelector(Fd(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=jd.get(a))&&Id(e,t),Ve(l=n.createElement("script")),nd(l,"link",e),n.head.appendChild(l)),l={type:"script",instance:l,count:1,state:null},r.set(a,l))}}};var Pd="undefined"===typeof document?null:document;function Td(e,t,n){var r=Pd;if(r&&"string"===typeof t&&t){var a=ht(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"===typeof n&&(a+='[crossorigin="'+n+'"]'),Nd.has(a)||(Nd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(nd(t=r.createElement("link"),"link",e),Ve(t),r.head.appendChild(t)))}}function Od(e,t,n,r){var a,l,o,s,u=(u=q.current)?Ed(u):null;if(!u)throw Error(i(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=Ld(n.href),(r=(n=qe(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=Ld(n.href);var c=qe(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Rd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),jd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},jd.set(e,n),c||(a=u,l=e,o=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+l+"]")?s.loading=1:(l=a.createElement("link"),s.preload=l,l.addEventListener("load",function(){return s.loading|=1}),l.addEventListener("error",function(){return s.loading|=2}),nd(l,"link",o),Ve(l),a.head.appendChild(l))))),t&&null===r)throw Error(i(528,""));return d}if(t&&null!==r)throw Error(i(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Ad(n),(r=(n=qe(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(i(444,e))}}function Ld(e){return'href="'+ht(e)+'"'}function Rd(e){return'link[rel="stylesheet"]['+e+"]"}function _d(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Ad(e){return'[src="'+ht(e)+'"]'}function Fd(e){return"script[async]"+e}function zd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,Ve(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Ve(r=(e.ownerDocument||e).createElement("style")),nd(r,"style",a),Dd(r,n.precedence,e),t.instance=r;case"stylesheet":a=Ld(n.href);var l=e.querySelector(Rd(a));if(l)return t.state.loading|=4,t.instance=l,Ve(l),l;r=_d(n),(a=jd.get(a))&&Md(r,a),Ve(l=(e.ownerDocument||e).createElement("link"));var o=l;return o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),nd(l,"link",r),t.state.loading|=4,Dd(l,n.precedence,e),t.instance=l;case"script":return l=Ad(n.src),(a=e.querySelector(Fd(l)))?(t.instance=a,Ve(a),a):(r=n,(a=jd.get(l))&&Id(r=f({},n),a),Ve(a=(e=e.ownerDocument||e).createElement("script")),nd(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(i(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Dd(r,n.precedence,e));return t.instance}function Dd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,l=a,i=0;i<r.length;i++){var o=r[i];if(o.dataset.precedence===t)l=o;else if(l!==a)break}l?l.parentNode.insertBefore(e,l.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Md(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Id(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Ud=null;function Bd(e,t,n){if(null===Ud){var r=new Map,a=Ud=new Map;a.set(n,r)}else(r=(a=Ud).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var l=n[a];if(!(l[Ie]||l[Re]||"link"===e&&"stylesheet"===l.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==l.namespaceURI){var i=l.getAttribute(t)||"";i=e+i;var o=r.get(i);o?o.push(l):r.set(i,[l])}}return r}function Hd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Wd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var qd=null;function Vd(){}function Kd(){if(this.count--,0===this.count)if(this.stylesheets)Qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var $d=null;function Qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,$d=new Map,t.forEach(Yd,e),$d=null,Kd.call(e))}function Yd(e,t){if(!(4&t.state.loading)){var n=$d.get(e);if(n)var r=n.get(null);else{n=new Map,$d.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),l=0;l<a.length;l++){var i=a[l];"LINK"!==i.nodeName&&"not all"===i.getAttribute("media")||(n.set(i.dataset.precedence,i),r=i)}r&&n.set(null,r)}i=(a=t.instance).getAttribute("data-precedence"),(l=n.get(i)||r)===r&&n.set(null,a),n.set(i,a),this.count++,r=Kd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),l?l.parentNode.insertBefore(a,l.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Gd={$$typeof:w,Provider:null,Consumer:null,_currentValue:z,_currentValue2:z,_threadCount:0};function Xd(e,t,n,r,a,l,i,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=je(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=je(0),this.hiddenUpdates=je(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=l,this.onRecoverableError=i,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Jd(e,t,n,r,a,l,i,o,s,u,c,d){return e=new Xd(e,t,n,i,o,s,u,d),t=1,!0===l&&(t|=24),l=zr(3,null,null,t),e.current=l,l.stateNode=e,(t=_a()).refCount++,e.pooledCache=t,t.refCount++,l.memoizedState={element:r,isDehydrated:n,cache:t},nl(l),e}function Zd(e){return e?e=Ar:Ar}function ef(e,t,n,r,a,l){a=Zd(a),null===r.context?r.context=a:r.pendingContext=a,(r=al(t)).payload={element:n},null!==(l=void 0===l?null:l)&&(r.callback=l),null!==(n=ll(e,r,t))&&(Mu(n,0,t),il(n,e,t))}function tf(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function nf(e,t){tf(e,t),(e=e.alternate)&&tf(e,t)}function rf(e){if(13===e.tag){var t=Lr(e,67108864);null!==t&&Mu(t,0,67108864),nf(e,67108864)}}var af=!0;function lf(e,t,n,r){var a=A.T;A.T=null;var l=F.p;try{F.p=2,sf(e,t,n,r)}finally{F.p=l,A.T=a}}function of(e,t,n,r){var a=A.T;A.T=null;var l=F.p;try{F.p=8,sf(e,t,n,r)}finally{F.p=l,A.T=a}}function sf(e,t,n,r){if(af){var a=uf(r);if(null===a)qc(e,t,r,cf,n),wf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return pf=kf(pf,e,t,n,r,a),!0;case"dragenter":return hf=kf(hf,e,t,n,r,a),!0;case"mouseover":return vf=kf(vf,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return gf.set(l,kf(gf.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,bf.set(l,kf(bf.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(wf(e,r),4&t&&-1<xf.indexOf(e)){for(;null!==a;){var l=He(a);if(null!==l)switch(l.tag){case 3:if((l=l.stateNode).current.memoizedState.isDehydrated){var i=be(l.pendingLanes);if(0!==i){var o=l;for(o.pendingLanes|=2,o.entangledLanes|=2;i;){var s=1<<31-me(i);o.entanglements[1]|=s,i&=~s}jc(l),0===(6&au)&&(ju=te()+500,Nc(0,!1))}}break;case 13:null!==(o=Lr(l,2))&&Mu(o,0,2),Wu(),nf(l,2)}if(null===(l=uf(r))&&qc(e,t,r,cf,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else qc(e,t,r,null,n)}}function uf(e){return df(e=Lt(e))}var cf=null;function df(e){if(cf=null,null!==(e=Be(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return cf=e,null}function ff(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case le:case ie:return 32;case oe:return 268435456;default:return 32}default:return 32}}var mf=!1,pf=null,hf=null,vf=null,gf=new Map,bf=new Map,yf=[],xf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function wf(e,t){switch(e){case"focusin":case"focusout":pf=null;break;case"dragenter":case"dragleave":hf=null;break;case"mouseover":case"mouseout":vf=null;break;case"pointerover":case"pointerout":gf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":bf.delete(t.pointerId)}}function kf(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&rf(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Sf(e){var t=Be(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e,t){var n=F.p;try{return F.p=e,t()}finally{F.p=n}}(e.priority,function(){if(13===n.tag){var e=zu();e=Pe(e);var t=Lr(n,e);null!==t&&Mu(t,0,e),nf(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function jf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=uf(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&rf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Ot=r,n.target.dispatchEvent(r),Ot=null,t.shift()}return!0}function Nf(e,t,n){jf(e)&&n.delete(t)}function Ef(){mf=!1,null!==pf&&jf(pf)&&(pf=null),null!==hf&&jf(hf)&&(hf=null),null!==vf&&jf(vf)&&(vf=null),gf.forEach(Nf),bf.forEach(Nf)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,mf||(mf=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,Ef)))}var Pf=null;function Tf(e){Pf!==e&&(Pf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Pf===e&&(Pf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!==typeof r){if(null===df(r||n))continue;break}var l=He(n);null!==l&&(e.splice(t,3),t-=3,Li(l,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Of(e){function t(t){return Cf(t,e)}null!==pf&&Cf(pf,e),null!==hf&&Cf(hf,e),null!==vf&&Cf(vf,e),gf.forEach(t),bf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)Sf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],l=n[r+1],i=a[_e]||null;if("function"===typeof l)i||Tf(n);else if(i){var o=null;if(l&&l.hasAttribute("formAction")){if(a=l,i=l[_e]||null)o=i.formAction;else if(null!==df(a))continue}else o=i.action;"function"===typeof o?n[r+1]=o:(n.splice(r,3),r-=3),Tf(n)}}}function Lf(e){this._internalRoot=e}function Rf(e){this._internalRoot=e}Rf.prototype.render=Lf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(i(409));ef(t.current,zu(),e,t,null,null)},Rf.prototype.unmount=Lf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;ef(e.current,2,null,e,null,null),Wu(),t[Ae]=null}},Rf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Oe();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&Sf(e)}};var _f=a.version;if("19.1.1"!==_f)throw Error(i(527,_f,"19.1.1"));F.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(i(188));throw e=Object.keys(e).join(","),Error(i(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(i(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return c(a),e;if(l===r)return c(a),t;l=l.sibling}throw Error(i(188))}if(n.return!==r.return)n=a,r=l;else{for(var o=!1,u=a.child;u;){if(u===n){o=!0,n=a,r=l;break}if(u===r){o=!0,r=a,n=l;break}u=u.sibling}if(!o){for(u=l.child;u;){if(u===n){o=!0,n=l,r=a;break}if(u===r){o=!0,r=l,n=a;break}u=u.sibling}if(!o)throw Error(i(189))}}if(n.alternate!==r)throw Error(i(190))}if(3!==n.tag)throw Error(i(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Af={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.1"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Ff=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ff.isDisabled&&Ff.supportsFiber)try{ce=Ff.inject(Af),de=Ff}catch(zf){}}t.createRoot=function(e,t){if(!o(e))throw Error(i(299));var n=!1,r="",a=yo,l=xo,s=wo;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(l=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Jd(e,1,!1,null,0,n,r,a,l,s,0,null),e[Ae]=t.current,Hc(e),new Lf(t)},t.hydrateRoot=function(e,t,n){if(!o(e))throw Error(i(299));var r=!1,a="",l=yo,s=xo,u=wo,c=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(l=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Jd(e,1,!0,t,0,r,a,l,s,u,0,c)).context=Zd(null),n=t.current,(a=al(r=Pe(r=zu()))).callback=null,ll(n,a,r),n=r,t.current.lanes=n,Ne(t,n),jc(t),e[Ae]=t.current,Hc(e),new Rf(t)},t.version="19.1.1"},43:(e,t,n)=>{e.exports=n(288)},237:(e,t,n)=>{e.exports=n(365)},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),m=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,v={};function g(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||p}function b(){}function y(e,t,n){this.props=e,this.context=t,this.refs=v,this.updater=n||p}g.prototype.isReactComponent={},g.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},g.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},b.prototype=g.prototype;var x=y.prototype=new b;x.constructor=y,h(x,g.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},S=Object.prototype.hasOwnProperty;function j(e,t,r,a,l,i){return r=i.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:i}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var E=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(){}function T(e,t,a,l,i){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(o){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return T((c=e._init)(e._payload),t,a,l,i)}}if(c)return i=i(e),c=""===l?"."+C(e,0):l,w(i)?(a="",null!=c&&(a=c.replace(E,"$&/")+"/"),T(i,t,a,"",function(e){return e})):null!=i&&(N(i)&&(s=i,u=a+(null==i.key||e&&e.key===i.key?"":(""+i.key).replace(E,"$&/")+"/")+c,i=j(s.type,u,void 0,0,0,s.props)),t.push(i)),1;c=0;var d,p=""===l?".":l+":";if(w(e))for(var h=0;h<e.length;h++)c+=T(l=e[h],t,a,o=p+C(l,h),i);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=m&&d[m]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(l=e.next()).done;)c+=T(l=l.value,t,a,o=p+C(l,h++),i);else if("object"===o){if("function"===typeof e.then)return T(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,l,i);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function O(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",function(e){return t.call(n,e,a++)}),r}function L(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function _(){}t.Children={map:O,forEach:function(e,t,n){O(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return O(e,function(){t++}),t},toArray:function(e){return O(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=g,t.Fragment=a,t.Profiler=i,t.PureComponent=y,t.StrictMode=l,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),a=e.key;if(null!=t)for(l in void 0!==t.ref&&void 0,void 0!==t.key&&(a=""+t.key),t)!S.call(t,l)||"key"===l||"__self"===l||"__source"===l||"ref"===l&&void 0===t.ref||(r[l]=t[l]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var i=Array(l),o=0;o<l;o++)i[o]=arguments[o+2];r.children=i}return j(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:o,_context:e},e},t.createElement=function(e,t,n){var r,a={},l=null;if(null!=t)for(r in void 0!==t.key&&(l=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var i=arguments.length-2;if(1===i)a.children=n;else if(1<i){for(var o=Array(i),s=0;s<i;s++)o[s]=arguments[s+2];a.children=o}if(e&&e.defaultProps)for(r in i=e.defaultProps)void 0===a[r]&&(a[r]=i[r]);return j(e,l,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:L}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),a=k.S;null!==a&&a(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(_,R)}catch(l){R(l)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=k.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.1.1"},365:(e,t,n)=>{var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},l=r.useSyncExternalStore,i=r.useRef,o=r.useEffect,s=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,c){var d=i(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=s(function(){function e(e){if(!o){if(o=!0,l=e,e=r(e),void 0!==c&&f.hasValue){var t=f.value;if(c(t,e))return i=t}return i=e}if(t=i,a(l,e))return t;var n=r(e);return void 0!==c&&c(t,n)?(l=e,t):(l=e,i=n)}var l,i,o=!1,s=void 0===n?null:n;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,n,r,c]);var m=l(e,d[0],d[1]);return o(function(){f.hasValue=!0,f.value=m},[m]),u(m),m}},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},579:(e,t,n)=>{e.exports=n(799)},672:(e,t,n)=>{var r=n(43);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function l(){}var i={d:{f:l,r:function(){throw Error(a(522))},D:l,C:l,L:l,m:l,X:l,S:l,M:l},p:0,findDOMNode:null},o=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=i,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=i.p;try{if(s.T=null,i.p=2,e)return e()}finally{s.T=t,i.p=n,i.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,i.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&i.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"===typeof t.integrity?t.integrity:void 0,l="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?i.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:l}):"script"===n&&i.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:l,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);i.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&i.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=u(n,t.crossOrigin);i.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=u(t.as,t.crossOrigin);i.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else i.d.m(e)},t.requestFormReset=function(e){i.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.1"},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function a(e,t,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==t.key&&(a=""+t.key),"key"in t)for(var l in r={},t)"key"!==l&&(r[l]=t[l]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:a,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=a,t.jsxs=a},853:(e,t,n)=>{e.exports=n(896)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var o=2*(r+1)-1,s=e[o],u=o+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[o]=n,r=o);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var o=Date,s=o.now();t.unstable_now=function(){return o.now()-s}}var u=[],c=[],d=1,f=null,m=3,p=!1,h=!1,v=!1,g=!1,b="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,x="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function k(e){if(v=!1,w(e),!h)if(null!==r(u))h=!0,j||(j=!0,S());else{var t=r(c);null!==t&&R(k,t.startTime-e)}}var S,j=!1,N=-1,E=5,C=-1;function P(){return!!g||!(t.unstable_now()-C<E)}function T(){if(g=!1,j){var e=t.unstable_now();C=e;var n=!0;try{e:{h=!1,v&&(v=!1,y(N),N=-1),p=!0;var l=m;try{t:{for(w(e),f=r(u);null!==f&&!(f.expirationTime>e&&P());){var i=f.callback;if("function"===typeof i){f.callback=null,m=f.priorityLevel;var o=i(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof o){f.callback=o,w(e),n=!0;break t}f===r(u)&&a(u),w(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&R(k,s.startTime-e),n=!1}}break e}finally{f=null,m=l,p=!1}n=void 0}}finally{n?S():j=!1}}}if("function"===typeof x)S=function(){x(T)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,L=O.port2;O.port1.onmessage=T,S=function(){L.postMessage(null)}}else S=function(){b(T,0)};function R(e,n){N=b(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_requestPaint=function(){g=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,a,l){var i=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?i+l:i:l=i,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:o=l+o,sortIndex:-1},l>i?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(v?(y(N),N=-1):v=!0,R(k,l-i))):(e.sortIndex=o,n(u,e),h||p||(h=!0,j||(j=!0,S()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r](l,l.exports,n),l.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var l=Object.create(null);n.r(l);var i={};e=e||[null,t({}),t([]),t(t)];for(var o=2&a&&r;("object"==typeof o||"function"==typeof o)&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach(e=>i[e]=()=>r[e]);return i.default=()=>r,n.d(l,i),l}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>Rn,hasStandardBrowserEnv:()=>An,hasStandardBrowserWebWorkerEnv:()=>Fn,navigator:()=>_n,origin:()=>zn});var a=n(43),l=n.t(a,2),i=n(391);function o(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)n=l[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function u(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=s(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function c(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach(function(t){c(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const m=["sri"],p=["page"],h=["page","matches"],v=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],g=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],b=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var y="popstate";function x(){return C(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return j("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:N(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function w(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function k(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(zo){}}}function S(e,t){return{usr:e.state,key:e.key,idx:t}}function j(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return f(f({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?E(t):t),{},{state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)})}function N(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function E(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function C(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:l=!1}=r,i=a.history,o="POP",s=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){o="POP";let e=c(),t=null==e?null:e-u;u=e,s&&s({action:o,location:p.location,delta:t})}function m(e){return P(e)}null==u&&(u=0,i.replaceState(f(f({},i.state),{},{idx:u}),""));let p={get action(){return o},get location(){return e(a,i)},listen(e){if(s)throw new Error("A history only accepts one active listener");return a.addEventListener(y,d),s=e,()=>{a.removeEventListener(y,d),s=null}},createHref:e=>t(a,e),createURL:m,encodeLocation(e){let t=m(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o="PUSH";let r=j(p.location,e,t);n&&n(r,e),u=c()+1;let d=S(r,u),f=p.createHref(r);try{i.pushState(d,"",f)}catch(m){if(m instanceof DOMException&&"DataCloneError"===m.name)throw m;a.location.assign(f)}l&&s&&s({action:o,location:p.location,delta:1})},replace:function(e,t){o="REPLACE";let r=j(p.location,e,t);n&&n(r,e),u=c();let a=S(r,u),d=p.createHref(r);i.replaceState(a,"",d),l&&s&&s({action:o,location:p.location,delta:0})},go:e=>i.go(e)};return p}function P(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),w(n,"No window.location.(origin|href) available to create URL");let r="string"===typeof e?e:N(e);return r=r.replace(/ $/,"%20"),!t&&r.startsWith("//")&&(r=n+r),new URL(r,n)}new WeakMap;function T(e,t){return O(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function O(e,t,n,r){let a=V(("string"===typeof t?E(t):t).pathname||"/",n);if(null==a)return null;let l=L(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(l);let i=null;for(let o=0;null==i&&o<l.length;++o){let e=q(a);i=B(l[o],e,r)}return i}function L(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=arguments.length>4&&void 0!==arguments[4]&&arguments[4],l=function(e,l){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:a,o=arguments.length>3?arguments[3]:void 0,s={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:l,route:e};if(s.relativePath.startsWith("/")){if(!s.relativePath.startsWith(r)&&i)return;w(s.relativePath.startsWith(r),'Absolute route path "'.concat(s.relativePath,'" nested under path "').concat(r,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),s.relativePath=s.relativePath.slice(r.length)}let u=G([r,s.relativePath]),c=n.concat(s);e.children&&e.children.length>0&&(w(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(u,'".')),L(e.children,t,c,u,i)),(null!=e.path||e.index)&&t.push({path:u,score:U(u,e.index),routesMeta:c})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let r of R(e.path))l(e,t,!0,r);else l(e,t)}),t}function R(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),l=n.replace(/\?$/,"");if(0===r.length)return a?[l,""]:[l];let i=R(r.join("/")),o=[];return o.push(...i.map(e=>""===e?l:[l,e].join("/"))),a&&o.push(...i),o.map(t=>e.startsWith("/")&&""===t?"/":t)}var _=/^:[\w-]+$/,A=3,F=2,z=1,D=10,M=-2,I=e=>"*"===e;function U(e,t){let n=e.split("/"),r=n.length;return n.some(I)&&(r+=M),t&&(r+=F),n.filter(e=>!I(e)).reduce((e,t)=>e+(_.test(t)?A:""===t?z:D),r)}function B(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},l="/",i=[];for(let o=0;o<r.length;++o){let e=r[o],s=o===r.length-1,u="/"===l?t:t.slice(l.length)||"/",c=H({path:e.relativePath,caseSensitive:e.caseSensitive,end:s},u),d=e.route;if(!c&&s&&n&&!r[r.length-1].route.index&&(c=H({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:G([l,c.pathname]),pathnameBase:X(G([l,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(l=G([l,c.pathnameBase]))}return i}function H(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=W(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:r.reduce((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=o[n]||"";i=l.slice(0,l.length-e.length).replace(/(.)\/+$/,"$1")}const s=o[n];return e[r]=a&&!s?void 0:(s||"").replace(/%2F/g,"/"),e},{}),pathname:l,pathnameBase:i,pattern:e}}function W(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];k("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")).replace(/\/([\w-]+)\?(\/|$)/g,"(/$1)?$2");return e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),r]}function q(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return k(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function V(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function K(e,t,n,r){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(r),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function $(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function Q(e){let t=$(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function Y(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=E(e):(r=f({},e),w(!r.pathname||!r.pathname.includes("?"),K("?","pathname","search",r)),w(!r.pathname||!r.pathname.includes("#"),K("#","pathname","hash",r)),w(!r.search||!r.search.includes("#"),K("#","search","hash",r)));let l,i=""===e||""===r.pathname,o=i?"/":r.pathname;if(null==o)l=n;else{let e=t.length-1;if(!a&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}l=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?E(e):e,l=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:l,search:J(r),hash:Z(a)}}(r,l),u=o&&"/"!==o&&o.endsWith("/"),c=(i||"."===o)&&n.endsWith("/");return s.pathname.endsWith("/")||!u&&!c||(s.pathname+="/"),s}var G=e=>e.join("/").replace(/\/\/+/g,"/"),X=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),J=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Z=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function ee(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var te=["POST","PUT","PATCH","DELETE"],ne=(new Set(te),["GET",...te]);new Set(ne),Symbol("ResetLoaderData");var re=a.createContext(null);re.displayName="DataRouter";var ae=a.createContext(null);ae.displayName="DataRouterState";var le=a.createContext(!1);var ie=a.createContext({isTransitioning:!1});ie.displayName="ViewTransition";var oe=a.createContext(new Map);oe.displayName="Fetchers";var se=a.createContext(null);se.displayName="Await";var ue=a.createContext(null);ue.displayName="Navigation";var ce=a.createContext(null);ce.displayName="Location";var de=a.createContext({outlet:null,matches:[],isDataRoute:!1});de.displayName="Route";var fe=a.createContext(null);fe.displayName="RouteError";function me(){return null!=a.useContext(ce)}function pe(){return w(me(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(ce).location}var he="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ve(e){a.useContext(ue).static||a.useLayoutEffect(e)}function ge(){let{isDataRoute:e}=a.useContext(de);return e?function(){let{router:e}=Ee("useNavigate"),t=Pe("useNavigate"),n=a.useRef(!1);ve(()=>{n.current=!0});let r=a.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};k(n.current,he),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,f({fromRouteId:t},a)))},[e,t]);return r}():function(){w(me(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(re),{basename:t,navigator:n}=a.useContext(ue),{matches:r}=a.useContext(de),{pathname:l}=pe(),i=JSON.stringify(Q(r)),o=a.useRef(!1);ve(()=>{o.current=!0});let s=a.useCallback(function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(k(o.current,he),!o.current)return;if("number"===typeof r)return void n.go(r);let s=Y(r,JSON.parse(i),l,"path"===a.relative);null==e&&"/"!==t&&(s.pathname="/"===s.pathname?t:G([t,s.pathname])),(a.replace?n.replace:n.push)(s,a.state,a)},[t,n,i,l,e]);return s}()}a.createContext(null);function be(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=a.useContext(de),{pathname:r}=pe(),l=JSON.stringify(Q(n));return a.useMemo(()=>Y(e,JSON.parse(l),r,"path"===t),[e,l,r,t])}function ye(e,t,n,r,l){w(me(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:i}=a.useContext(ue),{matches:o}=a.useContext(de),s=o[o.length-1],u=s?s.params:{},c=s?s.pathname:"/",d=s?s.pathnameBase:"/",m=s&&s.route;{let e=m&&m.path||"";Le(c,!m||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(c,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let p,h=pe();if(t){var v;let e="string"===typeof t?E(t):t;w("/"===d||(null===(v=e.pathname)||void 0===v?void 0:v.startsWith(d)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(d,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),p=e}else p=h;let g=p.pathname||"/",b=g;if("/"!==d){let e=d.replace(/^\//,"").split("/");b="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let y=T(e,{pathname:b});k(m||null!=y,'No routes matched location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" ')),k(null==y||void 0!==y[y.length-1].route.element||void 0!==y[y.length-1].route.Component||void 0!==y[y.length-1].route.lazy,'Matched leaf route at location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let x=je(y&&y.map(e=>Object.assign({},e,{params:Object.assign({},u,e.params),pathname:G([d,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?d:G([d,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),o,n,r,l);return t&&x?a.createElement(ce.Provider,{value:{location:f({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:"POP"}},x):x}function xe(){let e=Te(),t=ee(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",l={padding:"0.5rem",backgroundColor:r},i={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=a.createElement(a.Fragment,null,a.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:i},"ErrorBoundary")," or"," ",a.createElement("code",{style:i},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:l},n):null,o)}var we=a.createElement(xe,null),ke=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){this.props.unstable_onError?this.props.unstable_onError(e,t):console.error("React Router caught the following error during render",e)}render(){return void 0!==this.state.error?a.createElement(de.Provider,{value:this.props.routeContext},a.createElement(fe.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function Se(e){let{routeContext:t,match:n,children:r}=e,l=a.useContext(re);return l&&l.static&&l.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(de.Provider,{value:t},r)}function je(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let l=e,i=null===n||void 0===n?void 0:n.errors;if(null!=i){let e=l.findIndex(e=>e.route.id&&void 0!==(null===i||void 0===i?void 0:i[e.route.id]));w(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(i).join(","))),l=l.slice(0,Math.min(l.length,e+1))}let o=!1,s=-1;if(n)for(let a=0;a<l.length;a++){let e=l[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(s=a),e.route.id){let{loaderData:t,errors:r}=n,a=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!r||void 0===r[e.route.id]);if(e.route.lazy||a){o=!0,l=s>=0?l.slice(0,s+1):[l[0]];break}}}return l.reduceRight((e,u,c)=>{let d,f=!1,m=null,p=null;n&&(d=i&&u.route.id?i[u.route.id]:void 0,m=u.route.errorElement||we,o&&(s<0&&0===c?(Le("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),f=!0,p=null):s===c&&(f=!0,p=u.route.hydrateFallbackElement||null)));let h=t.concat(l.slice(0,c+1)),v=()=>{let t;return t=d?m:f?p:u.route.Component?a.createElement(u.route.Component,null):u.route.element?u.route.element:e,a.createElement(Se,{match:u,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(u.route.ErrorBoundary||u.route.errorElement||0===c)?a.createElement(ke,{location:n.location,revalidation:n.revalidation,component:m,error:d,children:v(),routeContext:{outlet:null,matches:h,isDataRoute:!0},unstable_onError:r}):v()},null)}function Ne(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Ee(e){let t=a.useContext(re);return w(t,Ne(e)),t}function Ce(e){let t=a.useContext(ae);return w(t,Ne(e)),t}function Pe(e){let t=function(e){let t=a.useContext(de);return w(t,Ne(e)),t}(e),n=t.matches[t.matches.length-1];return w(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Te(){var e;let t=a.useContext(fe),n=Ce("useRouteError"),r=Pe("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[r]}var Oe={};function Le(e,t,n){t||Oe[e]||(Oe[e]=!0,k(!1,n))}var Re={};function _e(e,t){e||Re[t]||(Re[t]=!0,console.warn(t))}a.memo(function(e){let{routes:t,future:n,state:r,unstable_onError:a}=e;return ye(t,void 0,r,a,n)});function Ae(e){let{to:t,replace:n,state:r,relative:l}=e;w(me(),"<Navigate> may be used only in the context of a <Router> component.");let{static:i}=a.useContext(ue);k(!i,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:o}=a.useContext(de),{pathname:s}=pe(),u=ge(),c=Y(t,Q(o),s,"path"===l),d=JSON.stringify(c);return a.useEffect(()=>{u(JSON.parse(d),{replace:n,state:r,relative:l})},[u,d,l,n,r]),null}function Fe(e){w(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ze(e){let{basename:t="/",children:n=null,location:r,navigationType:l="POP",navigator:i,static:o=!1}=e;w(!me(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let s=t.replace(/^\/*/,"/"),u=a.useMemo(()=>({basename:s,navigator:i,static:o,future:{}}),[s,i,o]);"string"===typeof r&&(r=E(r));let{pathname:c="/",search:d="",hash:f="",state:m=null,key:p="default"}=r,h=a.useMemo(()=>{let e=V(c,s);return null==e?null:{location:{pathname:e,search:d,hash:f,state:m,key:p},navigationType:l}},[s,c,d,f,m,p,l]);return k(null!=h,'<Router basename="'.concat(s,'"> is not able to match the URL "').concat(c).concat(d).concat(f,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==h?null:a.createElement(ue.Provider,{value:u},a.createElement(ce.Provider,{children:n,value:h}))}function De(e){let{children:t,location:n}=e;return ye(Me(t),n)}a.Component;function Me(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return a.Children.forEach(e,(e,r)=>{if(!a.isValidElement(e))return;let l=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,Me(e.props.children,l));w(e.type===Fe,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),w(!e.props.index||!e.props.children,"An index route cannot have child routes.");let i={id:e.props.id||l.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Me(e.props.children,l)),n.push(i)}),n}var Ie="get",Ue="application/x-www-form-urlencoded";function Be(e){return null!=e&&"string"===typeof e.tagName}var He=null;var We=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function qe(e){return null==e||We.has(e)?e:(k(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(Ue,'"')),null)}function Ve(e,t){let n,r,a,l,i;if(Be(o=e)&&"form"===o.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?V(i,t):null,n=e.getAttribute("method")||Ie,a=qe(e.getAttribute("enctype"))||Ue,l=new FormData(e)}else if(function(e){return Be(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Be(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let o=e.getAttribute("formaction")||i.getAttribute("action");if(r=o?V(o,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||Ie,a=qe(e.getAttribute("formenctype"))||qe(i.getAttribute("enctype"))||Ue,l=new FormData(i,e),!function(){if(null===He)try{new FormData(document.createElement("form"),0),He=!1}catch(zo){He=!0}return He}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?"".concat(t,"."):"";l.append("".concat(e,"x"),"0"),l.append("".concat(e,"y"),"0")}else t&&l.append(t,r)}}else{if(Be(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Ie,r=null,a=Ue,i=e}var o;return l&&"text/plain"===a&&(i=l,l=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:l,body:i}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;function Ke(e){return{__html:e}}function $e(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}Symbol("SingleFetchRedirect");function Qe(e,t,n){let r="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname="_root.".concat(n):t&&"/"===V(r.pathname,t)?r.pathname="".concat(t.replace(/\/$/,""),"/_root.").concat(n):r.pathname="".concat(r.pathname.replace(/\/$/,""),".").concat(n),r}async function Ye(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ge(e){return null!=e&&"string"===typeof e.page}function Xe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Je(e,t,n,r,a,l){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,o=(e,t)=>{var r;return n[t].pathname!==e.pathname||(null===(r=n[t].route.path)||void 0===r?void 0:r.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===l?t.filter((e,t)=>i(e,t)||o(e,t)):"data"===l?t.filter((t,l)=>{let s=r.routes[t.route.id];if(!s||!s.hasLoader)return!1;if(i(t,l)||o(t,l))return!0;if(t.route.shouldRevalidate){var u;let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:(null===(u=n[0])||void 0===u?void 0:u.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0}):[]}function Ze(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function et(e,t){let n=new Set,r=new Set(t);return e.reduce((e,a)=>{if(t&&!Ge(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let l=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(l)||(n.add(l),e.push({key:l,link:a})),e},[])}function tt(e,t){return"lazy"===e.mode&&!0===t}function nt(){let e=a.useContext(re);return $e(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function rt(){let e=a.useContext(ae);return $e(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var at=a.createContext(void 0);function lt(){let e=a.useContext(at);return $e(e,"You must render this element inside a <HydratedRouter> element"),e}function it(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function ot(e,t,n){if(n&&!dt)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}at.displayName="FrameworkContext";function st(e){let{page:t}=e,n=o(e,p),{router:r}=nt(),l=a.useMemo(()=>T(r.routes,t,r.basename),[r.routes,t,r.basename]);return l?a.createElement(ct,f({page:t,matches:l},n)):null}function ut(e){let{manifest:t,routeModules:n}=lt(),[r,l]=a.useState([]);return a.useEffect(()=>{let r=!1;return async function(e,t,n){return et((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await Ye(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(Xe).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?f(f({},e),{},{rel:"prefetch",as:"style"}):f(f({},e),{},{rel:"prefetch"})))}(e,t,n).then(e=>{r||l(e)}),()=>{r=!0}},[e,t,n]),r}function ct(e){let{page:t,matches:n}=e,r=o(e,h),l=pe(),{manifest:i,routeModules:s}=lt(),{basename:u}=nt(),{loaderData:c,matches:d}=rt(),m=a.useMemo(()=>Je(t,n,d,i,l,"data"),[t,n,d,i,l]),p=a.useMemo(()=>Je(t,n,d,i,l,"assets"),[t,n,d,i,l]),v=a.useMemo(()=>{if(t===l.pathname+l.search+l.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{var n;let a=i.routes[t.route.id];a&&a.hasLoader&&(!m.some(e=>e.route.id===t.route.id)&&t.route.id in c&&null!==(n=s[t.route.id])&&void 0!==n&&n.shouldRevalidate||a.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let a=Qe(t,u,"data");return r&&e.size>0&&a.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[a.pathname+a.search]},[u,c,l,i,m,n,t,s]),g=a.useMemo(()=>Ze(p,i),[p,i]),b=ut(p);return a.createElement(a.Fragment,null,v.map(e=>a.createElement("link",f({key:e,rel:"prefetch",as:"fetch",href:e},r))),g.map(e=>a.createElement("link",f({key:e,rel:"modulepreload",href:e},r))),b.map(e=>{let{key:t,link:n}=e;return a.createElement("link",f({key:t,nonce:r.nonce},n))}))}var dt=!1;function ft(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,renderMeta:l,routeDiscovery:i,ssr:s}=lt(),{router:u,static:c,staticContext:d}=nt(),{matches:p}=rt(),h=a.useContext(le),v=tt(i,s);l&&(l.didRenderScripts=!0);let g=ot(p,null,r);a.useEffect(()=>{dt=!0},[]);let b=a.useMemo(()=>{var r;if(h)return null;let l=d?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",i=c?"".concat(null!==(r=t.hmr)&&void 0!==r&&r.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(v?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(g.map((e,n)=>{let r="route".concat(n),a=t.routes[e.route.id];$e(a,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:l,clientLoaderModule:i,clientMiddlewareModule:o,hydrateFallbackModule:s,module:u}=a,c=[...l?[{module:l,varName:"".concat(r,"_clientAction")}]:[],...i?[{module:i,varName:"".concat(r,"_clientLoader")}]:[],...o?[{module:o,varName:"".concat(r,"_clientMiddleware")}]:[],...s?[{module:s,varName:"".concat(r,"_HydrateFallback")}]:[],{module:u,varName:"".concat(r,"_main")}];return 1===c.length?"import * as ".concat(r," from ").concat(JSON.stringify(u),";"):[c.map(e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";')).join("\n"),"const ".concat(r," = {").concat(c.map(e=>"...".concat(e.varName)).join(","),"};")].join("\n")}).join("\n"),"\n  ").concat(v?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,r=o(e,m),a=new Set(t.state.matches.map(e=>e.route.id)),l=t.state.location.pathname.split("/").filter(Boolean),i=["/"];for(l.pop();l.length>0;)i.push("/".concat(l.join("/"))),l.pop();i.forEach(e=>{let n=T(t.routes,e,t.basename);n&&n.forEach(e=>a.add(e.route.id))});let s=[...a].reduce((e,t)=>Object.assign(e,{[t]:r.routes[t]}),{});return f(f({},r),{},{routes:s,sri:!!n||void 0})}(t,u),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(g.map((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t)).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return a.createElement(a.Fragment,null,a.createElement("script",f(f({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(l),type:void 0})),a.createElement("script",f(f({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:Ke(i),type:"module",async:!0})))},[]),y=dt||h?[]:(x=t.entry.imports.concat(Ze(g,t,{includeHydrateFallback:!0})),[...new Set(x)]);var x;let w="object"===typeof t.sri?t.sri:{};return _e(!h,"The <Scripts /> element is a no-op when using RSC and can be safely removed."),dt||h?null:a.createElement(a.Fragment,null,"object"===typeof t.sri?a.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:w})}}):null,v?null:a.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:w[t.url],suppressHydrationWarning:!0}),a.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:w[t.entry.module],suppressHydrationWarning:!0}),y.map(t=>a.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:w[t],suppressHydrationWarning:!0})),b)}function mt(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}a.Component;function pt(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,l=a.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(ee(t))return a.createElement(ht,{title:"Unhandled Thrown Response!"},a.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),l);if(t instanceof Error)r=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);r=new Error(e)}return a.createElement(ht,{title:"Application Error!",isOutsideRemixApp:n},a.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),a.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),l)}function ht(e){var t;let{title:n,renderScripts:r,isOutsideRemixApp:l,children:i}=e,{routeModules:o}=lt();return null!==(t=o.root)&&void 0!==t&&t.Layout&&!l?i:a.createElement("html",{lang:"en"},a.createElement("head",null,a.createElement("meta",{charSet:"utf-8"}),a.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),a.createElement("title",null,n)),a.createElement("body",null,a.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},i,r?a.createElement(ft,null):null)))}var vt="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{vt&&(window.__reactRouterVersion="7.8.2")}catch(zo){}function gt(e){let{basename:t,children:n,window:r}=e,l=a.useRef();null==l.current&&(l.current=x({window:r,v5Compat:!0}));let i=l.current,[o,s]=a.useState({action:i.action,location:i.location}),u=a.useCallback(e=>{a.startTransition(()=>s(e))},[s]);return a.useLayoutEffect(()=>i.listen(u),[i,u]),a.createElement(ze,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:i})}var bt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,yt=a.forwardRef(function(e,t){let n,{onClick:r,discover:l="render",prefetch:i="none",relative:s,reloadDocument:u,replace:c,state:d,target:m,to:p,preventScrollReset:h,viewTransition:g}=e,b=o(e,v),{basename:y}=a.useContext(ue),x="string"===typeof p&&bt.test(p),S=!1;if("string"===typeof p&&x&&(n=p,vt))try{let e=new URL(window.location.href),t=p.startsWith("//")?new URL(e.protocol+p):new URL(p),n=V(t.pathname,y);t.origin===e.origin&&null!=n?p=n+t.search+t.hash:S=!0}catch(zo){k(!1,'<Link to="'.concat(p,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let j=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};w(me(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=a.useContext(ue),{hash:l,pathname:i,search:o}=be(e,{relative:t}),s=i;return"/"!==n&&(s="/"===i?n:G([n,i])),r.createHref({pathname:s,search:o,hash:l})}(p,{relative:s}),[E,C,P]=function(e,t){let n=a.useContext(at),[r,l]=a.useState(!1),[i,o]=a.useState(!1),{onFocus:s,onBlur:u,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,m=a.useRef(null);a.useEffect(()=>{if("render"===e&&o(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{o(e.isIntersecting)})},{threshold:.5});return m.current&&e.observe(m.current),()=>{e.disconnect()}}},[e]),a.useEffect(()=>{if(r){let e=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(e)}}},[r]);let p=()=>{l(!0)},h=()=>{l(!1),o(!1)};return n?"intent"!==e?[i,m,{}]:[i,m,{onFocus:it(s,p),onBlur:it(u,h),onMouseEnter:it(c,p),onMouseLeave:it(d,h),onTouchStart:it(f,p)}]:[!1,m,{}]}(i,b),T=function(e){let{target:t,replace:n,state:r,preventScrollReset:l,relative:i,viewTransition:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=ge(),u=pe(),c=be(e,{relative:i});return a.useCallback(a=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(a,t)){a.preventDefault();let t=void 0!==n?n:N(u)===N(c);s(e,{replace:t,state:r,preventScrollReset:l,relative:i,viewTransition:o})}},[u,s,c,n,r,t,e,l,i,o])}(p,{replace:c,state:d,target:m,preventScrollReset:h,relative:s,viewTransition:g});let O=a.createElement("a",f(f(f({},b),P),{},{href:n||j,onClick:S||u?r:function(e){r&&r(e),e.defaultPrevented||T(e)},ref:mt(t,C),target:m,"data-discover":x||"render"!==l?void 0:"true"}));return E&&!x?a.createElement(a.Fragment,null,O,a.createElement(st,{page:j})):O});yt.displayName="Link",a.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:l="",end:i=!1,style:s,to:u,viewTransition:c,children:d}=e,m=o(e,g),p=be(u,{relative:m.relative}),h=pe(),v=a.useContext(ae),{navigator:b,basename:y}=a.useContext(ue),x=null!=v&&function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=a.useContext(ie);w(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=kt("useViewTransitionState"),l=be(e,{relative:t});if(!n.isTransitioning)return!1;let i=V(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=V(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=H(l.pathname,o)||null!=H(l.pathname,i)}(p)&&!0===c,k=b.encodeLocation?b.encodeLocation(p).pathname:p.pathname,S=h.pathname,j=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;r||(S=S.toLowerCase(),j=j?j.toLowerCase():null,k=k.toLowerCase()),j&&y&&(j=V(j,y)||j);const N="/"!==k&&k.endsWith("/")?k.length-1:k.length;let E,C=S===k||!i&&S.startsWith(k)&&"/"===S.charAt(N),P=null!=j&&(j===k||!i&&j.startsWith(k)&&"/"===j.charAt(k.length)),T={isActive:C,isPending:P,isTransitioning:x},O=C?n:void 0;E="function"===typeof l?l(T):[l,C?"active":null,P?"pending":null,x?"transitioning":null].filter(Boolean).join(" ");let L="function"===typeof s?s(T):s;return a.createElement(yt,f(f({},m),{},{"aria-current":O,className:E,ref:t,style:L,to:u,viewTransition:c}),"function"===typeof d?d(T):d)}).displayName="NavLink";var xt=a.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:l,reloadDocument:i,replace:s,state:u,method:c=Ie,action:d,onSubmit:m,relative:p,preventScrollReset:h,viewTransition:v}=e,g=o(e,b),y=Nt(),x=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=a.useContext(ue),r=a.useContext(de);w(r,"useFormAction must be used inside a RouteContext");let[l]=r.matches.slice(-1),i=f({},be(e||".",{relative:t})),o=pe();if(null==e){i.search=o.search;let e=new URLSearchParams(i.search),t=e.getAll("index"),n=t.some(e=>""===e);if(n){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();i.search=n?"?".concat(n):""}}e&&"."!==e||!l.route.index||(i.search=i.search?i.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(i.pathname="/"===i.pathname?n:G([n,i.pathname]));return N(i)}(d,{relative:p}),k="get"===c.toLowerCase()?"get":"post",S="string"===typeof d&&bt.test(d);return a.createElement("form",f(f({ref:t,method:k,action:x,onSubmit:i?m:e=>{if(m&&m(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||c;y(t||e.currentTarget,{fetcherKey:r,method:n,navigate:l,replace:s,state:u,relative:p,preventScrollReset:h,viewTransition:v})}},g),{},{"data-discover":S||"render"!==n?void 0:"true"}))});function wt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function kt(e){let t=a.useContext(re);return w(t,wt(e)),t}xt.displayName="Form";var St=0,jt=()=>"__".concat(String(++St),"__");function Nt(){let{router:e}=kt("useSubmit"),{basename:t}=a.useContext(ue),n=Pe("useRouteId");return a.useCallback(async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:l,method:i,encType:o,formData:s,body:u}=Ve(r,t);if(!1===a.navigate){let t=a.fetcherKey||jt();await e.fetch(t,n,a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||o,flushSync:a.flushSync})}else await e.navigate(a.action||l,{preventScrollReset:a.preventScrollReset,formData:s,body:u,formMethod:a.method||i,formEncType:a.encType||o,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})},[e,t,n])}function Et(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ct}=Object.prototype,{getPrototypeOf:Pt}=Object,{iterator:Tt,toStringTag:Ot}=Symbol,Lt=(Rt=Object.create(null),e=>{const t=Ct.call(e);return Rt[t]||(Rt[t]=t.slice(8,-1).toLowerCase())});var Rt;const _t=e=>(e=e.toLowerCase(),t=>Lt(t)===e),At=e=>t=>typeof t===e,{isArray:Ft}=Array,zt=At("undefined");function Dt(e){return null!==e&&!zt(e)&&null!==e.constructor&&!zt(e.constructor)&&Ut(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Mt=_t("ArrayBuffer");const It=At("string"),Ut=At("function"),Bt=At("number"),Ht=e=>null!==e&&"object"===typeof e,Wt=e=>{if("object"!==Lt(e))return!1;const t=Pt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Ot in e)&&!(Tt in e)},qt=_t("Date"),Vt=_t("File"),Kt=_t("Blob"),$t=_t("FileList"),Qt=_t("URLSearchParams"),[Yt,Gt,Xt,Jt]=["ReadableStream","Request","Response","Headers"].map(_t);function Zt(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),Ft(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{if(Dt(e))return;const r=a?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let i;for(n=0;n<l;n++)i=r[n],t.call(null,e[i],i,e)}}function en(e,t){if(Dt(e))return null;t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const tn="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,nn=e=>!zt(e)&&e!==tn;const rn=(an="undefined"!==typeof Uint8Array&&Pt(Uint8Array),e=>an&&e instanceof an);var an;const ln=_t("HTMLFormElement"),on=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),sn=_t("RegExp"),un=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Zt(n,(n,a)=>{let l;!1!==(l=t(n,a,e))&&(r[a]=l||n)}),Object.defineProperties(e,r)};const cn=_t("AsyncFunction"),dn=((e,t)=>{return e?setImmediate:t?(n="axios@".concat(Math.random()),r=[],tn.addEventListener("message",e=>{let{source:t,data:a}=e;t===tn&&a===n&&r.length&&r.shift()()},!1),e=>{r.push(e),tn.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Ut(tn.postMessage)),fn="undefined"!==typeof queueMicrotask?queueMicrotask.bind(tn):"undefined"!==typeof process&&process.nextTick||dn,mn={isArray:Ft,isArrayBuffer:Mt,isBuffer:Dt,isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Ut(e.append)&&("formdata"===(t=Lt(e))||"object"===t&&Ut(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Mt(e.buffer),t},isString:It,isNumber:Bt,isBoolean:e=>!0===e||!1===e,isObject:Ht,isPlainObject:Wt,isEmptyObject:e=>{if(!Ht(e)||Dt(e))return!1;try{return 0===Object.keys(e).length&&Object.getPrototypeOf(e)===Object.prototype}catch(zo){return!1}},isReadableStream:Yt,isRequest:Gt,isResponse:Xt,isHeaders:Jt,isUndefined:zt,isDate:qt,isFile:Vt,isBlob:Kt,isRegExp:sn,isFunction:Ut,isStream:e=>Ht(e)&&Ut(e.pipe),isURLSearchParams:Qt,isTypedArray:rn,isFileList:$t,forEach:Zt,merge:function e(){const{caseless:t}=nn(this)&&this||{},n={},r=(r,a)=>{const l=t&&en(n,a)||a;Wt(n[l])&&Wt(r)?n[l]=e(n[l],r):Wt(r)?n[l]=e({},r):Ft(r)?n[l]=r.slice():n[l]=r};for(let a=0,l=arguments.length;a<l;a++)arguments[a]&&Zt(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Zt(t,(t,r)=>{n&&Ut(t)?e[r]=Et(t,n):e[r]=t},{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,l,i;const o={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),l=a.length;l-- >0;)i=a[l],r&&!r(i,e,t)||o[i]||(t[i]=e[i],o[i]=!0);e=!1!==n&&Pt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Lt,kindOfTest:_t,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(Ft(e))return e;let t=e.length;if(!Bt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Tt]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:ln,hasOwnProperty:on,hasOwnProp:on,reduceDescriptors:un,freezeMethods:e=>{un(e,(t,n)=>{if(Ut(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Ut(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))})},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach(e=>{n[e]=!0})};return Ft(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,n){return t.toUpperCase()+n}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:en,global:tn,isContextDefined:nn,isSpecCompliantForm:function(e){return!!(e&&Ut(e.append)&&"FormData"===e[Ot]&&e[Tt])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Ht(e)){if(t.indexOf(e)>=0)return;if(Dt(e))return e;if(!("toJSON"in e)){t[r]=e;const a=Ft(e)?[]:{};return Zt(e,(e,t)=>{const l=n(e,r+1);!zt(l)&&(a[t]=l)}),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:cn,isThenable:e=>e&&(Ht(e)||Ut(e))&&Ut(e.then)&&Ut(e.catch),setImmediate:dn,asap:fn,isIterable:e=>null!=e&&Ut(e[Tt])};function pn(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}mn.inherits(pn,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:mn.toJSONObject(this.config),code:this.code,status:this.status}}});const hn=pn.prototype,vn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{vn[e]={value:e}}),Object.defineProperties(pn,vn),Object.defineProperty(hn,"isAxiosError",{value:!0}),pn.from=(e,t,n,r,a,l)=>{const i=Object.create(hn);return mn.toFlatObject(e,i,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),pn.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,l&&Object.assign(i,l),i};const gn=pn;function bn(e){return mn.isPlainObject(e)||mn.isArray(e)}function yn(e){return mn.endsWith(e,"[]")?e.slice(0,-2):e}function xn(e,t,n){return e?e.concat(t).map(function(e,t){return e=yn(e),!n&&t?"["+e+"]":e}).join(n?".":""):t}const wn=mn.toFlatObject(mn,{},null,function(e){return/^is[A-Z]/.test(e)});const kn=function(e,t,n){if(!mn.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=mn.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!mn.isUndefined(t[e])})).metaTokens,a=n.visitor||u,l=n.dots,i=n.indexes,o=(n.Blob||"undefined"!==typeof Blob&&Blob)&&mn.isSpecCompliantForm(t);if(!mn.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(mn.isDate(e))return e.toISOString();if(mn.isBoolean(e))return e.toString();if(!o&&mn.isBlob(e))throw new gn("Blob is not supported. Use a Buffer instead.");return mn.isArrayBuffer(e)||mn.isTypedArray(e)?o&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let o=e;if(e&&!a&&"object"===typeof e)if(mn.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(mn.isArray(e)&&function(e){return mn.isArray(e)&&!e.some(bn)}(e)||(mn.isFileList(e)||mn.endsWith(n,"[]"))&&(o=mn.toArray(e)))return n=yn(n),o.forEach(function(e,r){!mn.isUndefined(e)&&null!==e&&t.append(!0===i?xn([n],r,l):null===i?n:n+"[]",s(e))}),!1;return!!bn(e)||(t.append(xn(a,n,l),s(e)),!1)}const c=[],d=Object.assign(wn,{defaultVisitor:u,convertValue:s,isVisitable:bn});if(!mn.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!mn.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),mn.forEach(n,function(n,l){!0===(!(mn.isUndefined(n)||null===n)&&a.call(t,n,mn.isString(l)?l.trim():l,r,d))&&e(n,r?r.concat(l):[l])}),c.pop()}}(e),t};function Sn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function jn(e,t){this._pairs=[],e&&kn(e,this,t)}const Nn=jn.prototype;Nn.append=function(e,t){this._pairs.push([e,t])},Nn.toString=function(e){const t=e?function(t){return e.call(this,t,Sn)}:Sn;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};const En=jn;function Cn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Pn(e,t,n){if(!t)return e;const r=n&&n.encode||Cn;mn.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let l;if(l=a?a(t,n):mn.isURLSearchParams(t)?t.toString():new En(t,n).toString(r),l){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+l}return e}const Tn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){mn.forEach(this.handlers,function(t){null!==t&&e(t)})}},On={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ln={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:En,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Rn="undefined"!==typeof window&&"undefined"!==typeof document,_n="object"===typeof navigator&&navigator||void 0,An=Rn&&(!_n||["ReactNative","NativeScript","NS"].indexOf(_n.product)<0),Fn="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,zn=Rn&&window.location.href||"http://localhost",Dn=f(f({},r),Ln);const Mn=function(e){function t(e,n,r,a){let l=e[a++];if("__proto__"===l)return!0;const i=Number.isFinite(+l),o=a>=e.length;if(l=!l&&mn.isArray(r)?r.length:l,o)return mn.hasOwnProp(r,l)?r[l]=[r[l],n]:r[l]=n,!i;r[l]&&mn.isObject(r[l])||(r[l]=[]);return t(e,n,r[l],a)&&mn.isArray(r[l])&&(r[l]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let l;for(r=0;r<a;r++)l=n[r],t[l]=e[l];return t}(r[l])),!i}if(mn.isFormData(e)&&mn.isFunction(e.entries)){const n={};return mn.forEachEntry(e,(e,r)=>{t(function(e){return mn.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0])}(e),r,n,0)}),n}return null};const In={transitional:On,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=mn.isObject(e);a&&mn.isHTMLForm(e)&&(e=new FormData(e));if(mn.isFormData(e))return r?JSON.stringify(Mn(e)):e;if(mn.isArrayBuffer(e)||mn.isBuffer(e)||mn.isStream(e)||mn.isFile(e)||mn.isBlob(e)||mn.isReadableStream(e))return e;if(mn.isArrayBufferView(e))return e.buffer;if(mn.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let l;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return kn(e,new Dn.classes.URLSearchParams,f({visitor:function(e,t,n,r){return Dn.isNode&&mn.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((l=mn.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return kn(l?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(mn.isString(e))try{return(t||JSON.parse)(e),mn.trim(e)}catch(zo){if("SyntaxError"!==zo.name)throw zo}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||In.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(mn.isResponse(e)||mn.isReadableStream(e))return e;if(e&&mn.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(zo){if(n){if("SyntaxError"===zo.name)throw gn.from(zo,gn.ERR_BAD_RESPONSE,this,null,this.response);throw zo}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Dn.classes.FormData,Blob:Dn.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};mn.forEach(["delete","get","head","post","put","patch"],e=>{In.headers[e]={}});const Un=In,Bn=mn.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Hn=Symbol("internals");function Wn(e){return e&&String(e).trim().toLowerCase()}function qn(e){return!1===e||null==e?e:mn.isArray(e)?e.map(qn):String(e)}function Vn(e,t,n,r,a){return mn.isFunction(r)?r.call(this,t,n):(a&&(t=n),mn.isString(t)?mn.isString(r)?-1!==t.indexOf(r):mn.isRegExp(r)?r.test(t):void 0:void 0)}class Kn{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Wn(t);if(!a)throw new Error("header name must be a non-empty string");const l=mn.findKey(r,a);(!l||void 0===r[l]||!0===n||void 0===n&&!1!==r[l])&&(r[l||t]=qn(e))}const l=(e,t)=>mn.forEach(e,(e,n)=>a(e,n,t));if(mn.isPlainObject(e)||e instanceof this.constructor)l(e,t);else if(mn.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))l((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach(function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Bn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t})(e),t);else if(mn.isObject(e)&&mn.isIterable(e)){let n,r,a={};for(const t of e){if(!mn.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?mn.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}l(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Wn(e)){const n=mn.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(mn.isFunction(t))return t.call(this,e,n);if(mn.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Wn(e)){const n=mn.findKey(this,e);return!(!n||void 0===this[n]||t&&!Vn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Wn(e)){const a=mn.findKey(n,e);!a||t&&!Vn(0,n[a],a,t)||(delete n[a],r=!0)}}return mn.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Vn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return mn.forEach(this,(r,a)=>{const l=mn.findKey(n,a);if(l)return t[l]=qn(r),void delete t[a];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,n)=>t.toUpperCase()+n)}(a):String(a).trim();i!==a&&delete t[a],t[i]=qn(r),n[i]=!0}),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return mn.forEach(this,(n,r)=>{null!=n&&!1!==n&&(t[r]=e&&mn.isArray(n)?n.join(", "):n)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(e=>{let[t,n]=e;return t+": "+n}).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach(e=>t.set(e)),t}static accessor(e){const t=(this[Hn]=this[Hn]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Wn(e);t[r]||(!function(e,t){const n=mn.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})})}(n,e),t[r]=!0)}return mn.isArray(e)?e.forEach(r):r(e),this}}Kn.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),mn.reduceDescriptors(Kn.prototype,(e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}}),mn.freezeMethods(Kn);const $n=Kn;function Qn(e,t){const n=this||Un,r=t||n,a=$n.from(r.headers);let l=r.data;return mn.forEach(e,function(e){l=e.call(n,l,a.normalize(),t?t.status:void 0)}),a.normalize(),l}function Yn(e){return!(!e||!e.__CANCEL__)}function Gn(e,t,n){gn.call(this,null==e?"canceled":e,gn.ERR_CANCELED,t,n),this.name="CanceledError"}mn.inherits(Gn,gn,{__CANCEL__:!0});const Xn=Gn;function Jn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new gn("Request failed with status code "+n.status,[gn.ERR_BAD_REQUEST,gn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Zn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,l=0,i=0;return t=void 0!==t?t:1e3,function(o){const s=Date.now(),u=r[i];a||(a=s),n[l]=o,r[l]=s;let c=i,d=0;for(;c!==l;)d+=n[c++],c%=e;if(l=(l+1)%e,l===i&&(i=(i+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}};const er=function(e,t){let n,r,a=0,l=1e3/t;const i=function(t){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=l,n=null,r&&(clearTimeout(r),r=null),e(...t)};return[function(){const e=Date.now(),t=e-a;for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];t>=l?i(s,e):(n=s,r||(r=setTimeout(()=>{r=null,i(n)},l-t)))},()=>n&&i(n)]},tr=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=Zn(50,250);return er(n=>{const l=n.loaded,i=n.lengthComputable?n.total:void 0,o=l-r,s=a(o);r=l;e({loaded:l,total:i,progress:i?l/i:void 0,bytes:o,rate:s||void 0,estimated:s&&i&&l<=i?(i-l)/s:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})},n)},nr=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},rr=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return mn.asap(()=>e(...n))},ar=Dn.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Dn.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Dn.origin),Dn.navigator&&/(msie|trident)/i.test(Dn.navigator.userAgent)):()=>!0,lr=Dn.hasStandardBrowserEnv?{write(e,t,n,r,a,l){const i=[e+"="+encodeURIComponent(t)];mn.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),mn.isString(r)&&i.push("path="+r),mn.isString(a)&&i.push("domain="+a),!0===l&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function ir(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const or=e=>e instanceof $n?f({},e):e;function sr(e,t){t=t||{};const n={};function r(e,t,n,r){return mn.isPlainObject(e)&&mn.isPlainObject(t)?mn.merge.call({caseless:r},e,t):mn.isPlainObject(t)?mn.merge({},t):mn.isArray(t)?t.slice():t}function a(e,t,n,a){return mn.isUndefined(t)?mn.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function l(e,t){if(!mn.isUndefined(t))return r(void 0,t)}function i(e,t){return mn.isUndefined(t)?mn.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function o(n,a,l){return l in t?r(n,a):l in e?r(void 0,n):void 0}const s={url:l,method:l,data:l,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:o,headers:(e,t,n)=>a(or(e),or(t),0,!0)};return mn.forEach(Object.keys(f(f({},e),t)),function(r){const l=s[r]||a,i=l(e[r],t[r],r);mn.isUndefined(i)&&l!==o||(n[r]=i)}),n}const ur=e=>{const t=sr({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:l,xsrfCookieName:i,headers:o,auth:s}=t;if(t.headers=o=$n.from(o),t.url=Pn(ir(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&o.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),mn.isFormData(r))if(Dn.hasStandardBrowserEnv||Dn.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if(!1!==(n=o.getContentType())){const[e,...t]=n?n.split(";").map(e=>e.trim()).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...t].join("; "))}if(Dn.hasStandardBrowserEnv&&(a&&mn.isFunction(a)&&(a=a(t)),a||!1!==a&&ar(t.url))){const e=l&&i&&lr.read(i);e&&o.set(l,e)}return t},cr="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise(function(t,n){const r=ur(e);let a=r.data;const l=$n.from(r.headers).normalize();let i,o,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:m}=r;function p(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let h=new XMLHttpRequest;function v(){if(!h)return;const r=$n.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());Jn(function(e){t(e),p()},function(e){n(e),p()},{data:d&&"text"!==d&&"json"!==d?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:r,config:e,request:h}),h=null}h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout,"onloadend"in h?h.onloadend=v:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(v)},h.onabort=function(){h&&(n(new gn("Request aborted",gn.ECONNABORTED,e,h)),h=null)},h.onerror=function(){n(new gn("Network Error",gn.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||On;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new gn(t,a.clarifyTimeoutError?gn.ETIMEDOUT:gn.ECONNABORTED,e,h)),h=null},void 0===a&&l.setContentType(null),"setRequestHeader"in h&&mn.forEach(l.toJSON(),function(e,t){h.setRequestHeader(t,e)}),mn.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),d&&"json"!==d&&(h.responseType=r.responseType),m&&([s,c]=tr(m,!0),h.addEventListener("progress",s)),f&&h.upload&&([o,u]=tr(f),h.upload.addEventListener("progress",o),h.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{h&&(n(!t||t.type?new Xn(null,e,h):t),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const g=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);g&&-1===Dn.protocols.indexOf(g)?n(new gn("Unsupported protocol "+g+":",gn.ERR_BAD_REQUEST,e)):h.send(a||null)})},dr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof gn?t:new Xn(t instanceof Error?t.message:t))}};let l=t&&setTimeout(()=>{l=null,a(new gn("timeout ".concat(t," of ms exceeded"),gn.ETIMEDOUT))},t);const i=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)}),e=null)};e.forEach(e=>e.addEventListener("abort",a));const{signal:o}=r;return o.unsubscribe=()=>mn.asap(i),o}};function fr(e,t){this.v=e,this.k=t}function mr(e){return function(){return new pr(e.apply(this,arguments))}}function pr(e){var t,n;function r(t,n){try{var l=e[t](n),i=l.value,o=i instanceof fr;Promise.resolve(o?i.v:i).then(function(n){if(o){var s="return"===t?"return":"next";if(!i.k||n.done)return r(s,n);n=e[s](n).value}a(l.done?"return":"normal",n)},function(e){r("throw",e)})}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise(function(l,i){var o={key:e,arg:a,resolve:l,reject:i,next:null};n?n=n.next=o:(t=n=o,r(e,a))})},"function"!=typeof e.return&&(this.return=void 0)}function hr(e){return new fr(e,0)}function vr(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise(function(n){n(e[t](r))}),{done:!1,value:new fr(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function gr(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new br(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function br(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return br=function(e){this.s=e,this.n=e.next},br.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new br(e)}pr.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},pr.prototype.next=function(e){return this._invoke("next",e)},pr.prototype.throw=function(e){return this._invoke("throw",e)},pr.prototype.return=function(e){return this._invoke("return",e)};const yr=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},xr=function(){var e=mr(function*(e,t){var n,r=!1,a=!1;try{for(var l,i=gr(wr(e));r=!(l=yield hr(i.next())).done;r=!1){const e=l.value;yield*vr(gr(yr(e,t)))}}catch(o){a=!0,n=o}finally{try{r&&null!=i.return&&(yield hr(i.return()))}finally{if(a)throw n}}});return function(t,n){return e.apply(this,arguments)}}(),wr=function(){var e=mr(function*(e){if(e[Symbol.asyncIterator])return void(yield*vr(gr(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield hr(t.read());if(e)break;yield n}}finally{yield hr(t.cancel())}});return function(t){return e.apply(this,arguments)}}(),kr=(e,t,n,r)=>{const a=xr(e,t);let l,i=0,o=e=>{l||(l=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return o(),void e.close();let l=r.byteLength;if(n){let e=i+=l;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw o(t),t}},cancel:e=>(o(e),a.return())},{highWaterMark:2})},Sr="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,jr=Sr&&"function"===typeof ReadableStream,Nr=Sr&&("function"===typeof TextEncoder?(Er=new TextEncoder,e=>Er.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Er;const Cr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(zo){return!1}},Pr=jr&&Cr(()=>{let e=!1;const t=new Request(Dn.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Tr=jr&&Cr(()=>mn.isReadableStream(new Response("").body)),Or={stream:Tr&&(e=>e.body)};var Lr;Sr&&(Lr=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{!Or[e]&&(Or[e]=mn.isFunction(Lr[e])?t=>t[e]():(t,n)=>{throw new gn("Response type '".concat(e,"' is not supported"),gn.ERR_NOT_SUPPORT,n)})}));const Rr=async(e,t)=>{const n=mn.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(mn.isBlob(e))return e.size;if(mn.isSpecCompliantForm(e)){const t=new Request(Dn.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return mn.isArrayBufferView(e)||mn.isArrayBuffer(e)?e.byteLength:(mn.isURLSearchParams(e)&&(e+=""),mn.isString(e)?(await Nr(e)).byteLength:void 0)})(t):n},_r=Sr&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:l,timeout:i,onDownloadProgress:o,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:m}=ur(e);u=u?(u+"").toLowerCase():"text";let p,h=dr([a,l&&l.toAbortSignal()],i);const v=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(s&&Pr&&"get"!==n&&"head"!==n&&0!==(g=await Rr(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(mn.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=nr(g,tr(rr(s)));r=kr(n.body,65536,e,t)}}mn.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,f(f({},m),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let l=await fetch(p,m);const i=Tr&&("stream"===u||"response"===u);if(Tr&&(o||i&&v)){const e={};["status","statusText","headers"].forEach(t=>{e[t]=l[t]});const t=mn.toFiniteNumber(l.headers.get("content-length")),[n,r]=o&&nr(t,tr(rr(o),!0))||[];l=new Response(kr(l.body,65536,n,()=>{r&&r(),v&&v()}),e)}u=u||"text";let b=await Or[mn.findKey(Or,u)||"text"](l,e);return!i&&v&&v(),await new Promise((t,n)=>{Jn(t,n,{data:b,headers:$n.from(l.headers),status:l.status,statusText:l.statusText,config:e,request:p})})}catch(b){if(v&&v(),b&&"TypeError"===b.name&&/Load failed|fetch/i.test(b.message))throw Object.assign(new gn("Network Error",gn.ERR_NETWORK,e,p),{cause:b.cause||b});throw gn.from(b,b&&b.code,e,p)}}),Ar={http:null,xhr:cr,fetch:_r};mn.forEach(Ar,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(zo){}Object.defineProperty(e,"adapterName",{value:t})}});const Fr=e=>"- ".concat(e),zr=e=>mn.isFunction(e)||null===e||!1===e,Dr=e=>{e=mn.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let l=0;l<t;l++){let t;if(n=e[l],r=n,!zr(n)&&(r=Ar[(t=String(n)).toLowerCase()],void 0===r))throw new gn("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+l]=r}if(!r){const e=Object.entries(a).map(e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")});let n=t?e.length>1?"since :\n"+e.map(Fr).join("\n"):" "+Fr(e[0]):"as no adapter specified";throw new gn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Mr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Xn(null,e)}function Ir(e){Mr(e),e.headers=$n.from(e.headers),e.data=Qn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Dr(e.adapter||Un.adapter)(e).then(function(t){return Mr(e),t.data=Qn.call(e,e.transformResponse,t),t.headers=$n.from(t.headers),t},function(t){return Yn(t)||(Mr(e),t&&t.response&&(t.response.data=Qn.call(e,e.transformResponse,t.response),t.response.headers=$n.from(t.response.headers))),Promise.reject(t)})}const Ur="1.11.0",Br={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Br[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Hr={};Br.transitional=function(e,t,n){function r(e,t){return"[Axios v"+Ur+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,l)=>{if(!1===e)throw new gn(r(a," has been removed"+(t?" in "+t:"")),gn.ERR_DEPRECATED);return t&&!Hr[a]&&(Hr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,l)}},Br.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const Wr={assertOptions:function(e,t,n){if("object"!==typeof e)throw new gn("options must be an object",gn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const l=r[a],i=t[l];if(i){const t=e[l],n=void 0===t||i(t,l,e);if(!0!==n)throw new gn("option "+l+" must be "+n,gn.ERR_BAD_OPTION_VALUE);continue}if(!0!==n)throw new gn("Unknown option "+l,gn.ERR_BAD_OPTION)}},validators:Br},qr=Wr.validators;class Vr{constructor(e){this.defaults=e||{},this.interceptors={request:new Tn,response:new Tn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(zo){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=sr(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&Wr.assertOptions(n,{silentJSONParsing:qr.transitional(qr.boolean),forcedJSONParsing:qr.transitional(qr.boolean),clarifyTimeoutError:qr.transitional(qr.boolean)},!1),null!=r&&(mn.isFunction(r)?t.paramsSerializer={serialize:r}:Wr.assertOptions(r,{encode:qr.function,serialize:qr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),Wr.assertOptions(t,{baseUrl:qr.spelling("baseURL"),withXsrfToken:qr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let l=a&&mn.merge(a.common,a[t.method]);a&&mn.forEach(["delete","get","head","post","put","patch","common"],e=>{delete a[e]}),t.headers=$n.concat(l,a);const i=[];let o=!0;this.interceptors.request.forEach(function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(o=o&&e.synchronous,i.unshift(e.fulfilled,e.rejected))});const s=[];let u;this.interceptors.response.forEach(function(e){s.push(e.fulfilled,e.rejected)});let c,d=0;if(!o){const e=[Ir.bind(this),void 0];for(e.unshift(...i),e.push(...s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(m){t.call(this,m);break}}try{u=Ir.call(this,f)}catch(m){return Promise.reject(m)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return Pn(ir((e=sr(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}mn.forEach(["delete","get","head","options"],function(e){Vr.prototype[e]=function(t,n){return this.request(sr(n||{},{method:e,url:t,data:(n||{}).data}))}}),mn.forEach(["post","put","patch"],function(e){function t(t){return function(n,r,a){return this.request(sr(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Vr.prototype[e]=t(),Vr.prototype[e+"Form"]=t(!0)});const Kr=Vr;class $r{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise(function(e){t=e});const n=this;this.promise.then(e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null}),this.promise.then=e=>{let t;const r=new Promise(e=>{n.subscribe(e),t=e}).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e(function(e,r,a){n.reason||(n.reason=new Xn(e,r,a),t(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new $r(function(t){e=t});return{token:t,cancel:e}}}const Qr=$r;const Yr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Yr).forEach(e=>{let[t,n]=e;Yr[n]=t});const Gr=Yr;const Xr=function e(t){const n=new Kr(t),r=Et(Kr.prototype.request,n);return mn.extend(r,Kr.prototype,n,{allOwnKeys:!0}),mn.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(sr(t,n))},r}(Un);Xr.Axios=Kr,Xr.CanceledError=Xn,Xr.CancelToken=Qr,Xr.isCancel=Yn,Xr.VERSION=Ur,Xr.toFormData=kn,Xr.AxiosError=gn,Xr.Cancel=Xr.CanceledError,Xr.all=function(e){return Promise.all(e)},Xr.spread=function(e){return function(t){return e.apply(null,t)}},Xr.isAxiosError=function(e){return mn.isObject(e)&&!0===e.isAxiosError},Xr.mergeConfig=sr,Xr.AxiosHeaders=$n,Xr.formToJSON=e=>Mn(mn.isHTMLForm(e)?new FormData(e):e),Xr.getAdapter=Dr,Xr.HttpStatusCode=Gr,Xr.default=Xr;const Jr=Xr.create({baseURL:"api/v1",headers:{"Content-Type":"application/json"}});Jr.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e},e=>Promise.reject(e)),Jr.interceptors.response.use(e=>e,e=>{var t;return 401===(null===(t=e.response)||void 0===t?void 0:t.status)&&(localStorage.removeItem("token"),localStorage.removeItem("user"),window.location.href="/login"),Promise.reject(e)});const Zr=async e=>(await Jr.get(e)).data,ea=async(e,t)=>(await Jr.post(e,t)).data,ta=async(e,t)=>(await Jr.put(e,t)).data,na=async e=>(await Jr.delete(e)).data;var ra=n(579);const aa=(0,a.createContext)(void 0),la=()=>{const e=(0,a.useContext)(aa);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},ia=e=>{let{children:t}=e;const[n,r]=(0,a.useState)(null),[l,i]=(0,a.useState)(!0);(0,a.useEffect)(()=>{const e=localStorage.getItem("token"),t=localStorage.getItem("user");if(e&&t)try{r(JSON.parse(t))}catch(n){localStorage.removeItem("token"),localStorage.removeItem("user")}i(!1)},[]);const o={user:n,login:async(e,t)=>{try{const n=await ea("/auth/login",{username:e,password:t});localStorage.setItem("token",n.token),localStorage.setItem("user",JSON.stringify(n.user)),r(n.user)}catch(n){throw n}},logout:()=>{localStorage.removeItem("token"),localStorage.removeItem("user"),r(null)},isLoading:l};return(0,ra.jsx)(aa.Provider,{value:o,children:t})},oa=(0,a.createContext)(void 0),sa=e=>{let{children:t}=e;const[n,r]=(0,a.useState)("light");(0,a.useEffect)(()=>{const e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches,n=e||(t?"dark":"light");r(n),"dark"===n?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")},[]);const l={theme:n,toggleTheme:()=>{const e="light"===n?"dark":"light";r(e),localStorage.setItem("theme",e),"dark"===e?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark")}};return(0,ra.jsx)(oa.Provider,{value:l,children:t})},ua=["title","titleId"];function ca(e,t){let{title:n,titleId:r}=e,l=o(e,ua);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const da=a.forwardRef(ca),fa=["title","titleId"];function ma(e,t){let{title:n,titleId:r}=e,l=o(e,fa);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const pa=a.forwardRef(ma),ha=()=>{const[e,t]=(0,a.useState)(""),[n,r]=(0,a.useState)(""),[l,i]=(0,a.useState)(!1),[o,s]=(0,a.useState)(""),[u,c]=(0,a.useState)(!1),{user:d,login:f}=la();if(d)return(0,ra.jsx)(Ae,{to:"/",replace:!0});return(0,ra.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 py-12 px-4 sm:px-6 lg:px-8",children:[(0,ra.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,ra.jsx)("div",{className:"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-3xl"}),(0,ra.jsx)("div",{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full blur-3xl"})]}),(0,ra.jsxs)("div",{className:"relative max-w-md w-full",children:[(0,ra.jsxs)("div",{className:"text-center mb-8",children:[(0,ra.jsx)("div",{className:"mx-auto w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 shadow-2xl",children:(0,ra.jsx)("span",{className:"text-white font-bold text-2xl",children:"K"})}),(0,ra.jsx)("h2",{className:"text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent",children:"KPSS Plus"}),(0,ra.jsx)("p",{className:"mt-2 text-lg text-slate-300",children:"Admin Panel"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-slate-400",children:"Y\xf6netici paneline giri\u015f yap\u0131n"})]}),(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-8 shadow-2xl",children:(0,ra.jsxs)("form",{className:"space-y-6",onSubmit:async t=>{t.preventDefault(),s(""),c(!0);try{await f(e,n)}catch(l){var r,a;s((null===(r=l.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.error)||"Giri\u015f ba\u015far\u0131s\u0131z")}finally{c(!1)}},children:[(0,ra.jsxs)("div",{className:"space-y-4",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{htmlFor:"username",className:"block text-sm font-medium text-slate-300 mb-2",children:"Kullan\u0131c\u0131 Ad\u0131"}),(0,ra.jsx)("input",{id:"username",name:"username",type:"text",required:!0,className:"w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Kullan\u0131c\u0131 ad\u0131n\u0131z\u0131 girin",value:e,onChange:e=>t(e.target.value)})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-slate-300 mb-2",children:"\u015eifre"}),(0,ra.jsxs)("div",{className:"relative",children:[(0,ra.jsx)("input",{id:"password",name:"password",type:l?"text":"password",required:!0,className:"w-full px-4 py-3 pr-12 bg-slate-700/50 border border-slate-600 rounded-xl text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"\u015eifrenizi girin",value:n,onChange:e=>r(e.target.value)}),(0,ra.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-4 flex items-center text-slate-400 hover:text-slate-300 transition-colors duration-200",onClick:()=>i(!l),children:l?(0,ra.jsx)(da,{className:"h-5 w-5"}):(0,ra.jsx)(pa,{className:"h-5 w-5"})})]})]})]}),o&&(0,ra.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-xl p-4",children:(0,ra.jsx)("div",{className:"text-sm text-red-400",children:o})}),(0,ra.jsx)("button",{type:"submit",disabled:u,className:"w-full py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",children:u?(0,ra.jsxs)("div",{className:"flex items-center justify-center",children:[(0,ra.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Giri\u015f yap\u0131l\u0131yor..."]}):"Giri\u015f Yap"})]})}),(0,ra.jsx)("div",{className:"text-center mt-8",children:(0,ra.jsx)("p",{className:"text-xs text-slate-500",children:"\xa9 2024 KPSS Plus. T\xfcm haklar\u0131 sakl\u0131d\u0131r."})})]})]})},va=["title","titleId"];function ga(e,t){let{title:n,titleId:r}=e,l=o(e,va);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const ba=a.forwardRef(ga),ya=["title","titleId"];function xa(e,t){let{title:n,titleId:r}=e,l=o(e,ya);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.26 10.147a60.438 60.438 0 0 0-.491 6.347A48.62 48.62 0 0 1 12 20.904a48.62 48.62 0 0 1 8.232-4.41 60.46 60.46 0 0 0-.491-6.347m-15.482 0a50.636 50.636 0 0 0-2.658-.813A59.906 59.906 0 0 1 12 3.493a59.903 59.903 0 0 1 10.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.717 50.717 0 0 1 12 13.489a50.702 50.702 0 0 1 7.74-3.342M6.75 15a.75.75 0 1 0 0-1.5.75.75 0 0 0 0 1.5Zm0 0v-3.675A55.378 55.378 0 0 1 12 8.443m-7.007 11.55A5.981 5.981 0 0 0 6.75 15.75v-1.5"}))}const wa=a.forwardRef(xa),ka=["title","titleId"];function Sa(e,t){let{title:n,titleId:r}=e,l=o(e,ka);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z"}))}const ja=a.forwardRef(Sa),Na=["title","titleId"];function Ea(e,t){let{title:n,titleId:r}=e,l=o(e,Na);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const Ca=a.forwardRef(Ea),Pa=["title","titleId"];function Ta(e,t){let{title:n,titleId:r}=e,l=o(e,Pa);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z"}))}const Oa=a.forwardRef(Ta),La=["title","titleId"];function Ra(e,t){let{title:n,titleId:r}=e,l=o(e,La);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5.25 14.25h13.5m-13.5 0a3 3 0 0 1-3-3m3 3a3 3 0 1 0 0 6h13.5a3 3 0 1 0 0-6m-16.5-3a3 3 0 0 1 3-3h13.5a3 3 0 0 1 3 3m-19.5 0a4.5 4.5 0 0 1 .9-2.7L5.737 5.1a3.375 3.375 0 0 1 2.7-1.35h7.126c1.062 0 2.062.5 2.7 1.35l2.587 3.45a4.5 4.5 0 0 1 .9 2.7m0 0a3 3 0 0 1-3 3m0 3h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Zm-3 6h.008v.008h-.008v-.008Zm0-6h.008v.008h-.008v-.008Z"}))}const _a=a.forwardRef(Ra),Aa=["title","titleId"];function Fa(e,t){let{title:n,titleId:r}=e,l=o(e,Aa);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z"}))}const za=a.forwardRef(Fa),Da=["title","titleId"];function Ma(e,t){let{title:n,titleId:r}=e,l=o(e,Da);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const Ia=a.forwardRef(Ma),Ua=["title","titleId"];function Ba(e,t){let{title:n,titleId:r}=e,l=o(e,Ua);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}const Ha=a.forwardRef(Ba),Wa=["title","titleId"];function qa(e,t){let{title:n,titleId:r}=e,l=o(e,Wa);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Va=a.forwardRef(qa),Ka=["title","titleId"];function $a(e,t){let{title:n,titleId:r}=e,l=o(e,Ka);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z"}))}const Qa=a.forwardRef($a),Ya=["title","titleId"];function Ga(e,t){let{title:n,titleId:r}=e,l=o(e,Ya);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z"}))}const Xa=a.forwardRef(Ga),Ja=["title","titleId"];function Za(e,t){let{title:n,titleId:r}=e,l=o(e,Ja);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const el=a.forwardRef(Za),tl=[{name:"Dashboard",href:"/",icon:ba},{name:"Dersler",href:"/lessons",icon:wa},{name:"Konular",href:"/subjects",icon:ja},{name:"A\xe7\u0131klamalar",href:"/explanations",icon:Ca},{name:"Sorular",href:"/questions",icon:Oa},{name:"Loglar",href:"/logs",icon:_a},{name:"Kullan\u0131c\u0131lar",href:"/users",icon:za},{name:"Admin Kullan\u0131c\u0131lar",href:"/admin-users",icon:Ia}],nl=e=>{var t;let{children:n}=e;const[r,l]=(0,a.useState)(!1),{user:i,logout:o}=la(),{theme:s,toggleTheme:u}=(()=>{const e=(0,a.useContext)(oa);if(void 0===e)throw new Error("useTheme must be used within a ThemeProvider");return e})(),c=pe(),d=e=>c.pathname===e;return(0,ra.jsxs)("div",{className:"h-screen flex overflow-hidden bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:[(0,ra.jsxs)("div",{className:"fixed inset-0 flex z-40 md:hidden ".concat(r?"":"hidden"),children:[(0,ra.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm",onClick:()=>l(!1)}),(0,ra.jsxs)("div",{className:"relative flex-1 flex flex-col max-w-xs w-full bg-slate-800/95 backdrop-blur-xl border-r border-slate-700/50",children:[(0,ra.jsx)("div",{className:"absolute top-0 right-0 -mr-12 pt-2",children:(0,ra.jsx)("button",{type:"button",className:"ml-1 flex items-center justify-center h-10 w-10 rounded-full bg-slate-800/80 backdrop-blur-sm hover:bg-slate-700/80 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200",onClick:()=>l(!1),children:(0,ra.jsx)(Ha,{className:"h-6 w-6 text-slate-200"})})}),(0,ra.jsxs)("div",{className:"flex-1 h-0 pt-6 pb-4 overflow-y-auto",children:[(0,ra.jsx)("div",{className:"flex-shrink-0 flex items-center px-6 mb-8",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,ra.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,ra.jsx)("span",{className:"text-white font-bold text-sm",children:"K"})}),(0,ra.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"KPSS Plus Admin"})]})}),(0,ra.jsx)("nav",{className:"px-3 space-y-2",children:tl.map(e=>{const t=e.icon;return(0,ra.jsxs)(yt,{to:e.href,className:"group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ".concat(d(e.href)?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25":"text-slate-300 hover:bg-slate-700/50 hover:text-white hover:shadow-md"),onClick:()=>l(!1),children:[(0,ra.jsx)(t,{className:"mr-3 h-5 w-5 flex-shrink-0"}),e.name]},e.name)})})]})]})]}),(0,ra.jsx)("div",{className:"hidden md:flex md:flex-shrink-0",children:(0,ra.jsx)("div",{className:"flex flex-col w-72",children:(0,ra.jsx)("div",{className:"flex flex-col h-0 flex-1 bg-slate-800/95 backdrop-blur-xl border-r border-slate-700/50",children:(0,ra.jsxs)("div",{className:"flex-1 flex flex-col pt-6 pb-4 overflow-y-auto",children:[(0,ra.jsx)("div",{className:"flex items-center flex-shrink-0 px-6 mb-8",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,ra.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg",children:(0,ra.jsx)("span",{className:"text-white font-bold text-lg",children:"K"})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"KPSS Plus"}),(0,ra.jsx)("p",{className:"text-xs text-slate-400 font-medium",children:"Admin Panel"})]})]})}),(0,ra.jsx)("nav",{className:"flex-1 px-3 space-y-2",children:tl.map(e=>{const t=e.icon;return(0,ra.jsxs)(yt,{to:e.href,className:"group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 ".concat(d(e.href)?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25 transform scale-[1.02]":"text-slate-300 hover:bg-slate-700/50 hover:text-white hover:shadow-md hover:transform hover:scale-[1.01]"),children:[(0,ra.jsx)(t,{className:"mr-3 h-5 w-5 flex-shrink-0"}),e.name]},e.name)})})]})})})}),(0,ra.jsxs)("div",{className:"flex flex-col w-0 flex-1 overflow-hidden",children:[(0,ra.jsxs)("div",{className:"relative z-10 flex-shrink-0 flex h-20 bg-slate-800/95 backdrop-blur-xl border-b border-slate-700/50 shadow-xl",children:[(0,ra.jsx)("button",{type:"button",className:"px-4 border-r border-slate-700/50 text-slate-400 hover:text-slate-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden transition-colors duration-200",onClick:()=>l(!0),children:(0,ra.jsx)(Va,{className:"h-6 w-6"})}),(0,ra.jsxs)("div",{className:"flex-1 px-6 flex justify-between items-center",children:[(0,ra.jsx)("div",{className:"flex-1 flex",children:(0,ra.jsx)("div",{className:"max-w-lg w-full lg:max-w-xs",children:(0,ra.jsxs)("div",{className:"relative",children:[(0,ra.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,ra.jsx)("svg",{className:"h-5 w-5 text-slate-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,ra.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,ra.jsx)("input",{className:"block w-full pl-10 pr-3 py-2 border border-slate-600 rounded-lg bg-slate-700/50 backdrop-blur-sm text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200",placeholder:"Ara...",type:"search"})]})})}),(0,ra.jsxs)("div",{className:"ml-4 flex items-center md:ml-6 space-x-4",children:[(0,ra.jsx)("button",{type:"button",className:"p-2 rounded-xl bg-slate-700/50 text-slate-400 hover:text-slate-200 hover:bg-slate-600/50 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200",onClick:u,children:"dark"===s?(0,ra.jsx)(Qa,{className:"h-5 w-5"}):(0,ra.jsx)(Xa,{className:"h-5 w-5"})}),(0,ra.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,ra.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,ra.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,ra.jsx)("span",{className:"text-white font-semibold text-sm",children:null===i||void 0===i||null===(t=i.username)||void 0===t?void 0:t.charAt(0).toUpperCase()})}),(0,ra.jsx)("span",{className:"text-sm font-medium text-slate-200",children:null===i||void 0===i?void 0:i.username})]}),(0,ra.jsx)("button",{type:"button",className:"p-2 rounded-xl bg-slate-700/50 text-slate-400 hover:text-red-400 hover:bg-red-500/10 focus:outline-none focus:ring-2 focus:ring-red-500 transition-all duration-200",onClick:o,title:"\xc7\u0131k\u0131\u015f Yap",children:(0,ra.jsx)(el,{className:"h-5 w-5"})})]})]})]})]}),(0,ra.jsx)("main",{className:"flex-1 relative overflow-y-auto focus:outline-none bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:(0,ra.jsx)("div",{className:"py-8",children:(0,ra.jsx)("div",{className:"max-w-7xl mx-auto px-6 sm:px-8 md:px-10",children:(0,ra.jsx)("div",{className:"min-h-full",children:n})})})})]})]})},rl=["title","titleId"];function al(e,t){let{title:n,titleId:r}=e,l=o(e,rl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"}))}const ll=a.forwardRef(al),il=["title","titleId"];function ol(e,t){let{title:n,titleId:r}=e,l=o(e,il);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))}const sl=a.forwardRef(ol),ul=()=>{const[e,t]=(0,a.useState)({users:0,lessons:0,subjects:0,explanations:0,questions:0,logs:0}),[n,r]=(0,a.useState)(!0);(0,a.useEffect)(()=>{l()},[]);const l=async()=>{try{var e,n,a,l,i,o;r(!0);const[s,u,c,d,f,m]=await Promise.all([Zr("/users?limit=1"),Zr("/lessons?limit=1"),Zr("/subjects?limit=1"),Zr("/explanations?limit=1"),Zr("/questions?limit=1"),Zr("/logs?limit=1")]);t({users:null!==(e=s.total)&&void 0!==e?e:0,lessons:null!==(n=u.total)&&void 0!==n?n:0,subjects:null!==(a=c.total)&&void 0!==a?a:0,explanations:null!==(l=d.total)&&void 0!==l?l:0,questions:null!==(i=f.total)&&void 0!==i?i:0,logs:null!==(o=m.total)&&void 0!==o?o:0})}catch(s){console.error("Dashboard stats failed",s)}finally{r(!1)}},i=[{name:"Kullan\u0131c\u0131lar",value:e.users,icon:za,gradient:"from-blue-500 to-cyan-500",iconBg:"bg-blue-500/20",href:"/users"},{name:"Dersler",value:e.lessons,icon:ll,gradient:"from-purple-500 to-pink-500",iconBg:"bg-purple-500/20",href:"/lessons"},{name:"Konular",value:e.subjects,icon:ja,gradient:"from-emerald-500 to-teal-500",iconBg:"bg-emerald-500/20",href:"/subjects"},{name:"A\xe7\u0131klamalar",value:e.explanations,icon:Ca,gradient:"from-rose-500 to-red-500",iconBg:"bg-rose-500/20",href:"/explanations"},{name:"Sorular",value:e.questions,icon:Oa,gradient:"from-indigo-500 to-violet-500",iconBg:"bg-indigo-500/20",href:"/questions"},{name:"Loglar",value:e.logs,icon:_a,gradient:"from-amber-500 to-orange-500",iconBg:"bg-amber-500/20",href:"/logs"}];return(0,ra.jsx)(nl,{children:(0,ra.jsxs)("div",{className:"space-y-8",children:[(0,ra.jsxs)("div",{className:"text-center",children:[(0,ra.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent",children:"Dashboard"}),(0,ra.jsx)("p",{className:"mt-4 text-lg text-slate-300 max-w-2xl mx-auto",children:"KPSS Plus y\xf6netim paneline ho\u015f geldiniz. \u0130\xe7erikleri ve sistemi buradan y\xf6netebilirsiniz."})]}),(0,ra.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3",children:i.map(e=>{const t=e.icon;return(0,ra.jsxs)("a",{href:e.href,className:"group relative bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 hover:bg-slate-800/80 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-2xl hover:shadow-blue-500/10",children:[(0,ra.jsxs)("div",{className:"flex items-center justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("p",{className:"text-sm font-medium text-slate-400 uppercase tracking-wider",children:e.name}),(0,ra.jsx)("p",{className:"mt-2 text-3xl font-bold text-white",children:n?(0,ra.jsx)("div",{className:"animate-pulse bg-slate-700 h-8 w-20 rounded-lg"}):(0,ra.jsx)("span",{className:"bg-gradient-to-r ".concat(e.gradient," bg-clip-text text-transparent"),children:e.value.toLocaleString()})})]}),(0,ra.jsx)("div",{className:"".concat(e.iconBg," p-3 rounded-xl group-hover:scale-110 transition-transform duration-300"),children:(0,ra.jsx)(t,{className:"h-8 w-8 bg-gradient-to-r ".concat(e.gradient," bg-clip-text text-transparent")})})]}),(0,ra.jsx)("div",{className:"mt-4 flex items-center text-sm text-slate-400",children:(0,ra.jsxs)("span",{children:["Son g\xfcncelleme: ",n?"y\xfckleniyor":"az \xf6nce"]})})]},e.name)})}),(0,ra.jsxs)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-8",children:[(0,ra.jsxs)("div",{className:"text-center mb-8",children:[(0,ra.jsx)("h3",{className:"text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"H\u0131zl\u0131 \u0130\u015flemler"}),(0,ra.jsx)("p",{className:"mt-2 text-slate-400",children:"S\u0131k kullan\u0131lan y\xf6netim ekranlar\u0131na h\u0131zl\u0131 eri\u015fim"})]}),(0,ra.jsx)("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[{href:"/lessons",title:"Yeni Ders",description:"Yeni ders ekleyin",icon:ll},{href:"/subjects",title:"Yeni Konu",description:"Derslere konu ekleyin",icon:ja},{href:"/explanations",title:"A\xe7\u0131klama Ekle",description:"Konu i\xe7erikleri olu\u015fturun",icon:Ca},{href:"/questions",title:"Soru Ekle",description:"Soru bankas\u0131na yeni kay\u0131t",icon:Oa}].map(e=>{const t=e.icon;return(0,ra.jsx)("a",{href:e.href,className:"group relative bg-slate-700/50 backdrop-blur-sm p-6 rounded-xl border border-slate-600/50 hover:border-blue-500/50 hover:bg-slate-700/70 transition-all duration-300 hover:transform hover:scale-105",children:(0,ra.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,ra.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,ra.jsx)(t,{className:"h-8 w-8 text-white"})}),(0,ra.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:e.title}),(0,ra.jsx)("p",{className:"text-sm text-slate-400",children:e.description})]})},e.href)})})]}),(0,ra.jsxs)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6 flex items-center justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h3",{className:"text-xl font-semibold text-white",children:"API Aktivitesi"}),(0,ra.jsx)("p",{className:"text-sm text-slate-400 mt-1",children:"Loglar \xfczerinden sistem trafi\u011fini izleyin ve sorunlar\u0131 h\u0131zla g\xf6r\xfcn."})]}),(0,ra.jsxs)("a",{href:"/logs",className:"btn-secondary flex items-center space-x-2",children:[(0,ra.jsx)(sl,{className:"h-5 w-5"}),(0,ra.jsx)("span",{children:"Loglar\u0131 G\xf6r\xfcnt\xfcle"})]})]})]})})},cl=["title","titleId"];function dl(e,t){let{title:n,titleId:r}=e,l=o(e,cl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))}const fl=a.forwardRef(dl),ml=["title","titleId"];function pl(e,t){let{title:n,titleId:r}=e,l=o(e,ml);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const hl=a.forwardRef(pl),vl=["title","titleId"];function gl(e,t){let{title:n,titleId:r}=e,l=o(e,vl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const bl=a.forwardRef(gl),yl=["title","titleId"];function xl(e,t){let{title:n,titleId:r}=e,l=o(e,yl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L6.832 19.82a4.5 4.5 0 0 1-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 0 1 1.13-1.897L16.863 4.487Zm0 0L19.5 7.125"}))}const wl=a.forwardRef(xl),kl=["title","titleId"];function Sl(e,t){let{title:n,titleId:r}=e,l=o(e,kl);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"}))}const jl=a.forwardRef(Sl);function Nl(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch(e=>setTimeout(()=>{throw e}))}function El(){let e=[],t={addEventListener:(e,n,r,a)=>(e.addEventListener(n,r,a),t.add(()=>e.removeEventListener(n,r,a))),requestAnimationFrame(){let e=requestAnimationFrame(...arguments);return t.add(()=>cancelAnimationFrame(e))},nextFrame(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame(()=>t.requestAnimationFrame(...n))},setTimeout(){let e=setTimeout(...arguments);return t.add(()=>clearTimeout(e))},microTask(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];let a={current:!0};return Nl(()=>{a.current&&n[0]()}),t.add(()=>{a.current=!1})},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add(()=>{Object.assign(e.style,{[t]:r})})},group(e){let t=El();return e(t),this.add(()=>t.dispose())},add:t=>(e.includes(t)||e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function Cl(){let[e]=(0,a.useState)(El);return(0,a.useEffect)(()=>()=>e.dispose(),[e]),e}var Pl=Object.defineProperty,Tl=(e,t,n)=>(((e,t,n)=>{t in e?Pl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let Ol=new class{constructor(){Tl(this,"current",this.detect()),Tl(this,"handoffState","pending"),Tl(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},Ll=(e,t)=>{Ol.isServer?(0,a.useEffect)(e,t):(0,a.useLayoutEffect)(e,t)};function Rl(e){let t=(0,a.useRef)(e);return Ll(()=>{t.current=e},[e]),t}let _l=function(e){let t=Rl(e);return a.useCallback(function(){return t.current(...arguments)},[t])};function Al(){let e=(0,a.useRef)(!1);return Ll(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function Fl(){let e=function(){let e="undefined"==typeof document;return(e=>e.useSyncExternalStore)(l)(()=>()=>{},()=>!1,()=>!e)}(),[t,n]=a.useState(Ol.isHandoffComplete);return t&&!1===Ol.isHandoffComplete&&n(!1),a.useEffect(()=>{!0!==t&&n(!0)},[t]),a.useEffect(()=>Ol.handoff(),[]),!e&&t}let zl=Symbol();function Dl(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];let r=(0,a.useRef)(t);(0,a.useEffect)(()=>{r.current=t},[t]);let l=_l(e=>{for(let t of r.current)null!=t&&("function"==typeof t?t(e):t.current=e)});return t.every(e=>null==e||(null==e?void 0:e[zl]))?void 0:l}var Ml,Il;"undefined"!=typeof process&&"undefined"!=typeof globalThis&&"undefined"!=typeof Element&&"test"===(null==(Ml=null==process?void 0:{NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0,REACT_APP_API_URL:"http://localhost:8100/api/v1"})?void 0:Ml.NODE_ENV)&&"undefined"==typeof(null==(Il=null==Element?void 0:Element.prototype)?void 0:Il.getAnimations)&&(Element.prototype.getAnimations=function(){return console.warn(["Headless UI has polyfilled `Element.prototype.getAnimations` for your tests.","Please install a proper polyfill e.g. `jsdom-testing-mocks`, to silence these warnings.","","Example usage:","```js","import { mockAnimationsApi } from 'jsdom-testing-mocks'","mockAnimationsApi()","```"].join("\n")),[]});var Ul=(e=>(e[e.None=0]="None",e[e.Closed=1]="Closed",e[e.Enter=2]="Enter",e[e.Leave=4]="Leave",e))(Ul||{});function Bl(e,t,n,r){let[l,i]=(0,a.useState)(n),{hasFlag:o,addFlag:s,removeFlag:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[t,n]=(0,a.useState)(e),r=(0,a.useCallback)(e=>n(e),[t]),l=(0,a.useCallback)(e=>n(t=>t|e),[t]),i=(0,a.useCallback)(e=>(t&e)===e,[t]),o=(0,a.useCallback)(e=>n(t=>t&~e),[n]),s=(0,a.useCallback)(e=>n(t=>t^e),[n]);return{flags:t,setFlag:r,addFlag:l,hasFlag:i,removeFlag:o,toggleFlag:s}}(e&&l?3:0),c=(0,a.useRef)(!1),d=(0,a.useRef)(!1),f=Cl();return Ll(()=>{var a;if(e)return n&&i(!0),t?(null==(a=null==r?void 0:r.start)||a.call(r,n),function(e,t){let{prepare:n,run:r,done:a,inFlight:l}=t,i=El();return function(e,t){let{inFlight:n,prepare:r}=t;if(null!=n&&n.current)return void r();let a=e.style.transition;e.style.transition="none",r(),e.offsetHeight,e.style.transition=a}(e,{prepare:n,inFlight:l}),i.nextFrame(()=>{r(),i.requestAnimationFrame(()=>{i.add(function(e,t){var n,r;let a=El();if(!e)return a.dispose;let l=!1;a.add(()=>{l=!0});let i=null!=(r=null==(n=e.getAnimations)?void 0:n.call(e).filter(e=>e instanceof CSSTransition))?r:[];return 0===i.length?(t(),a.dispose):(Promise.allSettled(i.map(e=>e.finished)).then(()=>{l||t()}),a.dispose)}(e,a))})}),i.dispose}(t,{inFlight:c,prepare(){d.current?d.current=!1:d.current=c.current,c.current=!0,!d.current&&(n?(s(3),u(4)):(s(4),u(2)))},run(){d.current?n?(u(3),s(4)):(u(4),s(3)):n?u(1):s(1)},done(){var e;d.current&&"function"==typeof t.getAnimations&&t.getAnimations().length>0||(c.current=!1,u(7),n||i(!1),null==(e=null==r?void 0:r.end)||e.call(r,n))}})):void(n&&s(3))},[e,n,t,f]),e?[l,{closed:o(1),enter:o(2),leave:o(4),transition:o(2)||o(4)}]:[n,{closed:void 0,enter:void 0,leave:void 0,transition:void 0}]}let Hl=(0,a.createContext)(null);Hl.displayName="OpenClosedContext";var Wl=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Wl||{});function ql(){return(0,a.useContext)(Hl)}function Vl(e){let{value:t,children:n}=e;return a.createElement(Hl.Provider,{value:t},n)}function Kl(e){let{children:t}=e;return a.createElement(Hl.Provider,{value:null},t)}function $l(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap(e=>"string"==typeof e?e.split(" "):[]))).filter(Boolean).join(" ")}function Ql(e,t){if(e in t){let l=t[e];for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];return"function"==typeof l?l(...r):l}let l=new Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map(e=>'"'.concat(e,'"')).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(l,Ql),l}const Yl=["static"],Gl=["unmount"],Xl=["as","children","refName"];var Jl=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(Jl||{}),Zl=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(Zl||{});function ei(){let e=function(){let e=(0,a.useRef)([]),t=(0,a.useCallback)(t=>{for(let n of e.current)null!=n&&("function"==typeof n?n(t):n.current=t)},[]);return function(){for(var n=arguments.length,r=new Array(n),a=0;a<n;a++)r[a]=arguments[a];if(!r.every(e=>null==e))return e.current=r,t}}();return(0,a.useCallback)(t=>function(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:a,features:l,visible:i=!0,name:s,mergeRefs:u}=e;u=null!=u?u:ni;let c=ri(n,t);if(i)return ti(c,r,a,s,u);let d=null!=l?l:0;if(2&d){let{static:e=!1}=c,t=o(c,Yl);if(e)return ti(t,r,a,s,u)}if(1&d){let{unmount:e=!0}=c,t=o(c,Gl);return Ql(e?0:1,{0:()=>null,1:()=>ti(f(f({},t),{},{hidden:!0,style:{display:"none"}}),r,a,s,u)})}return ti(c,r,a,s,u)}(f({mergeRefs:e},t)),[e])}function ti(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,l=arguments.length>4?arguments[4]:void 0,i=ii(e,["unmount","static"]),{as:s=n,children:u,refName:c="ref"}=i,d=o(i,Xl),f=void 0!==e.ref?{[c]:e.ref}:{},m="function"==typeof u?u(t):u;"className"in d&&d.className&&"function"==typeof d.className&&(d.className=d.className(t)),d["aria-labelledby"]&&d["aria-labelledby"]===d.id&&(d["aria-labelledby"]=void 0);let p={};if(t){let e=!1,n=[];for(let[r,a]of Object.entries(t))"boolean"==typeof a&&(e=!0),!0===a&&n.push(r.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())));if(e){p["data-headlessui-state"]=n.join(" ");for(let e of n)p["data-".concat(e)]=""}}if(s===a.Fragment&&(Object.keys(li(d)).length>0||Object.keys(li(p)).length>0)){if((0,a.isValidElement)(m)&&!(Array.isArray(m)&&m.length>1)){let e=m.props,t=null==e?void 0:e.className,n="function"==typeof t?function(){return $l(t(...arguments),d.className)}:$l(t,d.className),r=n?{className:n}:{},i=ri(m.props,li(ii(d,["ref"])));for(let a in p)a in i&&delete p[a];return(0,a.cloneElement)(m,Object.assign({},i,p,f,{ref:l(oi(m),f.ref)},r))}if(Object.keys(li(d)).length>0)throw new Error(['Passing props on "Fragment"!',"","The current component <".concat(r,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(li(d)).concat(Object.keys(li(p))).map(e=>"  - ".concat(e)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map(e=>"  - ".concat(e)).join("\n")].join("\n"))}return(0,a.createElement)(s,Object.assign({},ii(d,["ref"]),s!==a.Fragment&&f,s!==a.Fragment&&p),m)}function ni(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every(e=>null==e)?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function ri(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},a={};for(let l of t)for(let e in l)e.startsWith("on")&&"function"==typeof l[e]?(null!=a[e]||(a[e]=[]),a[e].push(l[e])):r[e]=l[e];if(r.disabled||r["aria-disabled"])for(let l in a)/^(on(?:Click|Pointer|Mouse|Key)(?:Down|Up|Press)?)$/.test(l)&&(a[l]=[e=>{var t;return null==(t=null==e?void 0:e.preventDefault)?void 0:t.call(e)}]);for(let l in a)Object.assign(r,{[l](e){let t=a[l];for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let a of t){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;a(e,...r)}}});return r}function ai(e){var t;return Object.assign((0,a.forwardRef)(e),{displayName:null!=(t=e.displayName)?t:e.name})}function li(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function ii(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}function oi(e){return a.version.split(".")[0]>="19"?e.props.ref:e.ref}const si=["transition","beforeEnter","afterEnter","beforeLeave","afterLeave","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"],ui=["show","appear","unmount"];function ci(e){var t;return!!(e.enter||e.enterFrom||e.enterTo||e.leave||e.leaveFrom||e.leaveTo)||(null!=(t=e.as)?t:vi)!==a.Fragment||1===a.Children.count(e.children)}let di=(0,a.createContext)(null);di.displayName="TransitionContext";var fi=(e=>(e.Visible="visible",e.Hidden="hidden",e))(fi||{});let mi=(0,a.createContext)(null);function pi(e){return"children"in e?pi(e.children):e.current.filter(e=>{let{el:t}=e;return null!==t.current}).filter(e=>{let{state:t}=e;return"visible"===t}).length>0}function hi(e,t){let n=Rl(e),r=(0,a.useRef)([]),l=Al(),i=Cl(),o=_l(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Zl.Hidden,a=r.current.findIndex(t=>{let{el:n}=t;return n===e});-1!==a&&(Ql(t,{[Zl.Unmount](){r.current.splice(a,1)},[Zl.Hidden](){r.current[a].state="hidden"}}),i.microTask(()=>{var e;!pi(r)&&l.current&&(null==(e=n.current)||e.call(n))}))}),s=_l(e=>{let t=r.current.find(t=>{let{el:n}=t;return n===e});return t?"visible"!==t.state&&(t.state="visible"):r.current.push({el:e,state:"visible"}),()=>o(e,Zl.Unmount)}),u=(0,a.useRef)([]),c=(0,a.useRef)(Promise.resolve()),d=(0,a.useRef)({enter:[],leave:[]}),f=_l((e,n,r)=>{u.current.splice(0),t&&(t.chains.current[n]=t.chains.current[n].filter(t=>{let[n]=t;return n!==e})),null==t||t.chains.current[n].push([e,new Promise(e=>{u.current.push(e)})]),null==t||t.chains.current[n].push([e,new Promise(e=>{Promise.all(d.current[n].map(e=>{let[t,n]=e;return n})).then(()=>e())})]),"enter"===n?c.current=c.current.then(()=>null==t?void 0:t.wait.current).then(()=>r(n)):r(n)}),m=_l((e,t,n)=>{Promise.all(d.current[t].splice(0).map(e=>{let[t,n]=e;return n})).then(()=>{var e;null==(e=u.current.shift())||e()}).then(()=>n(t))});return(0,a.useMemo)(()=>({children:r,register:s,unregister:o,onStart:f,onStop:m,wait:c,chains:d}),[s,o,r,f,m,d,c])}mi.displayName="NestingContext";let vi=a.Fragment,gi=Jl.RenderStrategy;let bi=ai(function(e,t){let{show:n,appear:r=!1,unmount:l=!0}=e,i=o(e,ui),s=(0,a.useRef)(null),u=Dl(...ci(e)?[s,t]:null===t?[]:[t]);Fl();let c=ql();if(void 0===n&&null!==c&&(n=(c&Wl.Open)===Wl.Open),void 0===n)throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[d,m]=(0,a.useState)(n?"visible":"hidden"),p=hi(()=>{n||m("hidden")}),[h,v]=(0,a.useState)(!0),g=(0,a.useRef)([n]);Ll(()=>{!1!==h&&g.current[g.current.length-1]!==n&&(g.current.push(n),v(!1))},[g,n]);let b=(0,a.useMemo)(()=>({show:n,appear:r,initial:h}),[n,r,h]);Ll(()=>{n?m("visible"):!pi(p)&&null!==s.current&&m("hidden")},[n,p]);let y={unmount:l},x=_l(()=>{var t;h&&v(!1),null==(t=e.beforeEnter)||t.call(e)}),w=_l(()=>{var t;h&&v(!1),null==(t=e.beforeLeave)||t.call(e)}),k=ei();return a.createElement(mi.Provider,{value:p},a.createElement(di.Provider,{value:b},k({ourProps:f(f({},y),{},{as:a.Fragment,children:a.createElement(yi,f(f(f({ref:u},y),i),{},{beforeEnter:x,beforeLeave:w}))}),theirProps:{},defaultTag:a.Fragment,features:gi,visible:"visible"===d,name:"Transition"})))}),yi=ai(function(e,t){var n,r;let{transition:l=!0,beforeEnter:i,afterEnter:s,beforeLeave:u,afterLeave:c,enter:d,enterFrom:m,enterTo:p,entered:h,leave:v,leaveFrom:g,leaveTo:b}=e,y=o(e,si),[x,w]=(0,a.useState)(null),k=(0,a.useRef)(null),S=ci(e),j=Dl(...S?[k,t,w]:null===t?[]:[t]),N=null==(n=y.unmount)||n?Zl.Unmount:Zl.Hidden,{show:E,appear:C,initial:P}=function(){let e=(0,a.useContext)(di);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[T,O]=(0,a.useState)(E?"visible":"hidden"),L=function(){let e=(0,a.useContext)(mi);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:R,unregister:_}=L;Ll(()=>R(k),[R,k]),Ll(()=>{if(N===Zl.Hidden&&k.current)return E&&"visible"!==T?void O("visible"):Ql(T,{hidden:()=>_(k),visible:()=>R(k)})},[T,k,R,_,E,N]);let A=Fl();Ll(()=>{if(S&&A&&"visible"===T&&null===k.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")},[k,T,A,S]);let F=P&&!C,z=C&&E&&P,D=(0,a.useRef)(!1),M=hi(()=>{D.current||(O("hidden"),_(k))},L),I=_l(e=>{D.current=!0;let t=e?"enter":"leave";M.onStart(k,t,e=>{"enter"===e?null==i||i():"leave"===e&&(null==u||u())})}),U=_l(e=>{let t=e?"enter":"leave";D.current=!1,M.onStop(k,t,e=>{"enter"===e?null==s||s():"leave"===e&&(null==c||c())}),"leave"===t&&!pi(M)&&(O("hidden"),_(k))});(0,a.useEffect)(()=>{S&&l||(I(E),U(E))},[E,S,l]);let B=!(!l||!S||!A||F),[,H]=Bl(B,x,E,{start:I,end:U}),W=li(f({ref:j,className:(null==(r=$l(y.className,z&&d,z&&m,H.enter&&d,H.enter&&H.closed&&m,H.enter&&!H.closed&&p,H.leave&&v,H.leave&&!H.closed&&g,H.leave&&H.closed&&b,!H.transition&&E&&h))?void 0:r.trim())||void 0},function(e){let t={};for(let n in e)!0===e[n]&&(t["data-".concat(n)]="");return t}(H))),q=0;"visible"===T&&(q|=Wl.Open),"hidden"===T&&(q|=Wl.Closed),E&&"hidden"===T&&(q|=Wl.Opening),!E&&"visible"===T&&(q|=Wl.Closing);let V=ei();return a.createElement(mi.Provider,{value:M},a.createElement(Vl,{value:q},V({ourProps:W,theirProps:y,defaultTag:vi,features:gi,visible:"visible"===T,name:"Transition.Child"})))}),xi=ai(function(e,t){let n=null!==(0,a.useContext)(di),r=null!==ql();return a.createElement(a.Fragment,null,!n&&r?a.createElement(bi,f({ref:t},e)):a.createElement(yi,f({ref:t},e)))}),wi=Object.assign(bi,{Child:xi,Root:bi});var ki=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(ki||{});function Si(e,t,n,r){let l=Rl(n);(0,a.useEffect)(()=>{function n(e){l.current(e)}return(e=null!=e?e:window).addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)},[e,t,r])}class ji extends Map{constructor(e){super(),this.factory=e}get(e){let t=super.get(e);return void 0===t&&(t=this.factory(e),this.set(e,t)),t}}var Ni,Ei,Ci,Pi=Object.defineProperty,Ti=(e,t,n)=>(((e,t,n)=>{t in e?Pi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),Oi=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},Li=(e,t,n)=>(Oi(e,t,"read from private field"),n?n.call(e):t.get(e)),Ri=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},_i=(e,t,n,r)=>(Oi(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);class Ai{constructor(e){Ri(this,Ni,{}),Ri(this,Ei,new ji(()=>new Set)),Ri(this,Ci,new Set),Ti(this,"disposables",El()),_i(this,Ni,e),Ol.isServer&&this.disposables.microTask(()=>{this.dispose()})}dispose(){this.disposables.dispose()}get state(){return Li(this,Ni)}subscribe(e,t){if(Ol.isServer)return()=>{};let n={selector:e,callback:t,current:e(Li(this,Ni))};return Li(this,Ci).add(n),this.disposables.add(()=>{Li(this,Ci).delete(n)})}on(e,t){return Ol.isServer?()=>{}:(Li(this,Ei).get(e).add(t),this.disposables.add(()=>{Li(this,Ei).get(e).delete(t)}))}send(e){let t=this.reduce(Li(this,Ni),e);if(t!==Li(this,Ni)){_i(this,Ni,t);for(let e of Li(this,Ci)){let t=e.selector(Li(this,Ni));Fi(e.current,t)||(e.current=t,e.callback(t))}for(let t of Li(this,Ei).get(e.type))t(Li(this,Ni),e)}}}function Fi(e,t){return!!Object.is(e,t)||"object"==typeof e&&null!==e&&"object"==typeof t&&null!==t&&(Array.isArray(e)&&Array.isArray(t)?e.length===t.length&&zi(e[Symbol.iterator](),t[Symbol.iterator]()):e instanceof Map&&t instanceof Map||e instanceof Set&&t instanceof Set?e.size===t.size&&zi(e.entries(),t.entries()):!(!Di(e)||!Di(t))&&zi(Object.entries(e)[Symbol.iterator](),Object.entries(t)[Symbol.iterator]()))}function zi(e,t){for(;;){let n=e.next(),r=t.next();if(n.done&&r.done)return!0;if(n.done||r.done||!Object.is(n.value,r.value))return!1}}function Di(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||null===Object.getPrototypeOf(t)}Ni=new WeakMap,Ei=new WeakMap,Ci=new WeakMap;var Mi=Object.defineProperty,Ii=(e,t,n)=>(((e,t,n)=>{t in e?Mi(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n),Ui=(e=>(e[e.Push=0]="Push",e[e.Pop=1]="Pop",e))(Ui||{});let Bi={0(e,t){let n=t.id,r=e.stack,a=e.stack.indexOf(n);if(-1!==a){let t=e.stack.slice();return t.splice(a,1),t.push(n),r=t,f(f({},e),{},{stack:r})}return f(f({},e),{},{stack:[...e.stack,n]})},1(e,t){let n=t.id,r=e.stack.indexOf(n);if(-1===r)return e;let a=e.stack.slice();return a.splice(r,1),f(f({},e),{},{stack:a})}};class Hi extends Ai{constructor(){super(...arguments),Ii(this,"actions",{push:e=>this.send({type:0,id:e}),pop:e=>this.send({type:1,id:e})}),Ii(this,"selectors",{isTop:(e,t)=>e.stack[e.stack.length-1]===t,inStack:(e,t)=>e.stack.includes(t)})}static new(){return new Hi({stack:[]})}reduce(e,t){return Ql(t.type,Bi,e,t)}}const Wi=new ji(()=>Hi.new());var qi=n(237);function Vi(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Fi;return(0,qi.useSyncExternalStoreWithSelector)(_l(t=>e.subscribe(Ki,t)),_l(()=>e.state),_l(()=>e.state),_l(t),n)}function Ki(e){return e}function $i(e,t){let n=(0,a.useId)(),r=Wi.get(t),[l,i]=Vi(r,(0,a.useCallback)(e=>[r.selectors.isTop(e,n),r.selectors.inStack(e,n)],[r,n]));return Ll(()=>{if(e)return r.actions.push(n),()=>r.actions.pop(n)},[r,e,n]),!!e&&(!i||l)}function Qi(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"undefined"!=typeof document?document.defaultView:null,n=arguments.length>2?arguments[2]:void 0,r=$i(e,"escape");Si(t,"keydown",e=>{r&&(e.defaultPrevented||e.key===ki.Escape&&n(e))})}function Yi(e){var t,n;return Ol.isServer?null:e?"ownerDocument"in e?e.ownerDocument:"current"in e?null!=(n=null==(t=e.current)?void 0:t.ownerDocument)?n:document:null:document}let Gi=new Map,Xi=new Map;function Ji(e){var t;let n=null!=(t=Xi.get(e))?t:0;return Xi.set(e,n+1),0!==n||(Gi.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0),()=>Zi(e)}function Zi(e){var t;let n=null!=(t=Xi.get(e))?t:1;if(1===n?Xi.delete(e):Xi.set(e,n-1),1!==n)return;let r=Gi.get(e);r&&(null===r["aria-hidden"]?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",r["aria-hidden"]),e.inert=r.inert,Gi.delete(e))}function eo(e){let{allowed:t,disallowed:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=$i(e,"inert-others");Ll(()=>{var e,a;if(!r)return;let l=El();for(let t of null!=(e=null==n?void 0:n())?e:[])t&&l.add(Ji(t));let i=null!=(a=null==t?void 0:t())?a:[];for(let t of i){if(!t)continue;let e=Yi(t);if(!e)continue;let n=t.parentElement;for(;n&&n!==e.body;){for(let e of n.children)i.some(t=>e.contains(t))||l.add(Ji(e));n=n.parentElement}}return l.dispose},[r,t,n])}function to(e){return"object"==typeof e&&null!==e&&"nodeType"in e}function no(e){return to(e)&&"tagName"in e}function ro(e){return no(e)&&"accessKey"in e}function ao(e){return no(e)&&"tabIndex"in e}let lo=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(","),io=["[data-autofocus]"].map(e=>"".concat(e,":not([tabindex='-1'])")).join(",");var oo=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e[e.AutoFocus=64]="AutoFocus",e))(oo||{}),so=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(so||{}),uo=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(uo||{});function co(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(lo)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}var fo=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(fo||{});function mo(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;var n;return e!==(null==(n=Yi(e))?void 0:n.body)&&Ql(t,{0:()=>e.matches(lo),1(){let t=e;for(;null!==t;){if(t.matches(lo))return!0;t=t.parentElement}return!1}})}var po=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(po||{});function ho(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")},!0),document.addEventListener("click",e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")},!0));let vo=["textarea","input"].join(",");function go(e,t){let{sorted:n=!0,relativeTo:r=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort((e,n)=>{let r=t(e),a=t(n);if(null===r||null===a)return 0;let l=r.compareDocumentPosition(a);return l&Node.DOCUMENT_POSITION_FOLLOWING?-1:l&Node.DOCUMENT_POSITION_PRECEDING?1:0})}(e):e:64&t?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(io)).sort((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER)))}(e):co(e);a.length>0&&i.length>1&&(i=i.filter(e=>!a.some(t=>null!=t&&"current"in t?(null==t?void 0:t.current)===e:t===e))),r=null!=r?r:l.activeElement;let o,s=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,i.indexOf(r))-1;if(4&t)return Math.max(0,i.indexOf(r))+1;if(8&t)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=i.length;do{if(d>=f||d+f<=0)return 0;let e=u+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}o=i[e],null==o||o.focus(c),d+=s}while(o!==l.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,vo))&&n}(o)&&o.select(),2}function bo(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function yo(){return bo()||/Android/gi.test(window.navigator.userAgent)}function xo(e,t,n,r){let l=Rl(n);(0,a.useEffect)(()=>{if(e)return document.addEventListener(t,n,r),()=>document.removeEventListener(t,n,r);function n(e){l.current(e)}},[e,t,r])}function wo(e,t,n,r){let l=Rl(n);(0,a.useEffect)(()=>{if(e)return window.addEventListener(t,n,r),()=>window.removeEventListener(t,n,r);function n(e){l.current(e)}},[e,t,r])}function ko(e,t,n){let r=Rl(n),l=(0,a.useCallback)(function(e,n){if(e.defaultPrevented)return;let a=n(e);if(null===a||!a.getRootNode().contains(a)||!a.isConnected)return;let l=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(t);for(let t of l)if(null!==t&&(t.contains(a)||e.composed&&e.composedPath().includes(t)))return;return!mo(a,fo.Loose)&&-1!==a.tabIndex&&e.preventDefault(),r.current(e,a)},[r,t]),i=(0,a.useRef)(null);xo(e,"pointerdown",e=>{var t,n;yo()||(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)},!0),xo(e,"pointerup",e=>{if(yo()||!i.current)return;let t=i.current;return i.current=null,l(e,()=>t)},!0);let o=(0,a.useRef)({x:0,y:0});xo(e,"touchstart",e=>{o.current.x=e.touches[0].clientX,o.current.y=e.touches[0].clientY},!0),xo(e,"touchend",e=>{let t=e.changedTouches[0].clientX,n=e.changedTouches[0].clientY;if(!(Math.abs(t-o.current.x)>=30||Math.abs(n-o.current.y)>=30))return l(e,()=>ao(e.target)?e.target:null)},!0),wo(e,"blur",e=>l(e,()=>function(e){return ro(e)&&"IFRAME"===e.nodeName}(window.document.activeElement)?window.document.activeElement:null),!0)}function So(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.useMemo)(()=>Yi(...t),[...t])}const jo=["features"];var No=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(No||{});let Eo=ai(function(e,t){var n;let{features:r=1}=e,a=o(e,jo),l={ref:t,"aria-hidden":2===(2&r)||(null!=(n=a["aria-hidden"])?n:void 0),hidden:4===(4&r)||void 0,style:f({position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},4===(4&r)&&2!==(2&r)&&{display:"none"})};return ei()({ourProps:l,theirProps:a,slot:{},defaultTag:"span",name:"Hidden"})});let Co=(0,a.createContext)(null);function Po(e){let{children:t,node:n}=e,[r,l]=(0,a.useState)(null),i=To(null!=n?n:r);return a.createElement(Co.Provider,{value:i},t,null===i&&a.createElement(Eo,{features:No.Hidden,ref:e=>{var t,n;if(e)for(let r of null!=(n=null==(t=Yi(e))?void 0:t.querySelectorAll("html > *, body > *"))?n:[])if(r!==document.body&&r!==document.head&&no(r)&&null!=r&&r.contains(e)){l(r);break}}}))}function To(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;var t;return null!=(t=(0,a.useContext)(Co))?t:e}function Oo(){let e;return{before(t){let{doc:n}=t;var r;let a=n.documentElement,l=null!=(r=n.defaultView)?r:window;e=Math.max(0,l.innerWidth-a.clientWidth)},after(t){let{doc:n,d:r}=t,a=n.documentElement,l=Math.max(0,a.clientWidth-a.offsetWidth),i=Math.max(0,e-l);r.style(a,"paddingRight","".concat(i,"px"))}}}function Lo(){return bo()?{before(e){let{doc:t,d:n,meta:r}=e;function a(e){return r.containers.flatMap(e=>e()).some(t=>t.contains(e))}n.microTask(()=>{var e;if("auto"!==window.getComputedStyle(t.documentElement).scrollBehavior){let e=El();e.style(t.documentElement,"scrollBehavior","auto"),n.add(()=>n.microTask(()=>e.dispose()))}let r=null!=(e=window.scrollY)?e:window.pageYOffset,l=null;n.addEventListener(t,"click",e=>{if(ao(e.target))try{let n=e.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),i=t.querySelector(r);ao(i)&&!a(i)&&(l=i)}catch(n){}},!0),n.addEventListener(t,"touchstart",e=>{if(ao(e.target)&&function(e){return no(e)&&"style"in e}(e.target))if(a(e.target)){let t=e.target;for(;t.parentElement&&a(t.parentElement);)t=t.parentElement;n.style(t,"overscrollBehavior","contain")}else n.style(e.target,"touchAction","none")}),n.addEventListener(t,"touchmove",e=>{if(ao(e.target)){if(function(e){return ro(e)&&"INPUT"===e.nodeName}(e.target))return;if(a(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}},{passive:!1}),n.add(()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;r!==t&&window.scrollTo(0,r),l&&l.isConnected&&(l.scrollIntoView({block:"nearest"}),l=null)})})}}:{}}function Ro(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let _o=function(e,t){let n=e(),r=new Set;return{getSnapshot:()=>n,subscribe:e=>(r.add(e),()=>r.delete(e)),dispatch(e){for(var a=arguments.length,l=new Array(a>1?a-1:0),i=1;i<a;i++)l[i-1]=arguments[i];let o=t[e].call(n,...l);o&&(n=o,r.forEach(e=>e()))}}}(()=>new Map,{PUSH(e,t){var n;let r=null!=(n=this.get(e))?n:{doc:e,count:0,d:El(),meta:new Set};return r.count++,r.meta.add(t),this.set(e,r),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT(e){let{doc:t,d:n,meta:r}=e,a={doc:t,d:n,meta:Ro(r)},l=[Lo(),Oo(),{before(e){let{doc:t,d:n}=e;n.style(t.documentElement,"overflow","hidden")}}];l.forEach(e=>{let{before:t}=e;return null==t?void 0:t(a)}),l.forEach(e=>{let{after:t}=e;return null==t?void 0:t(a)})},SCROLL_ALLOW(e){let{d:t}=e;t.dispose()},TEARDOWN(e){let{doc:t}=e;this.delete(t)}});function Ao(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>({containers:[]}),r=function(e){return(0,a.useSyncExternalStore)(e.subscribe,e.getSnapshot,e.getSnapshot)}(_o),l=t?r.get(t):void 0,i=!!l&&l.count>0;return Ll(()=>{if(t&&e)return _o.dispatch("PUSH",t,n),()=>_o.dispatch("POP",t,n)},[e,t]),i}function Fo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:()=>[document.body];Ao($i(e,"scroll-lock"),t,e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],n]}})}_o.subscribe(()=>{let e=_o.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),r=0!==n.count;(r&&!e||!r&&e)&&_o.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&_o.dispatch("TEARDOWN",n)}});let zo=(0,a.createContext)(()=>{});function Do(e){let{value:t,children:n}=e;return a.createElement(zo.Provider,{value:t},n)}let Mo=(0,a.createContext)(!1);function Io(e){return a.createElement(Mo.Provider,{value:e.force},e.children)}let Uo=(0,a.createContext)(void 0);const Bo=["id"];let Ho=(0,a.createContext)(null);function Wo(){let e=(0,a.useContext)(Ho);if(null===e){let e=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(e,Wo),e}return e}Ho.displayName="DescriptionContext";let qo=ai(function(e,t){let n=(0,a.useId)(),r=(0,a.useContext)(Uo),{id:l="headlessui-description-".concat(n)}=e,i=o(e,Bo),s=Wo(),u=Dl(t);Ll(()=>s.register(l),[l,s.register]);let c=r||!1,d=(0,a.useMemo)(()=>f(f({},s.slot),{},{disabled:c}),[s.slot,c]),m=f(f({ref:u},s.props),{},{id:l});return ei()({ourProps:m,theirProps:i,slot:d,defaultTag:"p",name:s.name||"Description"})}),Vo=Object.assign(qo,{});function Ko(e){let t=_l(e),n=(0,a.useRef)(!1);(0,a.useEffect)(()=>(n.current=!1,()=>{n.current=!0,Nl(()=>{n.current&&t()})}),[t])}var $o=(e=>(e[e.Forwards=0]="Forwards",e[e.Backwards=1]="Backwards",e))($o||{});function Qo(e,t){let n=(0,a.useRef)([]),r=_l(e);(0,a.useEffect)(()=>{let e=[...n.current];for(let[a,l]of t.entries())if(n.current[a]!==l){let a=r(t,e);return n.current=t,a}},[r,...t])}let Yo=[];!function(e){function t(){"loading"!==document.readyState&&(e(),document.removeEventListener("DOMContentLoaded",t))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",t),t())}(()=>{function e(e){if(!ao(e.target)||e.target===document.body||Yo[0]===e.target)return;let t=e.target;t=t.closest(lo),Yo.unshift(null!=t?t:e.target),Yo=Yo.filter(e=>null!=e&&e.isConnected),Yo.splice(10)}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})});const Go=["initialFocus","initialFocusFallback","containers","features"];function Xo(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.current)no(n.current)&&t.add(n.current);return t}var Jo=(e=>(e[e.None=0]="None",e[e.InitialFocus=1]="InitialFocus",e[e.TabLock=2]="TabLock",e[e.FocusLock=4]="FocusLock",e[e.RestoreFocus=8]="RestoreFocus",e[e.AutoFocus=16]="AutoFocus",e))(Jo||{});let Zo=ai(function(e,t){let n=(0,a.useRef)(null),r=Dl(n,t),{initialFocus:l,initialFocusFallback:i,containers:s,features:u=15}=e,c=o(e,Go);Fl()||(u=0);let d=So(n);!function(e,t){let{ownerDocument:n}=t,r=!!(8&e),l=function(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=(0,a.useRef)(Yo.slice());return Qo((e,n)=>{let[r]=e,[a]=n;!0===a&&!1===r&&Nl(()=>{t.current.splice(0)}),!1===a&&!0===r&&(t.current=Yo.slice())},[e,Yo,t]),_l(()=>{var e;return null!=(e=t.current.find(e=>null!=e&&e.isConnected))?e:null})}(r);Qo(()=>{r||(null==n?void 0:n.activeElement)===(null==n?void 0:n.body)&&ho(l())},[r]),Ko(()=>{r&&ho(l())})}(u,{ownerDocument:d});let f=ts(u,{ownerDocument:d,container:n,initialFocus:l,initialFocusFallback:i});ns(u,{ownerDocument:d,container:n,containers:s,previousActiveElement:f});let m=function(){let e=(0,a.useRef)(0);return wo(!0,"keydown",t=>{"Tab"===t.key&&(e.current=t.shiftKey?1:0)},!0),e}(),p=_l(e=>{if(!ro(n.current))return;let t=n.current;Ql(m.current,{[$o.Forwards]:()=>{go(t,oo.First,{skipElements:[e.relatedTarget,i]})},[$o.Backwards]:()=>{go(t,oo.Last,{skipElements:[e.relatedTarget,i]})}})}),h=$i(!!(2&u),"focus-trap#tab-lock"),v=Cl(),g=(0,a.useRef)(!1),b={ref:r,onKeyDown(e){"Tab"==e.key&&(g.current=!0,v.requestAnimationFrame(()=>{g.current=!1}))},onBlur(e){if(!(4&u))return;let t=Xo(s);ro(n.current)&&t.add(n.current);let r=e.relatedTarget;ao(r)&&"true"!==r.dataset.headlessuiFocusGuard&&(rs(t,r)||(g.current?go(n.current,Ql(m.current,{[$o.Forwards]:()=>oo.Next,[$o.Backwards]:()=>oo.Previous})|oo.WrapAround,{relativeTo:e.target}):ao(e.target)&&ho(e.target)))}},y=ei();return a.createElement(a.Fragment,null,h&&a.createElement(Eo,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:No.Focusable}),y({ourProps:b,theirProps:c,defaultTag:"div",name:"FocusTrap"}),h&&a.createElement(Eo,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:p,features:No.Focusable}))}),es=Object.assign(Zo,{features:Jo});function ts(e,t){let{ownerDocument:n,container:r,initialFocus:l,initialFocusFallback:i}=t,o=(0,a.useRef)(null),s=$i(!!(1&e),"focus-trap#initial-focus"),u=Al();return Qo(()=>{if(0===e)return;if(!s)return void(null!=i&&i.current&&ho(i.current));let t=r.current;t&&Nl(()=>{if(!u.current)return;let r=null==n?void 0:n.activeElement;if(null!=l&&l.current){if((null==l?void 0:l.current)===r)return void(o.current=r)}else if(t.contains(r))return void(o.current=r);if(null!=l&&l.current)ho(l.current);else{if(16&e){if(go(t,oo.First|oo.AutoFocus)!==so.Error)return}else if(go(t,oo.First)!==so.Error)return;if(null!=i&&i.current&&(ho(i.current),(null==n?void 0:n.activeElement)===i.current))return;console.warn("There are no focusable elements inside the <FocusTrap />")}o.current=null==n?void 0:n.activeElement})},[i,s,e]),o}function ns(e,t){let{ownerDocument:n,container:r,containers:a,previousActiveElement:l}=t,i=Al(),o=!!(4&e);Si(null==n?void 0:n.defaultView,"focus",e=>{if(!o||!i.current)return;let t=Xo(a);ro(r.current)&&t.add(r.current);let n=l.current;if(!n)return;let s=e.target;ro(s)?rs(t,s)?(l.current=s,ho(s)):(e.preventDefault(),e.stopPropagation(),ho(n)):ho(l.current)},!0)}function rs(e,t){for(let n of e)if(n.contains(t))return!0;return!1}var as=n(950);const ls=["ownerDocument"],is=["enabled","ownerDocument"],os=["target"];function ss(e){let t=(0,a.useContext)(Mo),n=(0,a.useContext)(fs),[r,l]=(0,a.useState)(()=>{var r;if(!t&&null!==n)return null!=(r=n.current)?r:null;if(Ol.isServer)return null;let a=null==e?void 0:e.getElementById("headlessui-portal-root");if(a)return a;if(null===e)return null;let l=e.createElement("div");return l.setAttribute("id","headlessui-portal-root"),e.body.appendChild(l)});return(0,a.useEffect)(()=>{null!==r&&(null!=e&&e.body.contains(r)||null==e||e.body.appendChild(r))},[r,e]),(0,a.useEffect)(()=>{t||null!==n&&l(n.current)},[n,l,t]),r}let us=a.Fragment,cs=ai(function(e,t){let{ownerDocument:n=null}=e,r=o(e,ls),l=(0,a.useRef)(null),i=Dl(function(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Object.assign(e,{[zl]:t})}(e=>{l.current=e}),t),s=So(l),u=null!=n?n:s,c=ss(u),[d]=(0,a.useState)(()=>{var e;return Ol.isServer?null:null!=(e=null==u?void 0:u.createElement("div"))?e:null}),f=(0,a.useContext)(ms),m=Fl();Ll(()=>{!c||!d||c.contains(d)||(d.setAttribute("data-headlessui-portal",""),c.appendChild(d))},[c,d]),Ll(()=>{if(d&&f)return f.register(d)},[f,d]),Ko(()=>{var e;!c||!d||(to(d)&&c.contains(d)&&c.removeChild(d),c.childNodes.length<=0&&(null==(e=c.parentElement)||e.removeChild(c)))});let p=ei();return m&&c&&d?(0,as.createPortal)(p({ourProps:{ref:i},theirProps:r,slot:{},defaultTag:us,name:"Portal"}),d):null});let ds=a.Fragment,fs=(0,a.createContext)(null);let ms=(0,a.createContext)(null);function ps(){let e=(0,a.useContext)(ms),t=(0,a.useRef)([]),n=_l(n=>(t.current.push(n),e&&e.register(n),()=>r(n))),r=_l(n=>{let r=t.current.indexOf(n);-1!==r&&t.current.splice(r,1),e&&e.unregister(n)}),l=(0,a.useMemo)(()=>({register:n,unregister:r,portals:t}),[n,r,t]);return[t,(0,a.useMemo)(()=>function(e){let{children:t}=e;return a.createElement(ms.Provider,{value:l},t)},[l])]}let hs=ai(function(e,t){let n=Dl(t),{enabled:r=!0,ownerDocument:l}=e,i=o(e,is),s=ei();return r?a.createElement(cs,f(f({},i),{},{ownerDocument:l,ref:n})):s({ourProps:{ref:n},theirProps:i,slot:{},defaultTag:us,name:"Portal"})}),vs=ai(function(e,t){let{target:n}=e,r=o(e,os),l={ref:Dl(t)},i=ei();return a.createElement(fs.Provider,{value:n},i({ourProps:l,theirProps:r,defaultTag:ds,name:"Popover.Group"}))}),gs=Object.assign(hs,{Group:vs});const bs=["id","open","onClose","initialFocus","role","autoFocus","__demoMode","unmount"],ys=["transition","open"],xs=["id","transition"],ws=["transition"],ks=["id"];var Ss=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(Ss||{}),js=(e=>(e[e.SetTitleId=0]="SetTitleId",e))(js||{});let Ns={0:(e,t)=>e.titleId===t.id?e:f(f({},e),{},{titleId:t.id})},Es=(0,a.createContext)(null);function Cs(e){let t=(0,a.useContext)(Es);if(null===t){let t=new Error("<".concat(e," /> is missing a parent <Dialog /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,Cs),t}return t}function Ps(e,t){return Ql(t.type,Ns,e,t)}Es.displayName="DialogContext";let Ts=ai(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-dialog-".concat(n),open:l,onClose:i,initialFocus:s,role:u="dialog",autoFocus:c=!0,__demoMode:d=!1,unmount:f=!1}=e,m=o(e,bs),p=(0,a.useRef)(!1);u="dialog"===u||"alertdialog"===u?u:(p.current||(p.current=!0,console.warn("Invalid role [".concat(u,"] passed to <Dialog />. Only `dialog` and and `alertdialog` are supported. Using `dialog` instead."))),"dialog");let h=ql();void 0===l&&null!==h&&(l=(h&Wl.Open)===Wl.Open);let v=(0,a.useRef)(null),g=Dl(v,t),b=So(v),y=l?0:1,[x,w]=(0,a.useReducer)(Ps,{titleId:null,descriptionId:null,panelRef:(0,a.createRef)()}),k=_l(()=>i(!1)),S=_l(e=>w({type:0,id:e})),j=!!Fl()&&0===y,[N,E]=ps(),C={get current(){var e;return null!=(e=x.panelRef.current)?e:v.current}},P=To(),{resolveContainers:T}=function(){let{defaultContainers:e=[],portals:t,mainTreeNode:n}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=So(n),a=_l(()=>{var a,l;let i=[];for(let t of e)null!==t&&(no(t)?i.push(t):"current"in t&&no(t.current)&&i.push(t.current));if(null!=t&&t.current)for(let e of t.current)i.push(e);for(let e of null!=(a=null==r?void 0:r.querySelectorAll("html > *, body > *"))?a:[])e!==document.body&&e!==document.head&&no(e)&&"headlessui-portal-root"!==e.id&&(n&&(e.contains(n)||e.contains(null==(l=null==n?void 0:n.getRootNode())?void 0:l.host))||i.some(t=>e.contains(t))||i.push(e));return i});return{resolveContainers:a,contains:_l(e=>a().some(t=>t.contains(e)))}}({mainTreeNode:P,portals:N,defaultContainers:[C]}),O=null!==h&&(h&Wl.Closing)===Wl.Closing;eo(!d&&!O&&j,{allowed:_l(()=>{var e,t;return[null!=(t=null==(e=v.current)?void 0:e.closest("[data-headlessui-portal]"))?t:null]}),disallowed:_l(()=>{var e;return[null!=(e=null==P?void 0:P.closest("body > *:not(#headlessui-portal-root)"))?e:null]})});let L=Wi.get(null);Ll(()=>{if(j)return L.actions.push(r),()=>L.actions.pop(r)},[L,r,j]);let R=Vi(L,(0,a.useCallback)(e=>L.selectors.isTop(e,r),[L,r]));ko(R,T,e=>{e.preventDefault(),k()}),Qi(R,null==b?void 0:b.defaultView,e=>{e.preventDefault(),e.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur(),k()}),Fo(!d&&!O&&j,b,T),function(e,t,n){let r=Rl(e=>{let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&n()});(0,a.useEffect)(()=>{if(!e)return;let n=null===t?null:ro(t)?t:t.current;if(!n)return;let a=El();if("undefined"!=typeof ResizeObserver){let e=new ResizeObserver(()=>r.current(n));e.observe(n),a.add(()=>e.disconnect())}if("undefined"!=typeof IntersectionObserver){let e=new IntersectionObserver(()=>r.current(n));e.observe(n),a.add(()=>e.disconnect())}return()=>a.dispose()},[t,r,e])}(j,v,k);let[_,A]=function(){let[e,t]=(0,a.useState)([]);return[e.length>0?e.join(" "):void 0,(0,a.useMemo)(()=>function(e){let n=_l(e=>(t(t=>[...t,e]),()=>t(t=>{let n=t.slice(),r=n.indexOf(e);return-1!==r&&n.splice(r,1),n}))),r=(0,a.useMemo)(()=>({register:n,slot:e.slot,name:e.name,props:e.props,value:e.value}),[n,e.slot,e.name,e.props,e.value]);return a.createElement(Ho.Provider,{value:r},e.children)},[t])]}(),F=(0,a.useMemo)(()=>[{dialogState:y,close:k,setTitleId:S,unmount:f},x],[y,x,k,S,f]),z=(0,a.useMemo)(()=>({open:0===y}),[y]),D={ref:g,id:r,role:u,tabIndex:-1,"aria-modal":d?void 0:0===y||void 0,"aria-labelledby":x.titleId,"aria-describedby":_,unmount:f},M=!function(){var e;let[t]=(0,a.useState)(()=>"undefined"!=typeof window&&"function"==typeof window.matchMedia?window.matchMedia("(pointer: coarse)"):null),[n,r]=(0,a.useState)(null!=(e=null==t?void 0:t.matches)&&e);return Ll(()=>{if(t)return t.addEventListener("change",e),()=>t.removeEventListener("change",e);function e(e){r(e.matches)}},[t]),n}(),I=Jo.None;j&&!d&&(I|=Jo.RestoreFocus,I|=Jo.TabLock,c&&(I|=Jo.AutoFocus),M&&(I|=Jo.InitialFocus));let U=ei();return a.createElement(Kl,null,a.createElement(Io,{force:!0},a.createElement(gs,null,a.createElement(Es.Provider,{value:F},a.createElement(vs,{target:v},a.createElement(Io,{force:!1},a.createElement(A,{slot:z},a.createElement(E,null,a.createElement(es,{initialFocus:s,initialFocusFallback:v,containers:T,features:I},a.createElement(Do,{value:k},U({ourProps:D,theirProps:m,slot:z,defaultTag:Os,features:Ls,visible:0===y,name:"Dialog"})))))))))))}),Os="div",Ls=Jl.RenderStrategy|Jl.Static;let Rs=ai(function(e,t){let{transition:n=!1,open:r}=e,l=o(e,ys),i=ql(),s=e.hasOwnProperty("open")||null!==i,u=e.hasOwnProperty("onClose");if(!s&&!u)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!u)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&"boolean"!=typeof e.open)throw new Error("You provided an `open` prop to the `Dialog`, but the value is not a boolean. Received: ".concat(e.open));if("function"!=typeof e.onClose)throw new Error("You provided an `onClose` prop to the `Dialog`, but the value is not a function. Received: ".concat(e.onClose));return void 0===r&&!n||l.static?a.createElement(Po,null,a.createElement(Ts,f({ref:t,open:r},l))):a.createElement(Po,null,a.createElement(wi,{show:r,transition:n,unmount:l.unmount},a.createElement(Ts,f({ref:t},l))))}),_s=ai(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-dialog-panel-".concat(n),transition:l=!1}=e,i=o(e,xs),[{dialogState:s,unmount:u},c]=Cs("Dialog.Panel"),d=Dl(t,c.panelRef),m=(0,a.useMemo)(()=>({open:0===s}),[s]),p=_l(e=>{e.stopPropagation()}),h={ref:d,id:r,onClick:p},v=l?xi:a.Fragment,g=l?{unmount:u}:{},b=ei();return a.createElement(v,f({},g),b({ourProps:h,theirProps:i,slot:m,defaultTag:"div",name:"Dialog.Panel"}))}),As=(ai(function(e,t){let{transition:n=!1}=e,r=o(e,ws),[{dialogState:l,unmount:i}]=Cs("Dialog.Backdrop"),s=(0,a.useMemo)(()=>({open:0===l}),[l]),u={ref:t,"aria-hidden":!0},c=n?xi:a.Fragment,d=n?{unmount:i}:{},m=ei();return a.createElement(c,f({},d),m({ourProps:u,theirProps:r,slot:s,defaultTag:"div",name:"Dialog.Backdrop"}))}),ai(function(e,t){let n=(0,a.useId)(),{id:r="headlessui-dialog-title-".concat(n)}=e,l=o(e,ks),[{dialogState:i,setTitleId:s}]=Cs("Dialog.Title"),u=Dl(t);(0,a.useEffect)(()=>(s(r),()=>s(null)),[r,s]);let c=(0,a.useMemo)(()=>({open:0===i}),[i]),d={ref:u,id:r};return ei()({ourProps:d,theirProps:l,slot:c,defaultTag:"h2",name:"Dialog.Title"})})),Fs=Object.assign(Rs,{Panel:_s,Title:As,Description:Vo});const zs=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)(!1),[l,i]=(0,a.useState)(""),[o,s]=(0,a.useState)("all"),[u,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(null),[p,h]=(0,a.useState)({title:"",description:"",body:"",isPublished:!0}),v=async()=>{try{var e;r(!0);const n=new URLSearchParams;n.append("limit","200"),l.trim()&&n.append("q",l.trim()),"published"===o&&n.append("published","true"),"draft"===o&&n.append("published","false");const a=await Zr("/lessons?".concat(n.toString()));t(null!==(e=a.data)&&void 0!==e?e:[])}catch(n){console.error("Lessons load failed",n)}finally{r(!1)}};(0,a.useEffect)(()=>{v()},[o]);const g=(0,a.useMemo)(()=>{if(!l.trim())return e;const t=l.trim().toLowerCase();return e.filter(e=>{var n;return e.title.toLowerCase().includes(t)||(null!==(n=e.description)&&void 0!==n?n:"").toLowerCase().includes(t)})},[e,l]),b=()=>{h({title:"",description:"",body:"",isPublished:!0}),m(null)};return(0,ra.jsxs)(nl,{children:[(0,ra.jsxs)("div",{className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-2xl font-bold text-gray-100",children:"Dersler"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-gray-300",children:"T\xfcm dersler i\xe7in ba\u015fl\u0131k, a\xe7\u0131klama ve durum y\xf6netimini buradan yapabilirsiniz."})]}),(0,ra.jsxs)("div",{className:"flex items-center space-x-3 mt-4 sm:mt-0",children:[(0,ra.jsxs)("div",{className:"inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200",children:[(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("all"===o?"bg-slate-700 text-white":""),onClick:()=>s("all"),children:"T\xfcm\xfc"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("published"===o?"bg-slate-700 text-white":""),onClick:()=>s("published"),children:"Yay\u0131nda"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("draft"===o?"bg-slate-700 text-white":""),onClick:()=>s("draft"),children:"Taslak"})]}),(0,ra.jsxs)("button",{type:"button",onClick:()=>{b(),c(!0)},className:"btn-primary flex items-center",children:[(0,ra.jsx)(sl,{className:"h-4 w-4 mr-2"}),"Yeni Ders"]})]})]}),(0,ra.jsx)("div",{className:"card",children:(0,ra.jsx)("div",{className:"flex items-center",children:(0,ra.jsxs)("div",{className:"relative flex-1",children:[(0,ra.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,ra.jsx)(fl,{className:"h-5 w-5 text-gray-400"})}),(0,ra.jsx)("input",{type:"text",className:"input-field pl-10",placeholder:"Ders ara...",value:l,onChange:e=>i(e.target.value)})]})})}),(0,ra.jsx)("div",{className:"card p-0",children:n?(0,ra.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,ra.jsx)("div",{className:"animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500"}),(0,ra.jsx)("span",{className:"ml-3 text-gray-300",children:"Dersler y\xfckleniyor..."})]}):0===g.length?(0,ra.jsx)("div",{className:"py-12 text-center text-gray-400",children:"Kriterlere uygun ders bulunamad\u0131."}):(0,ra.jsx)("div",{className:"table-container",children:(0,ra.jsxs)("table",{className:"min-w-full divide-y divide-slate-700",children:[(0,ra.jsx)("thead",{className:"bg-slate-800/80",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"table-header",children:"Ders"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"A\xe7\u0131klama"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Olu\u015fturulma"}),(0,ra.jsx)("th",{className:"table-header",children:"Durum"}),(0,ra.jsx)("th",{className:"table-header",children:"\u0130\u015flemler"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-800 bg-slate-900/40",children:g.map(e=>(0,ra.jsxs)("tr",{className:"hover:bg-slate-800/60",children:[(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,ra.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center",children:(0,ra.jsx)(ll,{className:"h-5 w-5 text-primary-400"})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("p",{className:"text-sm font-medium text-white",children:e.title}),e.body&&(0,ra.jsx)("p",{className:"text-xs text-gray-400 truncate max-w-xs",children:e.body})]})]})}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:e.description||"-"}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-xs text-gray-400",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)(hl,{className:"h-4 w-4"}),(0,ra.jsx)("span",{children:new Date(e.created_at).toLocaleDateString("tr-TR")})]})}),(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ".concat(e.is_published?"bg-emerald-500/20 text-emerald-300":"bg-amber-500/20 text-amber-300"),children:[(0,ra.jsx)(bl,{className:"h-4 w-4 mr-1"}),e.is_published?"Yay\u0131nda":"Taslak"]})}),(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>(e=>{var t,n;m(e),h({title:e.title,description:null!==(t=e.description)&&void 0!==t?t:"",body:null!==(n=e.body)&&void 0!==n?n:"",isPublished:e.is_published}),c(!0)})(e),className:"icon-button text-primary-400 hover:text-primary-200",children:(0,ra.jsx)(wl,{className:"h-4 w-4"})}),(0,ra.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Bu dersi silmek istedi\u011finizden emin misiniz?"))try{await na("/lessons/".concat(e)),await v()}catch(t){console.error("Lesson delete failed",t),alert("Ders silinemedi.")}})(e.id),className:"icon-button text-red-400 hover:text-red-200",children:(0,ra.jsx)(jl,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,ra.jsx)(wi,{show:u,as:a.Fragment,children:(0,ra.jsxs)(Fs,{as:"div",className:"relative z-50",onClose:()=>c(!1),children:[(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,ra.jsx)("div",{className:"fixed inset-0 bg-black/60"})}),(0,ra.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,ra.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-150",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,ra.jsxs)(Fs.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all",children:[(0,ra.jsx)(Fs.Title,{className:"text-xl font-semibold text-white mb-4",children:d?"Dersi D\xfczenle":"Yeni Ders Olu\u015ftur"}),(0,ra.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t={title:p.title.trim(),description:p.description.trim()||null,body:p.body.trim()||null,is_published:p.isPublished};try{d?await ta("/lessons/".concat(d.id),t):await ea("/lessons",t),c(!1),b(),await v()}catch(n){console.error("Lesson save failed",n),alert("Ders kaydedilirken bir hata olu\u015ftu.")}},className:"space-y-6",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ba\u015fl\u0131k"}),(0,ra.jsx)("input",{type:"text",className:"input-field",value:p.title,onChange:e=>h(t=>f(f({},t),{},{title:e.target.value})),required:!0})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"K\u0131sa A\xe7\u0131klama"}),(0,ra.jsx)("textarea",{className:"input-field",rows:2,value:p.description,onChange:e=>h(t=>f(f({},t),{},{description:e.target.value})),placeholder:"Opsiyonel"})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"\u0130\xe7erik"}),(0,ra.jsx)("textarea",{className:"input-field",rows:6,value:p.body,onChange:e=>h(t=>f(f({},t),{},{body:e.target.value})),placeholder:"Ders metni veya a\xe7\u0131klamas\u0131"})]}),(0,ra.jsxs)("div",{className:"flex items-center justify-between",children:[(0,ra.jsxs)("label",{className:"inline-flex items-center space-x-2 text-sm text-gray-200",children:[(0,ra.jsx)("input",{type:"checkbox",className:"form-checkbox",checked:p.isPublished,onChange:e=>h(t=>f(f({},t),{},{isPublished:e.target.checked}))}),(0,ra.jsx)("span",{children:"Ders yay\u0131mlans\u0131n"})]}),(0,ra.jsxs)("div",{className:"space-x-3",children:[(0,ra.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{c(!1),b()},children:"Vazge\xe7"}),(0,ra.jsx)("button",{type:"submit",className:"btn-primary",children:"Kaydet"})]})]})]})]})})})})]})})]})},Ds=["title","titleId"];function Ms(e,t){let{title:n,titleId:r}=e,l=o(e,Ds);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.19 8.688a4.5 4.5 0 0 1 1.242 7.244l-4.5 4.5a4.5 4.5 0 0 1-6.364-6.364l1.757-1.757m13.35-.622 1.757-1.757a4.5 4.5 0 0 0-6.364-6.364l-4.5 4.5a4.5 4.5 0 0 0 1.242 7.244"}))}const Is=a.forwardRef(Ms),Us=["title","titleId"];function Bs(e,t){let{title:n,titleId:r}=e,l=o(e,Us);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m9.75 9.75 4.5 4.5m0-4.5-4.5 4.5M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const Hs=a.forwardRef(Bs),Ws=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)([]),[l,i]=(0,a.useState)(!1),[o,s]=(0,a.useState)(""),[u,c]=(0,a.useState)(""),[d,m]=(0,a.useState)("all"),[p,h]=(0,a.useState)(!1),[v,g]=(0,a.useState)(null),[b,y]=(0,a.useState)({lessonId:"",parentId:"",title:"",description:"",isActive:!0});(0,a.useEffect)(()=>{(async()=>{try{var e;const n=await Zr("/lessons?limit=200");t(null!==(e=n.data)&&void 0!==e?e:[])}catch(n){console.error("Lessons load failed",n)}})()},[]);const x=async()=>{try{var e;i(!0);const t=new URLSearchParams;t.append("limit","200"),o.trim()&&t.append("q",o.trim()),u&&t.append("lesson_id",u),"active"===d&&t.append("is_active","true"),"inactive"===d&&t.append("is_active","false");const n=await Zr("/subjects?".concat(t.toString()));r(null!==(e=n.data)&&void 0!==e?e:[])}catch(t){console.error("Subjects load failed",t)}finally{i(!1)}};(0,a.useEffect)(()=>{x()},[u,d]);const w=(0,a.useMemo)(()=>{if(!o.trim())return n;const e=o.trim().toLowerCase();return n.filter(t=>{var n;return t.title.toLowerCase().includes(e)||(null!==(n=t.description)&&void 0!==n?n:"").toLowerCase().includes(e)})},[n,o]),k=()=>{y({lessonId:"",parentId:"",title:"",description:"",isActive:!0}),g(null)},S=(0,a.useMemo)(()=>{const e=new Map;return n.forEach(t=>{var n;const r=null!==(n=e.get(t.lesson_id))&&void 0!==n?n:[];r.push(t),e.set(t.lesson_id,r)}),e},[n]),j=(0,a.useMemo)(()=>{var e;return b.lessonId?(null!==(e=S.get(b.lessonId))&&void 0!==e?e:[]).filter(e=>e.id!==(null===v||void 0===v?void 0:v.id)):[]},[S,b.lessonId,v]),N=t=>{var n,r;return null!==(n=null===(r=e.find(e=>e.id===t))||void 0===r?void 0:r.title)&&void 0!==n?n:"-"},E=e=>{var t,r;return null!==(t=null===(r=n.find(t=>t.id===e))||void 0===r?void 0:r.title)&&void 0!==t?t:"-"};return(0,ra.jsxs)(nl,{children:[(0,ra.jsxs)("div",{className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-2xl font-bold text-gray-100",children:"Konular"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-gray-300",children:"Derslere ba\u011fl\u0131 konu ba\u015fl\u0131klar\u0131n\u0131 y\xf6netin, hiyerar\u015fi tan\u0131mlay\u0131n."})]}),(0,ra.jsxs)("div",{className:"flex items-center space-x-3 mt-4 sm:mt-0",children:[(0,ra.jsxs)("div",{className:"inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200",children:[(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("all"===d?"bg-slate-700 text-white":""),onClick:()=>m("all"),children:"T\xfcm\xfc"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("active"===d?"bg-slate-700 text-white":""),onClick:()=>m("active"),children:"Aktif"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("inactive"===d?"bg-slate-700 text-white":""),onClick:()=>m("inactive"),children:"Pasif"})]}),(0,ra.jsxs)("button",{className:"btn-primary flex items-center",onClick:()=>{k(),h(!0)},children:[(0,ra.jsx)(sl,{className:"h-4 w-4 mr-2"}),"Yeni Konu"]})]})]}),(0,ra.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,ra.jsxs)("div",{className:"card flex items-center space-x-3",children:[(0,ra.jsx)(fl,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsx)("input",{className:"input-field",placeholder:"Konu ara...",value:o,onChange:e=>s(e.target.value)})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders Filtrele"}),(0,ra.jsxs)("select",{className:"input-field",value:u,onChange:e=>c(e.target.value),children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),e.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Durum"}),(0,ra.jsxs)("select",{className:"input-field",value:d,onChange:e=>m(e.target.value),children:[(0,ra.jsx)("option",{value:"all",children:"T\xfcm\xfc"}),(0,ra.jsx)("option",{value:"active",children:"Aktif"}),(0,ra.jsx)("option",{value:"inactive",children:"Pasif"})]})]})]}),(0,ra.jsx)("div",{className:"card p-0",children:l?(0,ra.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,ra.jsx)("div",{className:"animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500"}),(0,ra.jsx)("span",{className:"ml-3 text-gray-300",children:"Konular y\xfckleniyor..."})]}):0===w.length?(0,ra.jsx)("div",{className:"py-12 text-center text-gray-400",children:"Kriterlere uygun konu bulunamad\u0131."}):(0,ra.jsx)("div",{className:"table-container",children:(0,ra.jsxs)("table",{className:"min-w-full divide-y divide-slate-700",children:[(0,ra.jsx)("thead",{className:"bg-slate-800/80",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"table-header",children:"Konu"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Ders"}),(0,ra.jsx)("th",{className:"table-header hidden lg:table-cell",children:"\xdcst Konu"}),(0,ra.jsx)("th",{className:"table-header hidden sm:table-cell",children:"Durum"}),(0,ra.jsx)("th",{className:"table-header",children:"\u0130\u015flemler"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-800 bg-slate-900/40",children:w.map(e=>(0,ra.jsxs)("tr",{className:"hover:bg-slate-800/60",children:[(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,ra.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center",children:(0,ra.jsx)(ja,{className:"h-5 w-5 text-primary-400"})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("p",{className:"text-sm font-medium text-white",children:e.title}),e.description&&(0,ra.jsx)("p",{className:"text-xs text-gray-400 truncate max-w-xs",children:e.description})]})]})}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:N(e.lesson_id)}),(0,ra.jsxs)("td",{className:"table-cell hidden lg:table-cell text-sm text-gray-300 flex items-center gap-2",children:[(0,ra.jsx)(Is,{className:"h-4 w-4 text-gray-400"}),(0,ra.jsx)("span",{children:E(e.main_subject_id)})]}),(0,ra.jsx)("td",{className:"table-cell hidden sm:table-cell",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ".concat(e.is_active?"bg-emerald-500/20 text-emerald-300":"bg-rose-500/20 text-rose-300"),children:e.is_active?(0,ra.jsxs)(ra.Fragment,{children:[(0,ra.jsx)(bl,{className:"h-4 w-4 mr-1"}),"Aktif"]}):(0,ra.jsxs)(ra.Fragment,{children:[(0,ra.jsx)(Hs,{className:"h-4 w-4 mr-1"}),"Pasif"]})})}),(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>(e=>{var t,n;g(e),y({lessonId:e.lesson_id,parentId:null!==(t=e.main_subject_id)&&void 0!==t?t:"",title:e.title,description:null!==(n=e.description)&&void 0!==n?n:"",isActive:e.is_active}),h(!0)})(e),className:"icon-button text-primary-400 hover:text-primary-200",children:(0,ra.jsx)(wl,{className:"h-4 w-4"})}),(0,ra.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Bu konuyu silmek istedi\u011finizden emin misiniz?"))try{await na("/subjects/".concat(e)),await x()}catch(t){console.error("Subject delete failed",t),alert("Konu silinemedi.")}})(e.id),className:"icon-button text-red-400 hover:text-red-200",children:(0,ra.jsx)(jl,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,ra.jsx)(wi,{show:p,as:a.Fragment,children:(0,ra.jsxs)(Fs,{as:"div",className:"relative z-50",onClose:()=>h(!1),children:[(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,ra.jsx)("div",{className:"fixed inset-0 bg-black/60"})}),(0,ra.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,ra.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-150",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,ra.jsxs)(Fs.Panel,{className:"w-full max-w-3xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all",children:[(0,ra.jsx)(Fs.Title,{className:"text-xl font-semibold text-white mb-4",children:v?"Konuyu D\xfczenle":"Yeni Konu Olu\u015ftur"}),(0,ra.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t={lesson_id:b.lessonId,main_subject_id:b.parentId||null,title:b.title.trim(),description:b.description.trim()||null,is_active:b.isActive};try{v?await ta("/subjects/".concat(v.id),t):await ea("/subjects",t),h(!1),k(),await x()}catch(n){console.error("Subject save failed",n),alert("Konu kaydedilirken bir hata olu\u015ftu.")}},className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders"}),(0,ra.jsxs)("select",{className:"input-field",value:b.lessonId,onChange:e=>y(t=>f(f({},t),{},{lessonId:e.target.value,parentId:""})),required:!0,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),e.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"\xdcst Konu (Opsiyonel)"}),(0,ra.jsxs)("select",{className:"input-field",value:b.parentId,onChange:e=>y(t=>f(f({},t),{},{parentId:e.target.value})),disabled:!b.lessonId,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),j.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ba\u015fl\u0131k"}),(0,ra.jsx)("input",{className:"input-field",value:b.title,onChange:e=>y(t=>f(f({},t),{},{title:e.target.value})),required:!0})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"A\xe7\u0131klama"}),(0,ra.jsx)("textarea",{className:"input-field",rows:3,value:b.description,onChange:e=>y(t=>f(f({},t),{},{description:e.target.value})),placeholder:"Opsiyonel"})]}),(0,ra.jsxs)("div",{className:"flex items-center justify-between",children:[(0,ra.jsxs)("label",{className:"inline-flex items-center space-x-2 text-sm text-gray-200",children:[(0,ra.jsx)("input",{type:"checkbox",className:"form-checkbox",checked:b.isActive,onChange:e=>y(t=>f(f({},t),{},{isActive:e.target.checked}))}),(0,ra.jsx)("span",{children:"Konu aktif olsun"})]}),(0,ra.jsxs)("div",{className:"space-x-3",children:[(0,ra.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{h(!1),k()},children:"Vazge\xe7"}),(0,ra.jsx)("button",{type:"submit",className:"btn-primary",children:"Kaydet"})]})]})]})]})})})})]})})]})},qs=["title","titleId"];function Vs(e,t){let{title:n,titleId:r}=e,l=o(e,qs);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"}))}const Ks=a.forwardRef(Vs),$s=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)([]),[l,i]=(0,a.useState)([]),[o,s]=(0,a.useState)(!1),[u,c]=(0,a.useState)(""),[d,m]=(0,a.useState)("all"),[p,h]=(0,a.useState)(""),[v,g]=(0,a.useState)(""),[b,y]=(0,a.useState)(!1),[x,w]=(0,a.useState)(null),[k,S]=(0,a.useState)({lessonId:"",subjectId:"",title:"",summary:"",body:"",isPublished:!0});(0,a.useEffect)(()=>{(async()=>{try{var e,t;const[n,a]=await Promise.all([Zr("/lessons?limit=200"),Zr("/subjects?limit=200")]);r(null!==(e=n.data)&&void 0!==e?e:[]),i(null!==(t=a.data)&&void 0!==t?t:[])}catch(n){console.error("Lookup load failed",n)}})()},[]);const j=async()=>{try{var e;s(!0);const n=new URLSearchParams;n.append("limit","200"),u.trim()&&n.append("q",u.trim()),p&&n.append("lesson_id",p),v&&n.append("subject_id",v),"published"===d&&n.append("is_published","true"),"draft"===d&&n.append("is_published","false");const r=await Zr("/explanations?".concat(n.toString()));t(null!==(e=r.data)&&void 0!==e?e:[])}catch(n){console.error("Explanations load failed",n)}finally{s(!1)}};(0,a.useEffect)(()=>{j()},[d,p,v]);const N=(0,a.useMemo)(()=>{if(!u.trim())return e;const t=u.trim().toLowerCase();return e.filter(e=>{var n;return e.title.toLowerCase().includes(t)||(null!==(n=e.summary)&&void 0!==n?n:"").toLowerCase().includes(t)})},[e,u]),E=()=>{S({lessonId:"",subjectId:"",title:"",summary:"",body:"",isPublished:!0}),w(null)},C=(0,a.useMemo)(()=>k.lessonId?l.filter(e=>e.lesson_id===k.lessonId):l,[l,k.lessonId]);return(0,ra.jsxs)(nl,{children:[(0,ra.jsxs)("div",{className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-2xl font-bold text-gray-100",children:"A\xe7\u0131klamalar"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-gray-300",children:"Ders ve konu bazl\u0131 a\xe7\u0131klamalar\u0131 y\xf6netin, i\xe7erikleri d\xfczenleyin."})]}),(0,ra.jsxs)("div",{className:"flex items-center space-x-3 mt-4 sm:mt-0",children:[(0,ra.jsxs)("div",{className:"inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200",children:[(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("all"===d?"bg-slate-700 text-white":""),onClick:()=>m("all"),children:"T\xfcm\xfc"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("published"===d?"bg-slate-700 text-white":""),onClick:()=>m("published"),children:"Yay\u0131nda"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("draft"===d?"bg-slate-700 text-white":""),onClick:()=>m("draft"),children:"Taslak"})]}),(0,ra.jsxs)("button",{className:"btn-primary flex items-center",onClick:()=>{E(),y(!0)},children:[(0,ra.jsx)(sl,{className:"h-4 w-4 mr-2"}),"Yeni A\xe7\u0131klama"]})]})]}),(0,ra.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,ra.jsxs)("div",{className:"card flex items-center space-x-3",children:[(0,ra.jsx)(fl,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsx)("input",{className:"input-field",placeholder:"A\xe7\u0131klama ara...",value:u,onChange:e=>c(e.target.value)})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders Filtrele"}),(0,ra.jsxs)("select",{className:"input-field",value:p,onChange:e=>{h(e.target.value),g("")},children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),n.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Konu Filtrele"}),(0,ra.jsxs)("select",{className:"input-field",value:v,onChange:e=>g(e.target.value),disabled:!p,children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),l.filter(e=>!p||e.lesson_id===p).map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]})]}),(0,ra.jsx)("div",{className:"card p-0",children:o?(0,ra.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,ra.jsx)("div",{className:"animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500"}),(0,ra.jsx)("span",{className:"ml-3 text-gray-300",children:"A\xe7\u0131klamalar y\xfckleniyor..."})]}):0===N.length?(0,ra.jsx)("div",{className:"py-12 text-center text-gray-400",children:"Kriterlere uygun a\xe7\u0131klama bulunamad\u0131."}):(0,ra.jsx)("div",{className:"table-container",children:(0,ra.jsxs)("table",{className:"min-w-full divide-y divide-slate-700",children:[(0,ra.jsx)("thead",{className:"bg-slate-800/80",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"table-header",children:"A\xe7\u0131klama"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Ders"}),(0,ra.jsx)("th",{className:"table-header hidden lg:table-cell",children:"Konu"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Durum"}),(0,ra.jsx)("th",{className:"table-header",children:"\u0130\u015flemler"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-800 bg-slate-900/40",children:N.map(e=>{var t,r;const a=n.find(t=>t.id===e.lesson_id),i=l.find(t=>t.id===e.subject_id);return(0,ra.jsxs)("tr",{className:"hover:bg-slate-800/60",children:[(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,ra.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center",children:(0,ra.jsx)(Ca,{className:"h-5 w-5 text-primary-400"})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("p",{className:"text-sm font-medium text-white",children:e.title}),e.summary&&(0,ra.jsx)("p",{className:"text-xs text-gray-400 truncate max-w-xs",children:e.summary})]})]})}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:null!==(t=null===a||void 0===a?void 0:a.title)&&void 0!==t?t:"-"}),(0,ra.jsxs)("td",{className:"table-cell hidden lg:table-cell text-sm text-gray-300 flex items-center gap-2",children:[(0,ra.jsx)(Ks,{className:"h-4 w-4 text-gray-400"}),(0,ra.jsx)("span",{children:null!==(r=null===i||void 0===i?void 0:i.title)&&void 0!==r?r:"-"})]}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ".concat(e.is_published?"bg-emerald-500/20 text-emerald-300":"bg-amber-500/20 text-amber-300"),children:e.is_published?"Yay\u0131nda":"Taslak"})}),(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>(e=>{var t,n;w(e),S({lessonId:e.lesson_id,subjectId:e.subject_id,title:e.title,summary:null!==(t=e.summary)&&void 0!==t?t:"",body:null!==(n=e.body)&&void 0!==n?n:"",isPublished:e.is_published}),y(!0)})(e),className:"icon-button text-primary-400 hover:text-primary-200",children:(0,ra.jsx)(wl,{className:"h-4 w-4"})}),(0,ra.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Bu a\xe7\u0131klamay\u0131 silmek istedi\u011finizden emin misiniz?"))try{await na("/explanations/".concat(e)),await j()}catch(t){console.error("Explanation delete failed",t),alert("A\xe7\u0131klama silinemedi.")}})(e.id),className:"icon-button text-red-400 hover:text-red-200",children:(0,ra.jsx)(jl,{className:"h-4 w-4"})})]})})]},e.id)})})]})})})]}),(0,ra.jsx)(wi,{show:b,as:a.Fragment,children:(0,ra.jsxs)(Fs,{as:"div",className:"relative z-50",onClose:()=>y(!1),children:[(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,ra.jsx)("div",{className:"fixed inset-0 bg-black/60"})}),(0,ra.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,ra.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-150",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,ra.jsxs)(Fs.Panel,{className:"w-full max-w-4xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all",children:[(0,ra.jsx)(Fs.Title,{className:"text-xl font-semibold text-white mb-4",children:x?"A\xe7\u0131klamay\u0131 D\xfczenle":"Yeni A\xe7\u0131klama Olu\u015ftur"}),(0,ra.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t={lesson_id:k.lessonId,subject_id:k.subjectId,title:k.title.trim(),summary:k.summary.trim()||null,body:k.body.trim()||null,is_published:k.isPublished};try{x?await ta("/explanations/".concat(x.id),t):await ea("/explanations",t),y(!1),E(),await j()}catch(n){console.error("Explanation save failed",n),alert("A\xe7\u0131klama kaydedilirken bir hata olu\u015ftu.")}},className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders"}),(0,ra.jsxs)("select",{className:"input-field",value:k.lessonId,onChange:e=>S(t=>f(f({},t),{},{lessonId:e.target.value,subjectId:""})),required:!0,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),n.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Konu"}),(0,ra.jsxs)("select",{className:"input-field",value:k.subjectId,onChange:e=>S(t=>f(f({},t),{},{subjectId:e.target.value})),required:!0,disabled:!k.lessonId,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),C.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ba\u015fl\u0131k"}),(0,ra.jsx)("input",{className:"input-field",value:k.title,onChange:e=>S(t=>f(f({},t),{},{title:e.target.value})),required:!0})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"\xd6zet"}),(0,ra.jsx)("textarea",{className:"input-field",rows:2,value:k.summary,onChange:e=>S(t=>f(f({},t),{},{summary:e.target.value})),placeholder:"Opsiyonel"})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"\u0130\xe7erik"}),(0,ra.jsx)("textarea",{className:"input-field",rows:6,value:k.body,onChange:e=>S(t=>f(f({},t),{},{body:e.target.value}))})]}),(0,ra.jsxs)("div",{className:"flex items-center justify-between",children:[(0,ra.jsxs)("label",{className:"inline-flex items-center space-x-2 text-sm text-gray-200",children:[(0,ra.jsx)("input",{type:"checkbox",className:"form-checkbox",checked:k.isPublished,onChange:e=>S(t=>f(f({},t),{},{isPublished:e.target.checked}))}),(0,ra.jsx)("span",{children:"A\xe7\u0131klama yay\u0131mlans\u0131n"})]}),(0,ra.jsxs)("div",{className:"space-x-3",children:[(0,ra.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{y(!1),E()},children:"Vazge\xe7"}),(0,ra.jsx)("button",{type:"submit",className:"btn-primary",children:"Kaydet"})]})]})]})]})})})})]})})]})},Qs=["title","titleId"];function Ys(e,t){let{title:n,titleId:r}=e,l=o(e,Qs);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z"}),a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 6h.008v.008H6V6Z"}))}const Gs=a.forwardRef(Ys),Xs=[{value:"single_choice",label:"Tek \u015e\u0131k"},{value:"multiple_choice",label:"\xc7oktan Se\xe7meli"},{value:"true_false",label:"Do\u011fru / Yanl\u0131\u015f"},{value:"fill_blank",label:"Bo\u015fluk Doldurma"}],Js=[{value:"easy",label:"Kolay"},{value:"medium",label:"Orta"},{value:"hard",label:"Zor"}],Zs=()=>{var e;const[t,n]=(0,a.useState)([]),[r,l]=(0,a.useState)([]),[i,o]=(0,a.useState)([]),[s,u]=(0,a.useState)({}),[c,d]=(0,a.useState)(!1),[m,p]=(0,a.useState)(""),[h,v]=(0,a.useState)(""),[g,b]=(0,a.useState)(""),[y,x]=(0,a.useState)(""),[w,k]=(0,a.useState)(""),[S,j]=(0,a.useState)("all"),[N,E]=(0,a.useState)(!1),[C,P]=(0,a.useState)(null),[T,O]=(0,a.useState)({lessonId:"",subjectId:"",explanationId:"",type:"single_choice",difficulty:"medium",source:"",stem:"",isPublished:!0});(0,a.useEffect)(()=>{(async()=>{try{var e,t;const[n,r]=await Promise.all([Zr("/lessons?limit=200"),Zr("/subjects?limit=200")]);l(null!==(e=n.data)&&void 0!==e?e:[]),o(null!==(t=r.data)&&void 0!==t?t:[])}catch(n){console.error("Lookup load failed",n)}})()},[]);const L=async()=>{try{var e;d(!0);const t=new URLSearchParams;t.append("limit","200"),m.trim()&&t.append("q",m.trim()),h&&t.append("lesson_id",h),g&&t.append("subject_id",g),y&&t.append("type",y),w&&t.append("difficulty",w),"published"===S&&t.append("is_published","true"),"draft"===S&&t.append("is_published","false");const r=await Zr("/questions?".concat(t.toString()));n(null!==(e=r.data)&&void 0!==e?e:[])}catch(t){console.error("Questions load failed",t)}finally{d(!1)}};(0,a.useEffect)(()=>{L()},[h,g,y,w,S]);const R=(0,a.useMemo)(()=>{if(!m.trim())return t;const e=m.trim().toLowerCase();return t.filter(t=>{var n;return t.stem.toLowerCase().includes(e)||(null!==(n=t.source)&&void 0!==n?n:"").toLowerCase().includes(e)})},[t,m]),_=e=>{var t,n;return null!==(t=null===(n=r.find(t=>t.id===e))||void 0===n?void 0:n.title)&&void 0!==t?t:"-"},A=e=>{var t,n;return null!==(t=null===(n=i.find(t=>t.id===e))||void 0===n?void 0:n.title)&&void 0!==t?t:"-"},F=e=>{var t,n;if(!e)return"-";return null!==(t=null===(n=Object.values(s).flat().find(t=>t.id===e))||void 0===n?void 0:n.title)&&void 0!==t?t:"-"},z=async e=>{if(e&&!s[e])try{const t=await Zr("/explanations?subject_id=".concat(e,"&limit=200"));u(n=>{var r;return f(f({},n),{},{[e]:null!==(r=t.data)&&void 0!==r?r:[]})})}catch(t){console.error("Explanations load failed",t)}},D=()=>{O({lessonId:"",subjectId:"",explanationId:"",type:"single_choice",difficulty:"medium",source:"",stem:"",isPublished:!0}),P(null)},M=(0,a.useMemo)(()=>T.lessonId?i.filter(e=>e.lesson_id===T.lessonId):i,[i,T.lessonId]),I=(0,a.useMemo)(()=>h?i.filter(e=>e.lesson_id===h):i,[i,h]),U=T.subjectId&&null!==(e=s[T.subjectId])&&void 0!==e?e:[];return(0,ra.jsxs)(nl,{children:[(0,ra.jsxs)("div",{className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-2xl font-bold text-gray-100",children:"Sorular"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-gray-300",children:"Ders ve konu bazl\u0131 soru bankan\u0131z\u0131 y\xf6netin."})]}),(0,ra.jsxs)("div",{className:"flex items-center space-x-3 mt-4 sm:mt-0",children:[(0,ra.jsxs)("div",{className:"inline-flex rounded-lg border border-slate-700 bg-slate-800 text-sm text-slate-200",children:[(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("all"===S?"bg-slate-700 text-white":""),onClick:()=>j("all"),children:"T\xfcm\xfc"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("published"===S?"bg-slate-700 text-white":""),onClick:()=>j("published"),children:"Yay\u0131nda"}),(0,ra.jsx)("button",{className:"px-3 py-1.5 ".concat("draft"===S?"bg-slate-700 text-white":""),onClick:()=>j("draft"),children:"Taslak"})]}),(0,ra.jsxs)("button",{className:"btn-primary flex items-center",onClick:()=>{D(),E(!0)},children:[(0,ra.jsx)(sl,{className:"h-4 w-4 mr-2"}),"Yeni Soru"]})]})]}),(0,ra.jsxs)("div",{className:"grid gap-4 lg:grid-cols-5 md:grid-cols-3 sm:grid-cols-2",children:[(0,ra.jsxs)("div",{className:"card flex items-center space-x-3 lg:col-span-2",children:[(0,ra.jsx)(fl,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsx)("input",{className:"input-field",placeholder:"Soru ara...",value:m,onChange:e=>p(e.target.value)})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders"}),(0,ra.jsxs)("select",{className:"input-field",value:h,onChange:e=>{v(e.target.value),b("")},children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),r.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Konu"}),(0,ra.jsxs)("select",{className:"input-field",value:g,onChange:e=>b(e.target.value),disabled:!h,children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),I.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Soru Tipi"}),(0,ra.jsxs)("select",{className:"input-field",value:y,onChange:e=>x(e.target.value),children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),Xs.map(e=>(0,ra.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,ra.jsxs)("div",{className:"card",children:[(0,ra.jsx)("label",{className:"form-label",children:"Zorluk"}),(0,ra.jsxs)("select",{className:"input-field",value:w,onChange:e=>k(e.target.value),children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm\xfc"}),Js.map(e=>(0,ra.jsx)("option",{value:e.value,children:e.label},e.value))]})]})]}),(0,ra.jsx)("div",{className:"card p-0",children:c?(0,ra.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,ra.jsx)("div",{className:"animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500"}),(0,ra.jsx)("span",{className:"ml-3 text-gray-300",children:"Sorular y\xfckleniyor..."})]}):0===R.length?(0,ra.jsx)("div",{className:"py-12 text-center text-gray-400",children:"Kriterlere uygun soru bulunamad\u0131."}):(0,ra.jsx)("div",{className:"table-container",children:(0,ra.jsxs)("table",{className:"min-w-full divide-y divide-slate-700",children:[(0,ra.jsx)("thead",{className:"bg-slate-800/80",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"table-header",children:"Soru"}),(0,ra.jsx)("th",{className:"table-header hidden lg:table-cell",children:"Ders / Konu"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Tip & Zorluk"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"A\xe7\u0131klama"}),(0,ra.jsx)("th",{className:"table-header hidden sm:table-cell",children:"Durum"}),(0,ra.jsx)("th",{className:"table-header",children:"\u0130\u015flemler"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-800 bg-slate-900/40",children:R.map(e=>{var t,n,r,a;return(0,ra.jsxs)("tr",{className:"hover:bg-slate-800/60",children:[(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,ra.jsx)("div",{className:"h-10 w-10 rounded-full bg-primary-500/20 flex items-center justify-center",children:(0,ra.jsx)(Oa,{className:"h-5 w-5 text-primary-400"})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("p",{className:"text-sm font-medium text-white line-clamp-2 max-w-md",children:e.stem}),e.source&&(0,ra.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:["Kaynak: ",e.source]})]})]})}),(0,ra.jsx)("td",{className:"table-cell hidden lg:table-cell text-sm text-gray-300",children:(0,ra.jsxs)("div",{className:"space-y-1",children:[(0,ra.jsx)("p",{children:_(e.lesson_id)}),(0,ra.jsx)("p",{className:"text-xs text-gray-400",children:A(e.subject_id)})]})}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:(0,ra.jsxs)("div",{className:"space-y-1",children:[(0,ra.jsxs)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold bg-slate-700/70 text-slate-200",children:[(0,ra.jsx)(Gs,{className:"h-4 w-4 mr-1"}),null!==(t=null===(n=Xs.find(t=>t.value===e.type))||void 0===n?void 0:n.label)&&void 0!==t?t:e.type]}),(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ".concat("easy"===e.difficulty?"bg-emerald-500/20 text-emerald-300":"medium"===e.difficulty?"bg-amber-500/20 text-amber-300":"bg-rose-500/20 text-rose-300"),children:null!==(r=null===(a=Js.find(t=>t.value===e.difficulty))||void 0===a?void 0:a.label)&&void 0!==r?r:e.difficulty})]})}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:F(e.explanation_id)}),(0,ra.jsx)("td",{className:"table-cell hidden sm:table-cell",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-semibold ".concat(e.is_published?"bg-emerald-500/20 text-emerald-300":"bg-amber-500/20 text-amber-300"),children:e.is_published?"Yay\u0131nda":"Taslak"})}),(0,ra.jsx)("td",{className:"table-cell",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>(async e=>{var t,n;P(e),await z(e.subject_id),O({lessonId:e.lesson_id,subjectId:e.subject_id,explanationId:null!==(t=e.explanation_id)&&void 0!==t?t:"",type:e.type,difficulty:e.difficulty,source:null!==(n=e.source)&&void 0!==n?n:"",stem:e.stem,isPublished:e.is_published}),E(!0)})(e),className:"icon-button text-primary-400 hover:text-primary-200",children:(0,ra.jsx)(wl,{className:"h-4 w-4"})}),(0,ra.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Bu soruyu silmek istedi\u011finizden emin misiniz?"))try{await na("/questions/".concat(e)),await L()}catch(t){console.error("Question delete failed",t),alert("Soru silinemedi.")}})(e.id),className:"icon-button text-red-400 hover:text-red-200",children:(0,ra.jsx)(jl,{className:"h-4 w-4"})})]})})]},e.id)})})]})})})]}),(0,ra.jsx)(wi,{show:N,as:a.Fragment,children:(0,ra.jsxs)(Fs,{as:"div",className:"relative z-50",onClose:()=>E(!1),children:[(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0",enterTo:"opacity-100",leave:"ease-in duration-150",leaveFrom:"opacity-100",leaveTo:"opacity-0",children:(0,ra.jsx)("div",{className:"fixed inset-0 bg-black/60"})}),(0,ra.jsx)("div",{className:"fixed inset-0 overflow-y-auto",children:(0,ra.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,ra.jsx)(wi.Child,{as:a.Fragment,enter:"ease-out duration-200",enterFrom:"opacity-0 scale-95",enterTo:"opacity-100 scale-100",leave:"ease-in duration-150",leaveFrom:"opacity-100 scale-100",leaveTo:"opacity-0 scale-95",children:(0,ra.jsxs)(Fs.Panel,{className:"w-full max-w-4xl transform overflow-hidden rounded-2xl bg-slate-900 border border-slate-700 p-6 shadow-2xl transition-all",children:[(0,ra.jsx)(Fs.Title,{className:"text-xl font-semibold text-white mb-4",children:C?"Soruyu D\xfczenle":"Yeni Soru Olu\u015ftur"}),(0,ra.jsxs)("form",{onSubmit:async e=>{e.preventDefault();const t={lesson_id:T.lessonId,subject_id:T.subjectId,explanation_id:T.explanationId||null,type:T.type,difficulty:T.difficulty,source:T.source.trim()||null,stem:T.stem.trim(),is_published:T.isPublished};try{C?await ta("/questions/".concat(C.id),t):await ea("/questions",t),E(!1),D(),await L()}catch(n){console.error("Question save failed",n),alert("Soru kaydedilirken bir hata olu\u015ftu.")}},className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Ders"}),(0,ra.jsxs)("select",{className:"input-field",value:T.lessonId,onChange:e=>{O(t=>f(f({},t),{},{lessonId:e.target.value,subjectId:"",explanationId:""}))},required:!0,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),r.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Konu"}),(0,ra.jsxs)("select",{className:"input-field",value:T.subjectId,onChange:async e=>{const t=e.target.value;O(e=>f(f({},e),{},{subjectId:t,explanationId:""})),t&&await z(t)},required:!0,disabled:!T.lessonId,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),M.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"A\xe7\u0131klama (Opsiyonel)"}),(0,ra.jsxs)("select",{className:"input-field",value:T.explanationId,onChange:e=>O(t=>f(f({},t),{},{explanationId:e.target.value})),disabled:!T.subjectId,children:[(0,ra.jsx)("option",{value:"",children:"Se\xe7iniz"}),U.map(e=>(0,ra.jsx)("option",{value:e.id,children:e.title},e.id))]})]}),(0,ra.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Soru Tipi"}),(0,ra.jsx)("select",{className:"input-field",value:T.type,onChange:e=>O(t=>f(f({},t),{},{type:e.target.value})),required:!0,children:Xs.map(e=>(0,ra.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Zorluk"}),(0,ra.jsx)("select",{className:"input-field",value:T.difficulty,onChange:e=>O(t=>f(f({},t),{},{difficulty:e.target.value})),required:!0,children:Js.map(e=>(0,ra.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Soru Metni"}),(0,ra.jsx)("textarea",{className:"input-field",rows:5,value:T.stem,onChange:e=>O(t=>f(f({},t),{},{stem:e.target.value})),required:!0})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"form-label",children:"Kaynak (Opsiyonel)"}),(0,ra.jsx)("input",{className:"input-field",value:T.source,onChange:e=>O(t=>f(f({},t),{},{source:e.target.value})),placeholder:"Kitap, deneme, y\u0131l vb."})]}),(0,ra.jsxs)("div",{className:"flex items-center justify-between",children:[(0,ra.jsxs)("label",{className:"inline-flex items-center space-x-2 text-sm text-gray-200",children:[(0,ra.jsx)("input",{type:"checkbox",className:"form-checkbox",checked:T.isPublished,onChange:e=>O(t=>f(f({},t),{},{isPublished:e.target.checked}))}),(0,ra.jsx)("span",{children:"Soru yay\u0131mlans\u0131n"})]}),(0,ra.jsxs)("div",{className:"space-x-3",children:[(0,ra.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{E(!1),D()},children:"Vazge\xe7"}),(0,ra.jsx)("button",{type:"submit",className:"btn-primary",children:"Kaydet"})]})]})]})]})})})})]})})]})},eu=["title","titleId"];function tu(e,t){let{title:n,titleId:r}=e,l=o(e,eu);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 0 1-.659 1.591l-5.432 5.432a2.25 2.25 0 0 0-.659 1.591v2.927a2.25 2.25 0 0 1-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 0 0-.659-1.591L3.659 7.409A2.25 2.25 0 0 1 3 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0 1 12 3Z"}))}const nu=a.forwardRef(tu),ru=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)(!1),[l,i]=(0,a.useState)(""),[o,s]=(0,a.useState)(""),[u,c]=(0,a.useState)(""),d=async()=>{try{var e;r(!0);const n=new URLSearchParams;n.append("limit","200"),o&&n.append("entity",o),u&&n.append("type",u),l.trim()&&n.append("q",l.trim());const a=await Zr("/logs?".concat(n.toString()));t(null!==(e=a.data)&&void 0!==e?e:[])}catch(n){console.error("Logs load failed",n)}finally{r(!1)}};(0,a.useEffect)(()=>{d()},[o,u]);const f=(0,a.useMemo)(()=>{if(!l.trim())return e;const t=l.trim().toLowerCase();return e.filter(e=>{var n,r;return e.title.toLowerCase().includes(t)||(null!==(n=e.message)&&void 0!==n?n:"").toLowerCase().includes(t)||(null!==(r=e.entity)&&void 0!==r?r:"").toLowerCase().includes(t)})},[e,l]),m=(0,a.useMemo)(()=>Array.from(new Set(e.map(e=>e.entity))).filter(Boolean),[e]),p=(0,a.useMemo)(()=>Array.from(new Set(e.map(e=>e.type))).filter(Boolean),[e]);return(0,ra.jsx)(nl,{children:(0,ra.jsxs)("div",{className:"space-y-6",children:[(0,ra.jsxs)("div",{className:"sm:flex sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-2xl font-bold text-gray-100",children:"Sistem Loglar\u0131"}),(0,ra.jsx)("p",{className:"mt-1 text-sm text-gray-300",children:"Uygulama olaylar\u0131n\u0131 takip edin, hatalar\u0131 h\u0131zl\u0131ca tespit edin."})]}),(0,ra.jsxs)("button",{className:"btn-secondary flex items-center space-x-2",onClick:d,children:[(0,ra.jsx)(_a,{className:"h-5 w-5"}),(0,ra.jsx)("span",{children:"Yenile"})]})]}),(0,ra.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,ra.jsxs)("div",{className:"card flex items-center space-x-3 md:col-span-2",children:[(0,ra.jsx)(fl,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsx)("input",{className:"input-field",placeholder:"Log ara...",value:l,onChange:e=>i(e.target.value)})]}),(0,ra.jsxs)("div",{className:"card flex items-center space-x-3",children:[(0,ra.jsx)(nu,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsxs)("select",{className:"input-field",value:o,onChange:e=>s(e.target.value),children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm Entity'ler"}),m.map(e=>(0,ra.jsx)("option",{value:e,children:e},e))]})]}),(0,ra.jsxs)("div",{className:"card flex items-center space-x-3",children:[(0,ra.jsx)(nu,{className:"h-5 w-5 text-gray-400"}),(0,ra.jsxs)("select",{className:"input-field",value:u,onChange:e=>c(e.target.value),children:[(0,ra.jsx)("option",{value:"",children:"T\xfcm Tipler"}),p.map(e=>(0,ra.jsx)("option",{value:e,children:e},e))]})]})]}),(0,ra.jsx)("div",{className:"card p-0",children:n?(0,ra.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,ra.jsx)("div",{className:"animate-spin h-10 w-10 rounded-full border-b-2 border-primary-500"}),(0,ra.jsx)("span",{className:"ml-3 text-gray-300",children:"Loglar y\xfckleniyor..."})]}):0===f.length?(0,ra.jsx)("div",{className:"py-12 text-center text-gray-400",children:"Kay\u0131t bulunamad\u0131."}):(0,ra.jsx)("div",{className:"table-container",children:(0,ra.jsxs)("table",{className:"min-w-full divide-y divide-slate-700",children:[(0,ra.jsx)("thead",{className:"bg-slate-800/80",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"table-header",children:"Ba\u015fl\u0131k"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Entity"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Tip"}),(0,ra.jsx)("th",{className:"table-header hidden lg:table-cell",children:"IP"}),(0,ra.jsx)("th",{className:"table-header",children:"Mesaj"}),(0,ra.jsx)("th",{className:"table-header hidden md:table-cell",children:"Zaman"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-800 bg-slate-900/40",children:f.map(e=>(0,ra.jsxs)("tr",{className:"hover:bg-slate-800/60",children:[(0,ra.jsx)("td",{className:"table-cell text-sm text-white font-medium",children:e.title}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-sm text-gray-300",children:e.entity||"-"}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell",children:e.type||"-"}),(0,ra.jsx)("td",{className:"table-cell hidden lg:table-cell text-sm text-gray-300",children:e.ip||"-"}),(0,ra.jsx)("td",{className:"table-cell text-xs text-gray-200 whitespace-pre-wrap max-w-xl",children:e.message}),(0,ra.jsx)("td",{className:"table-cell hidden md:table-cell text-xs text-gray-400",children:new Date(e.created_at).toLocaleString("tr-TR")})]},e.id))})]})})})]})})},au=["title","titleId"];function lu(e,t){let{title:n,titleId:r}=e,l=o(e,au);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const iu=a.forwardRef(lu),ou=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)(!0),[l,i]=(0,a.useState)(""),[o,s]=(0,a.useState)(0),[u,c]=(0,a.useState)(1),[d]=(0,a.useState)(20);(0,a.useEffect)(()=>{f()},[l,u]);const f=async()=>{try{r(!0);const e=new URLSearchParams;l&&e.append("q",l),e.append("limit",d.toString()),e.append("offset",((u-1)*d).toString());const n=await Zr("/users?".concat(e));t(n.data||[]),s(n.total||0)}catch(e){console.error("Error loading users:",e)}finally{r(!1)}},m=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),p=Math.ceil(o/d);return(0,ra.jsx)(nl,{children:(0,ra.jsxs)("div",{className:"space-y-8",children:[(0,ra.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"Kullan\u0131c\u0131lar"}),(0,ra.jsx)("p",{className:"mt-2 text-slate-300",children:"KPSS Plus kullan\u0131c\u0131lar\u0131n\u0131 y\xf6netin"})]}),(0,ra.jsx)("div",{className:"mt-4 sm:mt-0 flex items-center space-x-4",children:(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-xl px-4 py-2",children:(0,ra.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,ra.jsx)(za,{className:"h-5 w-5 text-blue-400"}),(0,ra.jsxs)("span",{className:"text-slate-300 font-medium",children:[o," Kullan\u0131c\u0131"]})]})})})]}),(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6",children:(0,ra.jsxs)("div",{className:"max-w-md",children:[(0,ra.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Kullan\u0131c\u0131 Ara"}),(0,ra.jsxs)("div",{className:"relative",children:[(0,ra.jsx)(fl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400"}),(0,ra.jsx)("input",{type:"text",placeholder:"\u0130sim, email veya kullan\u0131c\u0131 ad\u0131...",value:l,onChange:e=>i(e.target.value),className:"input-field pl-10"})]})]})}),(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl overflow-hidden",children:n?(0,ra.jsxs)("div",{className:"p-8 text-center",children:[(0,ra.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,ra.jsx)("p",{className:"mt-2 text-slate-400",children:"Y\xfckleniyor..."})]}):0===e.length?(0,ra.jsxs)("div",{className:"p-8 text-center",children:[(0,ra.jsx)(za,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,ra.jsx)("p",{className:"text-slate-400",children:"Kullan\u0131c\u0131 bulunamad\u0131."})]}):(0,ra.jsxs)(ra.Fragment,{children:[(0,ra.jsx)("div",{className:"overflow-x-auto",children:(0,ra.jsxs)("table",{className:"min-w-full",children:[(0,ra.jsx)("thead",{className:"bg-slate-900/50",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Kullan\u0131c\u0131"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"\u0130leti\u015fim"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Durum"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Son Giri\u015f"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Kay\u0131t Tarihi"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-700/50",children:e.map(e=>{var t;return(0,ra.jsxs)("tr",{className:"hover:bg-slate-700/30 transition-colors duration-200",children:[(0,ra.jsx)("td",{className:"px-6 py-4",children:(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4",children:(0,ra.jsx)("span",{className:"text-white font-semibold text-sm",children:null===(t=e.first_name||e.username)||void 0===t?void 0:t.charAt(0).toUpperCase()})}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("div",{className:"text-sm font-medium text-slate-200",children:e.first_name&&e.last_name?"".concat(e.first_name," ").concat(e.last_name):e.username}),(0,ra.jsxs)("div",{className:"text-sm text-slate-400",children:["@",e.username]})]})]})}),(0,ra.jsxs)("td",{className:"px-6 py-4",children:[(0,ra.jsx)("div",{className:"text-sm text-slate-300",children:e.email}),e.phone&&(0,ra.jsx)("div",{className:"text-sm text-slate-400",children:e.phone})]}),(0,ra.jsx)("td",{className:"px-6 py-4",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.is_active?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:e.is_active?"Aktif":"Pasif"})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-sm text-slate-300",children:e.last_login?(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)(iu,{className:"h-4 w-4 text-slate-400 mr-1"}),m(e.last_login)]}):(0,ra.jsx)("span",{className:"text-slate-500",children:"Hi\xe7 giri\u015f yapmam\u0131\u015f"})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-sm text-slate-300",children:(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)(iu,{className:"h-4 w-4 text-slate-400 mr-1"}),m(e.created_at)]})})]},e.id)})})]})}),p>1&&(0,ra.jsxs)("div",{className:"bg-slate-900/50 px-6 py-4 flex items-center justify-between",children:[(0,ra.jsxs)("div",{className:"text-sm text-slate-400",children:[(u-1)*d+1," - ",Math.min(u*d,o)," / ",o," kullan\u0131c\u0131"]}),(0,ra.jsxs)("div",{className:"flex space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>c(Math.max(1,u-1)),disabled:1===u,className:"px-3 py-1 text-sm bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:"\xd6nceki"}),(0,ra.jsxs)("span",{className:"px-3 py-1 text-sm text-slate-300",children:[u," / ",p]}),(0,ra.jsx)("button",{onClick:()=>c(Math.min(p,u+1)),disabled:u===p,className:"px-3 py-1 text-sm bg-slate-700 text-slate-300 rounded-lg hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200",children:"Sonraki"})]})]})]})})]})})},su=["title","titleId"];function uu(e,t){let{title:n,titleId:r}=e,l=o(e,su);return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},l),n?a.createElement("title",{id:r},n):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75m-3-7.036A11.959 11.959 0 0 1 3.598 6 11.99 11.99 0 0 0 3 9.749c0 5.592 3.824 10.29 9 11.623 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.285Z"}))}const cu=a.forwardRef(uu),du=[{value:"admin",label:"Admin"},{value:"super_admin",label:"S\xfcper Admin"},{value:"moderator",label:"Moderat\xf6r"}],fu=()=>{const[e,t]=(0,a.useState)([]),[n,r]=(0,a.useState)(!0),[l,i]=(0,a.useState)(""),[o,s]=(0,a.useState)(!1),[u,c]=(0,a.useState)(null),[d,m]=(0,a.useState)({username:"",email:"",password:"",role:"admin",is_active:!0});(0,a.useEffect)(()=>{p()},[l]);const p=async()=>{try{r(!0);const e=new URLSearchParams;l&&e.append("q",l);const n=await Zr("/auth/admins?".concat(e));t(n.data||[])}catch(e){console.error("Error loading admin users:",e)}finally{r(!1)}},h=()=>{m({username:"",email:"",password:"",role:"admin",is_active:!0})},v=e=>new Date(e).toLocaleDateString("tr-TR",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),g=e=>{const t=du.find(t=>t.value===e);return(null===t||void 0===t?void 0:t.label)||e},b=e=>{switch(e){case"super_admin":return"bg-red-500/20 text-red-400";case"admin":return"bg-blue-500/20 text-blue-400";case"moderator":return"bg-green-500/20 text-green-400";default:return"bg-gray-500/20 text-gray-400"}};return(0,ra.jsxs)(nl,{children:[(0,ra.jsxs)("div",{className:"space-y-8",children:[(0,ra.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent",children:"Admin Kullan\u0131c\u0131lar"}),(0,ra.jsx)("p",{className:"mt-2 text-slate-300",children:"Y\xf6netici hesaplar\u0131n\u0131 y\xf6netin"})]}),(0,ra.jsxs)("button",{onClick:()=>{c(null),h(),s(!0)},className:"mt-4 sm:mt-0 btn-primary flex items-center",children:[(0,ra.jsx)(sl,{className:"h-5 w-5 mr-2"}),"Admin Ekle"]})]}),(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl p-6",children:(0,ra.jsxs)("div",{className:"max-w-md",children:[(0,ra.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Admin Ara"}),(0,ra.jsxs)("div",{className:"relative",children:[(0,ra.jsx)(fl,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400"}),(0,ra.jsx)("input",{type:"text",placeholder:"Kullan\u0131c\u0131 ad\u0131 veya email...",value:l,onChange:e=>i(e.target.value),className:"input-field pl-10"})]})]})}),(0,ra.jsx)("div",{className:"bg-slate-800/60 backdrop-blur-xl border border-slate-700/50 rounded-2xl overflow-hidden",children:n?(0,ra.jsxs)("div",{className:"p-8 text-center",children:[(0,ra.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,ra.jsx)("p",{className:"mt-2 text-slate-400",children:"Y\xfckleniyor..."})]}):0===e.length?(0,ra.jsxs)("div",{className:"p-8 text-center",children:[(0,ra.jsx)(Ia,{className:"h-12 w-12 text-slate-400 mx-auto mb-4"}),(0,ra.jsx)("p",{className:"text-slate-400",children:"Admin kullan\u0131c\u0131 bulunamad\u0131."})]}):(0,ra.jsx)("div",{className:"overflow-x-auto",children:(0,ra.jsxs)("table",{className:"min-w-full",children:[(0,ra.jsx)("thead",{className:"bg-slate-900/50",children:(0,ra.jsxs)("tr",{children:[(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Kullan\u0131c\u0131"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Email"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Rol"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Durum"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Son Giri\u015f"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-left text-xs font-medium text-slate-300 uppercase tracking-wider",children:"Olu\u015fturulma"}),(0,ra.jsx)("th",{className:"px-6 py-4 text-right text-xs font-medium text-slate-300 uppercase tracking-wider",children:"\u0130\u015flemler"})]})}),(0,ra.jsx)("tbody",{className:"divide-y divide-slate-700/50",children:e.map(e=>(0,ra.jsxs)("tr",{className:"hover:bg-slate-700/30 transition-colors duration-200",children:[(0,ra.jsx)("td",{className:"px-6 py-4",children:(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mr-4",children:(0,ra.jsx)(cu,{className:"h-5 w-5 text-white"})}),(0,ra.jsx)("div",{children:(0,ra.jsx)("div",{className:"text-sm font-medium text-slate-200",children:e.username})})]})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-sm text-slate-300",children:e.email}),(0,ra.jsx)("td",{className:"px-6 py-4",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(b(e.role)),children:g(e.role)})}),(0,ra.jsx)("td",{className:"px-6 py-4",children:(0,ra.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(e.is_active?"bg-green-500/20 text-green-400":"bg-red-500/20 text-red-400"),children:e.is_active?"Aktif":"Pasif"})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-sm text-slate-300",children:e.last_login?(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)(iu,{className:"h-4 w-4 text-slate-400 mr-1"}),v(e.last_login)]}):(0,ra.jsx)("span",{className:"text-slate-500",children:"Hi\xe7 giri\u015f yapmam\u0131\u015f"})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-sm text-slate-300",children:(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)(iu,{className:"h-4 w-4 text-slate-400 mr-1"}),v(e.created_at)]})}),(0,ra.jsx)("td",{className:"px-6 py-4 text-right text-sm font-medium",children:(0,ra.jsxs)("div",{className:"flex items-center justify-end space-x-2",children:[(0,ra.jsx)("button",{onClick:()=>(e=>{c(e),m({username:e.username,email:e.email,password:"",role:e.role,is_active:e.is_active}),s(!0)})(e),className:"text-blue-400 hover:text-blue-300 transition-colors duration-200",children:(0,ra.jsx)(wl,{className:"h-4 w-4"})}),(0,ra.jsx)("button",{onClick:()=>(async e=>{if(window.confirm("Bu admin kullan\u0131c\u0131y\u0131 silmek istedi\u011finizden emin misiniz?"))try{await na("/auth/admins/".concat(e)),p()}catch(t){console.error("Error deleting admin user:",t)}})(e.id),className:"text-red-400 hover:text-red-300 transition-colors duration-200",children:(0,ra.jsx)(jl,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),o&&(0,ra.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:(0,ra.jsxs)("div",{className:"bg-slate-800 rounded-2xl p-6 w-full max-w-md",children:[(0,ra.jsx)("h3",{className:"text-lg font-semibold text-white mb-6",children:u?"Admin D\xfczenle":"Yeni Admin Ekle"}),(0,ra.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{if(u){const e=f({},d);e.password||delete e.password,await ta("/auth/admins/".concat(u.id),e)}else await ea("/auth/admins",d);s(!1),c(null),h(),p()}catch(t){console.error("Error saving admin user:",t)}},className:"space-y-4",children:[(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Kullan\u0131c\u0131 Ad\u0131 *"}),(0,ra.jsx)("input",{type:"text",required:!0,value:d.username,onChange:e=>m(f(f({},d),{},{username:e.target.value})),className:"input-field",placeholder:"Kullan\u0131c\u0131 ad\u0131"})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Email *"}),(0,ra.jsx)("input",{type:"email",required:!0,value:d.email,onChange:e=>m(f(f({},d),{},{email:e.target.value})),className:"input-field",placeholder:"Email adresi"})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsxs)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:["\u015eifre ",u?"(Bo\u015f b\u0131rak\u0131l\u0131rsa de\u011fi\u015fmez)":"*"]}),(0,ra.jsx)("input",{type:"password",required:!u,value:d.password,onChange:e=>m(f(f({},d),{},{password:e.target.value})),className:"input-field",placeholder:"\u015eifre"})]}),(0,ra.jsxs)("div",{children:[(0,ra.jsx)("label",{className:"block text-sm font-medium text-slate-300 mb-2",children:"Rol *"}),(0,ra.jsx)("select",{required:!0,value:d.role,onChange:e=>m(f(f({},d),{},{role:e.target.value})),className:"input-field",children:du.map(e=>(0,ra.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,ra.jsxs)("div",{className:"flex items-center",children:[(0,ra.jsx)("input",{type:"checkbox",id:"is_active",checked:d.is_active,onChange:e=>m(f(f({},d),{},{is_active:e.target.checked})),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,ra.jsx)("label",{htmlFor:"is_active",className:"ml-2 block text-sm text-slate-300",children:"Aktif"})]}),(0,ra.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,ra.jsx)("button",{type:"button",onClick:()=>s(!1),className:"btn-secondary",children:"\u0130ptal"}),(0,ra.jsx)("button",{type:"submit",className:"btn-primary",children:u?"G\xfcncelle":"Olu\u015ftur"})]})]})]})})]})},mu=e=>{let{children:t}=e;const{user:n,isLoading:r}=la();return r?(0,ra.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,ra.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"})}):n?(0,ra.jsx)(ra.Fragment,{children:t}):(0,ra.jsx)(Ae,{to:"/login",replace:!0})};const pu=function(){return(0,ra.jsx)(sa,{children:(0,ra.jsx)(ia,{children:(0,ra.jsx)(gt,{children:(0,ra.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:(0,ra.jsxs)(De,{children:[(0,ra.jsx)(Fe,{path:"/login",element:(0,ra.jsx)(ha,{})}),(0,ra.jsx)(Fe,{path:"/",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(ul,{})})}),(0,ra.jsx)(Fe,{path:"/lessons",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(zs,{})})}),(0,ra.jsx)(Fe,{path:"/subjects",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(Ws,{})})}),(0,ra.jsx)(Fe,{path:"/explanations",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)($s,{})})}),(0,ra.jsx)(Fe,{path:"/questions",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(Zs,{})})}),(0,ra.jsx)(Fe,{path:"/logs",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(ru,{})})}),(0,ra.jsx)(Fe,{path:"/users",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(ou,{})})}),(0,ra.jsx)(Fe,{path:"/admin-users",element:(0,ra.jsx)(mu,{children:(0,ra.jsx)(fu,{})})})]})})})})})};i.createRoot(document.getElementById("root")).render((0,ra.jsx)(a.StrictMode,{children:(0,ra.jsx)(pu,{})}))})();
//# sourceMappingURL=main.e1cd90a2.js.map