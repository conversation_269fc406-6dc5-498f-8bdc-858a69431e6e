package explanation

import "github.com/google/uuid"

// ListOptions captures filters applicable when listing explanations.
type ListOptions struct {
	LessonID  *uuid.UUID
	SubjectID *uuid.UUID
	Published *bool
	Search    string
	Limit     int
	Offset    int
	Order     string
}

// CreateInput models payload for creating an explanation.
type CreateInput struct {
	LessonID    uuid.UUID
	SubjectID   uuid.UUID
	Title       string
	Summary     *string
	Body        *string
	IsPublished bool
}

// UpdateInput models mutable explanation fields.
type UpdateInput struct {
	LessonID    *uuid.UUID
	SubjectID   *uuid.UUID
	Title       *string
	Summary     *string
	Body        *string
	IsPublished *bool
}
