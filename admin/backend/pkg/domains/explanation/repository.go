package explanation

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	List(opts ListOptions) ([]entities.Explanation, int64, error)
	Get(id uuid.UUID) (*entities.Explanation, error)
	Create(exp *entities.Explanation) error
	Update(exp *entities.Explanation) error
	Delete(id uuid.UUID) error
	DeleteByLessonAndPublishState(lessonID uuid.UUID, isPublished bool) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) List(opts ListOptions) ([]entities.Explanation, int64, error) {
	query := r.db.Model(&entities.Explanation{})

	if opts.LessonID != nil {
		query = query.Where("lesson_id = ?", *opts.LessonID)
	}
	if opts.SubjectID != nil {
		query = query.Where("subject_id = ?", *opts.SubjectID)
	}
	if opts.Published != nil {
		query = query.Where("is_published = ?", *opts.Published)
	}
	if opts.Search != "" {
		like := "%" + opts.Search + "%"
		query = query.Where("title ILIKE ? OR COALESCE(summary, '') ILIKE ?", like, like)
	}

	if opts.Order == "" {
		opts.Order = "created_at DESC"
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if opts.Limit > 0 {
		query = query.Limit(opts.Limit)
	}
	if opts.Offset > 0 {
		query = query.Offset(opts.Offset)
	}

	var list []entities.Explanation
	err := query.Order(opts.Order).Find(&list).Error
	return list, total, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Explanation, error) {
	var exp entities.Explanation
	if err := r.db.First(&exp, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &exp, nil
}

func (r *repository) Create(exp *entities.Explanation) error {
	return r.db.Create(exp).Error
}

func (r *repository) Update(exp *entities.Explanation) error {
	return r.db.Save(exp).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Explanation{}, "id = ?", id).Error
}

func (r *repository) DeleteByLessonAndPublishState(lessonID uuid.UUID, isPublished bool) error {
	return r.db.Where("lesson_id = ? AND is_published = ?", lessonID, isPublished).
		Delete(&entities.Explanation{}).Error
}
