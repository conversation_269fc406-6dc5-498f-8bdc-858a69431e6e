package explanation

import (
	"errors"
	"strings"

	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Service interface {
	List(opts ListOptions) ([]entities.Explanation, int64, error)
	Get(id uuid.UUID) (*entities.Explanation, error)
	Create(input *CreateInput) (*entities.Explanation, error)
	Update(id uuid.UUID, input *UpdateInput) (*entities.Explanation, error)
	Delete(id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) List(opts ListOptions) ([]entities.Explanation, int64, error) {
	if opts.Limit <= 0 {
		opts.Limit = 50
	}
	if opts.Limit > 200 {
		opts.Limit = 200
	}
	if opts.Offset < 0 {
		opts.Offset = 0
	}
	return s.repo.List(opts)
}

func (s *service) Get(id uuid.UUID) (*entities.Explanation, error) {
	return s.repo.Get(id)
}

func (s *service) Create(input *CreateInput) (*entities.Explanation, error) {
	if input == nil {
		return nil, errors.New("explanation payload is required")
	}
	if input.LessonID == uuid.Nil {
		return nil, errors.New("lesson_id is required")
	}
	if input.SubjectID == uuid.Nil {
		return nil, errors.New("subject_id is required")
	}
	if strings.TrimSpace(input.Title) == "" {
		return nil, errors.New("title is required")
	}

	exp := &entities.Explanation{
		LessonID:    input.LessonID,
		SubjectID:   input.SubjectID,
		Title:       strings.TrimSpace(input.Title),
		IsPublished: input.IsPublished,
	}
	if input.Summary != nil {
		trimmed := strings.TrimSpace(*input.Summary)
		if trimmed != "" {
			exp.Summary = &trimmed
		}
	}
	if input.Body != nil {
		trimmed := strings.TrimSpace(*input.Body)
		if trimmed != "" {
			exp.Body = &trimmed
		}
	}

	if err := s.repo.Create(exp); err != nil {
		return nil, err
	}
	return exp, nil
}

func (s *service) Update(id uuid.UUID, input *UpdateInput) (*entities.Explanation, error) {
	if input == nil {
		return nil, errors.New("explanation payload is required")
	}
	exp, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}

	if input.LessonID != nil && *input.LessonID != uuid.Nil {
		exp.LessonID = *input.LessonID
	}
	if input.SubjectID != nil && *input.SubjectID != uuid.Nil {
		exp.SubjectID = *input.SubjectID
	}
	if input.Title != nil {
		if trimmed := strings.TrimSpace(*input.Title); trimmed != "" {
			exp.Title = trimmed
		}
	}
	if input.Summary != nil {
		trimmed := strings.TrimSpace(*input.Summary)
		if trimmed == "" {
			exp.Summary = nil
		} else {
			exp.Summary = &trimmed
		}
	}
	if input.Body != nil {
		trimmed := strings.TrimSpace(*input.Body)
		if trimmed == "" {
			exp.Body = nil
		} else {
			exp.Body = &trimmed
		}
	}
	if input.IsPublished != nil {
		exp.IsPublished = *input.IsPublished
	}

	if err := s.repo.Update(exp); err != nil {
		return nil, err
	}
	return exp, nil
}

func (s *service) Delete(id uuid.UUID) error {
	return s.repo.Delete(id)
}
