package explanationimport

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/explanation"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/subject"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

// Service defines behaviour for importing lesson explanations via automation.
type Service interface {
	ImportFromPDF(ctx context.Context, lessonID uuid.UUID, fileName string, fileData []byte) (*Result, error)
}

type service struct {
	db         *gorm.DB
	httpClient *http.Client
	config     Config
}

// NewService constructs a new Service instance.
func NewService(db *gorm.DB, cfg Config) Service {
	timeout := cfg.Timeout
	if timeout <= 0 {
		timeout = 120 * time.Second
	}
	client := &http.Client{Timeout: timeout}
	return &service{
		db:         db,
		httpClient: client,
		config:     cfg,
	}
}

// ImportFromPDF sends the provided PDF to the automation pipeline and persists the returned notes.
func (s *service) ImportFromPDF(ctx context.Context, lessonID uuid.UUID, fileName string, fileData []byte) (*Result, error) {
	if lessonID == uuid.Nil {
		return nil, errors.New("lesson_id is required")
	}
	if len(fileData) == 0 {
		return nil, errors.New("pdf file payload is empty")
	}
	if strings.TrimSpace(s.config.WebhookURL) == "" {
		return nil, errors.New("automation webhook url is not configured")
	}

	payload, err := s.invokeAutomation(ctx, fileName, fileData)
	if err != nil {
		return nil, err
	}

	res := &Result{Payload: *payload}

	tx := s.db.WithContext(ctx).Begin()
	if tx.Error != nil {
		return nil, tx.Error
	}

	subjectRepo := subject.NewRepository(tx)
	explanationRepo := explanation.NewRepository(tx)

	for _, topic := range payload.DersNotlari {
		title := strings.TrimSpace(topic.UstKonu)
		if title == "" {
			continue
		}

		subjectEntity := &entities.Subject{
			LessonID: lessonID,
			Title:    title,
			IsActive: false,
		}
		if err := subjectRepo.Create(subjectEntity); err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("create subject %q: %w", title, err)
		}
		res.Subjects = append(res.Subjects, *subjectEntity)

		for _, sub := range topic.AltBasliklar {
			altTitle := strings.TrimSpace(sub.Baslik)
			summaryText := strings.TrimSpace(sub.Ozet)
			if altTitle == "" && summaryText == "" {
				continue
			}

			explanationEntity := &entities.Explanation{
				LessonID:    lessonID,
				SubjectID:   subjectEntity.ID,
				Title:       altTitle,
				IsPublished: false,
			}
			if summaryText != "" {
				summary := summaryText
				explanationEntity.Summary = &summary
			}
			if err := explanationRepo.Create(explanationEntity); err != nil {
				tx.Rollback()
				return nil, fmt.Errorf("create explanation %q: %w", altTitle, err)
			}
			res.Explanations = append(res.Explanations, *explanationEntity)
		}
	}

	if err := tx.Commit().Error; err != nil {
		return nil, err
	}

	return res, nil
}

func (s *service) invokeAutomation(ctx context.Context, fileName string, fileData []byte) (*AutomationPayload, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	if strings.TrimSpace(fileName) == "" {
		fileName = "lesson.pdf"
	}

	part, err := writer.CreateFormFile("data", fileName)
	if err != nil {
		return nil, err
	}
	if _, err := part.Write(fileData); err != nil {
		return nil, err
	}
	if err := writer.Close(); err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, s.config.WebhookURL, body)
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())
	if s.config.HeaderName != "" && s.config.HeaderValue != "" {
		req.Header.Set(s.config.HeaderName, s.config.HeaderValue)
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		bodyBytes, _ := io.ReadAll(io.LimitReader(resp.Body, 4_096))
		return nil, fmt.Errorf("automation responded with %d: %s", resp.StatusCode, string(bodyBytes))
	}

	respBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var payload AutomationPayload
	if err := json.Unmarshal(respBytes, &payload); err != nil {
		return nil, fmt.Errorf("parse automation payload: %w", err)
	}

	return &payload, nil
}
