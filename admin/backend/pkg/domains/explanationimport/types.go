package explanationimport

import (
	"time"

	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

// Config defines automation integration settings for the import service.
type Config struct {
	WebhookURL  string
	HeaderName  string
	HeaderValue string
	Timeout     time.Duration
}

// Result represents the outcome of a PDF import operation.
type Result struct {
	Subjects     []entities.Subject
	Explanations []entities.Explanation
	Payload      AutomationPayload
}

// AutomationPayload captures the structure returned by the automation webhook.
type AutomationPayload struct {
	DersNotlari []AutomationTopic `json:"ders_notlari"`
}

// AutomationTopic represents a top-level subject imported from the automation pipeline.
type AutomationTopic struct {
	UstKonu      string                 `json:"ust_konu"`
	AltBasliklar []AutomationSubheading `json:"alt_basliklar"`
}

// AutomationSubheading contains the title and summary for a single explanation.
type AutomationSubheading struct {
	Baslik string `json:"baslik"`
	Ozet   string `json:"ozet"`
}
