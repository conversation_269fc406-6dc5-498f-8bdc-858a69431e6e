package log

import (
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	List(opts ListOptions) ([]entities.Log, int64, error)
	Create(entry *entities.Log) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) List(opts ListOptions) ([]entities.Log, int64, error) {
	query := r.db.Model(&entities.Log{})

	if opts.Entity != "" {
		query = query.Where("entity = ?", opts.Entity)
	}
	if opts.Type != "" {
		query = query.Where("type = ?", opts.Type)
	}
	if opts.Search != "" {
		like := "%" + opts.Search + "%"
		query = query.Where("title ILIKE ? OR message ILIKE ?", like, like)
	}

	if opts.Order == "" {
		opts.Order = "created_at DESC"
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if opts.Limit > 0 {
		query = query.Limit(opts.Limit)
	}
	if opts.Offset > 0 {
		query = query.Offset(opts.Offset)
	}

	var list []entities.Log
	err := query.Order(opts.Order).Find(&list).Error
	return list, total, err
}

func (r *repository) Create(entry *entities.Log) error {
	return r.db.Create(entry).Error
}
