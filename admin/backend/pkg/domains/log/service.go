package log

import (
	"sync"

	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Service interface {
	List(opts ListOptions) ([]entities.Log, int64, error)
	Create(entry *entities.Log) error
}

type service struct {
	repo Repository
}

var (
	defaultService   Service
	defaultServiceMu sync.RWMutex
)

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) List(opts ListOptions) ([]entities.Log, int64, error) {
	if opts.Limit <= 0 {
		opts.Limit = 50
	}
	if opts.Limit > 200 {
		opts.Limit = 200
	}
	if opts.Offset < 0 {
		opts.Offset = 0
	}
	return s.repo.List(opts)
}

func (s *service) Create(entry *entities.Log) error {
	return s.repo.Create(entry)
}

// SetDefaultService exposes the log service for helper usage (e.g., domain logs without DI).
func SetDefaultService(svc Service) {
	defaultServiceMu.Lock()
	defer defaultServiceMu.Unlock()
	defaultService = svc
}

// CreateLog persists a log entry using the configured default service (or DB fallback).
func CreateLog(entry *entities.Log) error {
	defaultServiceMu.RLock()
	svc := defaultService
	defaultServiceMu.RUnlock()
	if svc != nil {
		return svc.Create(entry)
	}
	return database.DB().Create(entry).Error
}
