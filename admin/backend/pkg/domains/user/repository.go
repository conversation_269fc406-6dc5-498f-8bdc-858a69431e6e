package user

import (
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Repository interface {
	List(q string, limit, offset int, order string) ([]entities.User, int64, error)
}

type repository struct{}

func NewRepository() Repository { return &repository{} }

func (r *repository) List(q string, limit, offset int, order string) ([]entities.User, int64, error) {
	var list []entities.User
	dbq := database.DB().Model(&entities.User{})
	if q != "" {
		like := "%" + q + "%"
		dbq = dbq.Where("username ILIKE ? OR name ILIKE ? OR email ILIKE ?", like, like, like)
	}
	var total int64
	dbq.Count(&total)
	err := dbq.Limit(limit).Offset(offset).Order(order).Find(&list).Error
	return list, total, err
}
