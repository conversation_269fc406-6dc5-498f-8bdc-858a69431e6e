package user

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

type Service interface {
	List(q string, limit, offset int, order string) ([]entities.User, int64, error)
}

type service struct{ repo Repository }

func NewService(r Repository) Service { return &service{repo: r} }

func (s *service) List(q string, limit, offset int, order string) ([]entities.User, int64, error) {
	return s.repo.List(q, limit, offset, order)
}
