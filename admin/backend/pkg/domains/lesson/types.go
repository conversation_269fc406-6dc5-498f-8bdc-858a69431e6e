package lesson

// ListOptions captures supported filters for lesson listings.
type ListOptions struct {
	Search    string
	Published *bool
	Limit     int
	Offset    int
	Order     string
}

// CreateInput models payload required to create a lesson.
type CreateInput struct {
	Title       string
	Description *string
	Body        *string
	IsPublished bool
}

// UpdateInput models mutable fields for lesson updates.
type UpdateInput struct {
	Title       *string
	Description *string
	Body        *string
	IsPublished *bool
}
