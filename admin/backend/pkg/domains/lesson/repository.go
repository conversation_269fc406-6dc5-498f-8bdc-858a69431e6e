package lesson

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	List(opts ListOptions) ([]entities.Lesson, int64, error)
	Get(id uuid.UUID) (*entities.Lesson, error)
	Create(lesson *entities.Lesson) error
	Update(lesson *entities.Lesson) error
	Delete(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) List(opts ListOptions) ([]entities.Lesson, int64, error) {
	query := r.db.Model(&entities.Lesson{})
	if opts.Search != "" {
		like := "%" + opts.Search + "%"
		query = query.Where("title ILIKE ? OR description ILIKE ?", like, like)
	}
	if opts.Published != nil {
		query = query.Where("is_published = ?", *opts.Published)
	}

	if opts.Order == "" {
		opts.Order = "created_at DESC"
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if opts.Limit > 0 {
		query = query.Limit(opts.Limit)
	}
	if opts.Offset > 0 {
		query = query.Offset(opts.Offset)
	}

	var list []entities.Lesson
	err := query.Order(opts.Order).Find(&list).Error
	return list, total, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Lesson, error) {
	var lesson entities.Lesson
	if err := r.db.First(&lesson, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &lesson, nil
}

func (r *repository) Create(lesson *entities.Lesson) error {
	return r.db.Create(lesson).Error
}

func (r *repository) Update(lesson *entities.Lesson) error {
	return r.db.Save(lesson).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Lesson{}, "id = ?", id).Error
}
