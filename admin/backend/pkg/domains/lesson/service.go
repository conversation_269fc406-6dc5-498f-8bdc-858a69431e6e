package lesson

import (
	"errors"
	"strings"

	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Service interface {
	List(opts ListOptions) ([]entities.Lesson, int64, error)
	Get(id uuid.UUID) (*entities.Lesson, error)
	Create(input *CreateInput) (*entities.Lesson, error)
	Update(id uuid.UUID, input *UpdateInput) (*entities.Lesson, error)
	Delete(id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) List(opts ListOptions) ([]entities.Lesson, int64, error) {
	if opts.Limit <= 0 {
		opts.Limit = 50
	}
	if opts.Limit > 200 {
		opts.Limit = 200
	}
	if opts.Offset < 0 {
		opts.Offset = 0
	}
	return s.repo.List(opts)
}

func (s *service) Get(id uuid.UUID) (*entities.Lesson, error) {
	return s.repo.Get(id)
}

func (s *service) Create(input *CreateInput) (*entities.Lesson, error) {
	if input == nil {
		return nil, errors.New("lesson payload is required")
	}
	if strings.TrimSpace(input.Title) == "" {
		return nil, errors.New("lesson title is required")
	}

	lesson := &entities.Lesson{
		Title: strings.TrimSpace(input.Title),
	}
	if input.Description != nil {
		lesson.Description = strings.TrimSpace(*input.Description)
	}
	if input.Body != nil {
		lesson.Body = strings.TrimSpace(*input.Body)
	}
	lesson.IsPublished = input.IsPublished

	if err := s.repo.Create(lesson); err != nil {
		return nil, err
	}
	return lesson, nil
}

func (s *service) Update(id uuid.UUID, input *UpdateInput) (*entities.Lesson, error) {
	if input == nil {
		return nil, errors.New("lesson payload is required")
	}
	lesson, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}

	if input.Title != nil {
		if trimmed := strings.TrimSpace(*input.Title); trimmed != "" {
			lesson.Title = trimmed
		}
	}
	if input.Description != nil {
		lesson.Description = strings.TrimSpace(*input.Description)
	}
	if input.Body != nil {
		lesson.Body = strings.TrimSpace(*input.Body)
	}
	if input.IsPublished != nil {
		lesson.IsPublished = *input.IsPublished
	}

	if err := s.repo.Update(lesson); err != nil {
		return nil, err
	}
	return lesson, nil
}

func (s *service) Delete(id uuid.UUID) error {
	return s.repo.Delete(id)
}
