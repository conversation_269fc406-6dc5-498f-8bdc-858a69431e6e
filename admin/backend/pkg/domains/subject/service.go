package subject

import (
	"errors"
	"strings"

	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Service interface {
	List(opts ListOptions) ([]entities.Subject, int64, error)
	Get(id uuid.UUID) (*entities.Subject, error)
	Create(input *CreateInput) (*entities.Subject, error)
	Update(id uuid.UUID, input *UpdateInput) (*entities.Subject, error)
	Delete(id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) List(opts ListOptions) ([]entities.Subject, int64, error) {
	if opts.Limit <= 0 {
		opts.Limit = 50
	}
	if opts.Limit > 200 {
		opts.Limit = 200
	}
	if opts.Offset < 0 {
		opts.Offset = 0
	}
	return s.repo.List(opts)
}

func (s *service) Get(id uuid.UUID) (*entities.Subject, error) {
	return s.repo.Get(id)
}

func (s *service) Create(input *CreateInput) (*entities.Subject, error) {
	if input == nil {
		return nil, errors.New("subject payload is required")
	}
	if strings.TrimSpace(input.Title) == "" {
		return nil, errors.New("subject title is required")
	}
	if input.LessonID == uuid.Nil {
		return nil, errors.New("lesson_id is required")
	}

	subject := &entities.Subject{
		LessonID: input.LessonID,
		Title:    strings.TrimSpace(input.Title),
		IsActive: input.IsActive,
	}
	if input.MainSubjectID != nil {
		subject.MainSubjectID = input.MainSubjectID
	}
	if input.Description != nil {
		trimmed := strings.TrimSpace(*input.Description)
		if trimmed != "" {
			subject.Description = &trimmed
		}
	}

	if err := s.repo.Create(subject); err != nil {
		return nil, err
	}
	return subject, nil
}

func (s *service) Update(id uuid.UUID, input *UpdateInput) (*entities.Subject, error) {
	if input == nil {
		return nil, errors.New("subject payload is required")
	}

	subject, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}

	if input.Title != nil {
		if trimmed := strings.TrimSpace(*input.Title); trimmed != "" {
			subject.Title = trimmed
		}
	}
	if input.Description != nil {
		trimmed := strings.TrimSpace(*input.Description)
		if trimmed == "" {
			subject.Description = nil
		} else {
			subject.Description = &trimmed
		}
	}
	if input.LessonID != nil && *input.LessonID != uuid.Nil {
		subject.LessonID = *input.LessonID
	}
	if input.MainSubjectIDSet {
		subject.MainSubjectID = input.MainSubjectID
	}
	if input.IsActive != nil {
		subject.IsActive = *input.IsActive
	}

	if err := s.repo.Update(subject); err != nil {
		return nil, err
	}
	return subject, nil
}

func (s *service) Delete(id uuid.UUID) error {
	return s.repo.Delete(id)
}
