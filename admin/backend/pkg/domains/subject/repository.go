package subject

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	List(opts ListOptions) ([]entities.Subject, int64, error)
	Get(id uuid.UUID) (*entities.Subject, error)
	Create(subject *entities.Subject) error
	Update(subject *entities.Subject) error
	Delete(id uuid.UUID) error
	DeleteInactiveByLesson(lessonID uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) List(opts ListOptions) ([]entities.Subject, int64, error) {
	query := r.db.Model(&entities.Subject{})

	if opts.LessonID != nil {
		query = query.Where("lesson_id = ?", *opts.LessonID)
	}
	if opts.MainSubjectID != nil {
		query = query.Where("main_subject_id = ?", *opts.MainSubjectID)
	}
	if opts.Search != "" {
		like := "%" + opts.Search + "%"
		query = query.Where("title ILIKE ? OR description ILIKE ?", like, like)
	}
	if opts.Active != nil {
		query = query.Where("is_active = ?", *opts.Active)
	}

	if opts.Order == "" {
		opts.Order = "created_at DESC"
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if opts.Limit > 0 {
		query = query.Limit(opts.Limit)
	}
	if opts.Offset > 0 {
		query = query.Offset(opts.Offset)
	}

	var list []entities.Subject
	err := query.Order(opts.Order).Find(&list).Error
	return list, total, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Subject, error) {
	var subject entities.Subject
	if err := r.db.First(&subject, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &subject, nil
}

func (r *repository) Create(subject *entities.Subject) error {
	return r.db.Create(subject).Error
}

func (r *repository) Update(subject *entities.Subject) error {
	return r.db.Save(subject).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Subject{}, "id = ?", id).Error
}

func (r *repository) DeleteInactiveByLesson(lessonID uuid.UUID) error {
	return r.db.Where("lesson_id = ? AND is_active = ?", lessonID, false).
		Delete(&entities.Subject{}).Error
}
