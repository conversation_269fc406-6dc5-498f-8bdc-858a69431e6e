package subject

import "github.com/google/uuid"

// ListOptions captures filtering criteria for subject listings.
type ListOptions struct {
	LessonID      *uuid.UUID
	MainSubjectID *uuid.UUID
	Search        string
	Active        *bool
	Limit         int
	Offset        int
	Order         string
}

// CreateInput represents the payload to create a subject.
type CreateInput struct {
	LessonID      uuid.UUID
	MainSubjectID *uuid.UUID
	Title         string
	Description   *string
	IsActive      bool
}

// UpdateInput encapsulates mutable subject fields.
type UpdateInput struct {
	LessonID         *uuid.UUID
	MainSubjectID    *uuid.UUID
	MainSubjectIDSet bool
	Title            *string
	Description      *string
	IsActive         *bool
}
