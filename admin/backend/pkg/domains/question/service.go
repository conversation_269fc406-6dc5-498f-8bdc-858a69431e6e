package question

import (
	"errors"
	"strings"

	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
)

type Service interface {
	List(opts ListOptions) ([]entities.Question, int64, error)
	Get(id uuid.UUID) (*entities.Question, error)
	Create(input *CreateInput) (*entities.Question, error)
	Update(id uuid.UUID, input *UpdateInput) (*entities.Question, error)
	Delete(id uuid.UUID) error
}

type service struct {
	repo Repository
}

func NewService(repo Repository) Service {
	return &service{repo: repo}
}

func (s *service) List(opts ListOptions) ([]entities.Question, int64, error) {
	if opts.Limit <= 0 {
		opts.Limit = 50
	}
	if opts.Limit > 200 {
		opts.Limit = 200
	}
	if opts.Offset < 0 {
		opts.Offset = 0
	}
	return s.repo.List(opts)
}

func (s *service) Get(id uuid.UUID) (*entities.Question, error) {
	return s.repo.Get(id)
}

func (s *service) Create(input *CreateInput) (*entities.Question, error) {
	if input == nil {
		return nil, errors.New("question payload is required")
	}
	if input.LessonID == uuid.Nil {
		return nil, errors.New("lesson_id is required")
	}
	if input.SubjectID == uuid.Nil {
		return nil, errors.New("subject_id is required")
	}
	if strings.TrimSpace(input.Stem) == "" {
		return nil, errors.New("stem is required")
	}
	if err := validateType(input.Type); err != nil {
		return nil, err
	}
	if err := validateDifficulty(input.Difficulty); err != nil {
		return nil, err
	}

	question := &entities.Question{
		LessonID:      input.LessonID,
		SubjectID:     input.SubjectID,
		ExplanationID: input.ExplanationID,
		Type:          input.Type,
		Difficulty:    input.Difficulty,
		Source:        strings.TrimSpace(input.Source),
		Stem:          strings.TrimSpace(input.Stem),
		IsPublished:   input.IsPublished,
	}

	if err := s.repo.Create(question); err != nil {
		return nil, err
	}
	return question, nil
}

func (s *service) Update(id uuid.UUID, input *UpdateInput) (*entities.Question, error) {
	if input == nil {
		return nil, errors.New("question payload is required")
	}
	question, err := s.repo.Get(id)
	if err != nil {
		return nil, err
	}

	if input.LessonID != nil && *input.LessonID != uuid.Nil {
		question.LessonID = *input.LessonID
	}
	if input.SubjectID != nil && *input.SubjectID != uuid.Nil {
		question.SubjectID = *input.SubjectID
	}
	if input.ExplanationIDSet {
		question.ExplanationID = input.ExplanationID
	}
	if input.Type != nil {
		if err := validateType(*input.Type); err != nil {
			return nil, err
		}
		question.Type = *input.Type
	}
	if input.Difficulty != nil {
		if err := validateDifficulty(*input.Difficulty); err != nil {
			return nil, err
		}
		question.Difficulty = *input.Difficulty
	}
	if input.Source != nil {
		question.Source = strings.TrimSpace(*input.Source)
	}
	if input.Stem != nil {
		trimmed := strings.TrimSpace(*input.Stem)
		if trimmed != "" {
			question.Stem = trimmed
		}
	}
	if input.IsPublished != nil {
		question.IsPublished = *input.IsPublished
	}

	if err := s.repo.Update(question); err != nil {
		return nil, err
	}
	return question, nil
}

func (s *service) Delete(id uuid.UUID) error {
	return s.repo.Delete(id)
}

func validateType(value string) error {
	switch value {
	case entities.QuestionTypeSingleChoice,
		entities.QuestionTypeMultipleChoice,
		entities.QuestionTypeTrueFalse,
		entities.QuestionTypeFillBlank:
		return nil
	}
	return errors.New("invalid question type")
}

func validateDifficulty(value string) error {
	switch value {
	case entities.DifficultyEasy,
		entities.DifficultyMedium,
		entities.DifficultyHard:
		return nil
	}
	return errors.New("invalid question difficulty")
}
