package question

import "github.com/google/uuid"

// ListOptions defines supported filters when querying questions.
type ListOptions struct {
	LessonID      *uuid.UUID
	SubjectID     *uuid.UUID
	ExplanationID *uuid.UUID
	Type          string
	Difficulty    string
	Published     *bool
	Search        string
	Limit         int
	Offset        int
	Order         string
}

// CreateInput represents the payload required to create a question.
type CreateInput struct {
	LessonID      uuid.UUID
	SubjectID     uuid.UUID
	ExplanationID *uuid.UUID
	Type          string
	Difficulty    string
	Source        string
	Stem          string
	IsPublished   bool
}

// UpdateInput encapsulates mutable fields for a question.
type UpdateInput struct {
	LessonID         *uuid.UUID
	SubjectID        *uuid.UUID
	ExplanationID    *uuid.UUID
	ExplanationIDSet bool
	Type             *string
	Difficulty       *string
	Source           *string
	Stem             *string
	IsPublished      *bool
}
