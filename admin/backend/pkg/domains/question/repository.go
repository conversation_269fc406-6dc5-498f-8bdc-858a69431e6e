package question

import (
	"github.com/google/uuid"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"gorm.io/gorm"
)

type Repository interface {
	List(opts ListOptions) ([]entities.Question, int64, error)
	Get(id uuid.UUID) (*entities.Question, error)
	Create(question *entities.Question) error
	Update(question *entities.Question) error
	Delete(id uuid.UUID) error
}

type repository struct {
	db *gorm.DB
}

func NewRepository(db *gorm.DB) Repository {
	return &repository{db: db}
}

func (r *repository) List(opts ListOptions) ([]entities.Question, int64, error) {
	query := r.db.Model(&entities.Question{})

	if opts.LessonID != nil {
		query = query.Where("lesson_id = ?", *opts.LessonID)
	}
	if opts.SubjectID != nil {
		query = query.Where("subject_id = ?", *opts.SubjectID)
	}
	if opts.ExplanationID != nil {
		query = query.Where("explanation_id = ?", *opts.ExplanationID)
	}
	if opts.Type != "" {
		query = query.Where("type = ?", opts.Type)
	}
	if opts.Difficulty != "" {
		query = query.Where("difficulty = ?", opts.Difficulty)
	}
	if opts.Published != nil {
		query = query.Where("is_published = ?", *opts.Published)
	}
	if opts.Search != "" {
		like := "%" + opts.Search + "%"
		query = query.Where("stem ILIKE ? OR source ILIKE ?", like, like)
	}

	if opts.Order == "" {
		opts.Order = "created_at DESC"
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	if opts.Limit > 0 {
		query = query.Limit(opts.Limit)
	}
	if opts.Offset > 0 {
		query = query.Offset(opts.Offset)
	}

	var list []entities.Question
	err := query.Order(opts.Order).Find(&list).Error
	return list, total, err
}

func (r *repository) Get(id uuid.UUID) (*entities.Question, error) {
	var question entities.Question
	if err := r.db.First(&question, "id = ?", id).Error; err != nil {
		return nil, err
	}
	return &question, nil
}

func (r *repository) Create(question *entities.Question) error {
	return r.db.Create(question).Error
}

func (r *repository) Update(question *entities.Question) error {
	return r.db.Save(question).Error
}

func (r *repository) Delete(id uuid.UUID) error {
	return r.db.Delete(&entities.Question{}, "id = ?", id).Error
}
