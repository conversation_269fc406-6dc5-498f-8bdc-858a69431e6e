package utils

import (
	"github.com/golang-jwt/jwt/v5"
	"time"
)

type AdminClaims struct {
	Username string `json:"username"`
	Role     string `json:"role"`
	AdminID  string `json:"admin_id,omitempty"`
	jwt.RegisteredClaims
}

func GenerateToken(secret, issuer, username, role, adminID string, expireHours int) (string, error) {
	claims := AdminClaims{
		Username: username,
		Role:     role,
		AdminID:  adminID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    issuer,
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(expireHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}

func ParseAndValidate(secret string, tokenStr string) (*AdminClaims, bool) {
	token, err := jwt.ParseWithClaims(tokenStr, &AdminClaims{}, func(t *jwt.Token) (interface{}, error) {
		return []byte(secret), nil
	})
	if err != nil {
		return nil, false
	}
	if claims, ok := token.Claims.(*AdminClaims); ok && token.Valid {
		return claims, true
	}
	return nil, false
}
