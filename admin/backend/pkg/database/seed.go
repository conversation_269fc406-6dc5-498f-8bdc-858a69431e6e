package database

import (
	"log"

	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/entities"
	"github.com/nocytech/kpss-plus-admin/pkg/utils"
)

// SeedAdminFromConfig ensures there is at least one admin user.
// It creates an admin with configured credentials if missing.
func SeedAdminFromConfig(appc config.App) {
	username := appc.AdminUser
	password := appc.AdminPass
	if username == "" {
		username = "admin"
	}
	if password == "" {
		password = "admin123"
	}

	var count int64
	if err := DB().Model(&entities.AdminUser{}).Where("username = ?", username).Count(&count).Error; err != nil {
		log.Printf("seed: failed to check admin user: %v", err)
		return
	}
	if count > 0 {
		return
	}

	hash, err := utils.HashPassword(password)
	if err != nil {
		log.Printf("seed: failed to hash admin password: %v", err)
		return
	}
	u := entities.AdminUser{Username: username, PasswordHash: hash, Role: "admin"}
	if err := DB().Create(&u).Error; err != nil {
		log.Printf("seed: failed to create admin user: %v", err)
		return
	}
	log.Printf("seed: created default admin '%s'", username)
}
