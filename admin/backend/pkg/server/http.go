package server

import (
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"github.com/Depado/ginprom"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/nocytech/kpss-plus-admin/app/api/routes"
	"github.com/nocytech/kpss-plus-admin/pkg/config"
	"github.com/nocytech/kpss-plus-admin/pkg/database"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/auth"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/explanation"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/explanationimport"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/lesson"
	logDomain "github.com/nocytech/kpss-plus-admin/pkg/domains/log"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/question"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/subject"
	"github.com/nocytech/kpss-plus-admin/pkg/domains/user"
	"github.com/nocytech/kpss-plus-admin/pkg/middleware"
)

func LaunchHttpServer(appc config.App, allows config.Allows) {
	gin.SetMode(gin.ReleaseMode)
	app := gin.New()
	app.Use(gin.Logger())
	app.Use(gin.Recovery())

	app.Use(cors.New(cors.Config{
		AllowMethods:     allows.Methods,
		AllowHeaders:     allows.Headers,
		AllowOrigins:     allows.Origins,
		AllowCredentials: false,
		MaxAge:           12 * time.Hour,
	}))

	p := ginprom.New(ginprom.Engine(app), ginprom.Subsystem("gin"), ginprom.Path("/metrics"))
	app.Use(p.Instrument())

	middleware.SetInternalAPIKey(appc.InternalAPIKey)
	db := database.DB()

	api := app.Group("/api/v1")
	public := api
	adminProtected := api.Group("")
	adminProtected.Use(middleware.AdminAuthorized(appc))
	internal := api.Group("")
	internal.Use(middleware.XApiKey())

	// public endpoints
	public.GET("/healthz", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// serve static openapi json
	public.GET("/openapi.json", func(c *gin.Context) {
		c.File("./docs/openapi.json")
	})

	authRepo := auth.NewRepository()
	authService := auth.NewService(authRepo, appc)
	routes.RegisterAuthRoutes(public, adminProtected, authService)

	userRepo := user.NewRepository()
	userService := user.NewService(userRepo)
	routes.RegisterUserRoutes(adminProtected, userService)

	lessonRepo := lesson.NewRepository(db)
	lessonService := lesson.NewService(lessonRepo)
	routes.LessonRoutes(adminProtected, lessonService)

	subjectRepo := subject.NewRepository(db)
	subjectService := subject.NewService(subjectRepo)
	routes.SubjectRoutes(adminProtected, subjectService)

	explanationRepo := explanation.NewRepository(db)
	explanationService := explanation.NewService(explanationRepo)
	importService := explanationimport.NewService(db, explanationimport.Config{
		WebhookURL:  appc.AutomationWebhookURL,
		HeaderName:  appc.AutomationHeaderName,
		HeaderValue: appc.AutomationHeaderValue,
		Timeout:     time.Duration(appc.AutomationTimeoutSec) * time.Second,
	})
	routes.ExplanationRoutes(adminProtected, explanationService, importService)

	questionRepo := question.NewRepository(db)
	questionService := question.NewService(questionRepo)
	routes.QuestionRoutes(adminProtected, questionService)

	logRepo := logDomain.NewRepository(db)
	logService := logDomain.NewService(logRepo)
	logDomain.SetDefaultService(logService)
	routes.LogRoutes(adminProtected, logService)
	routes.LogInternalRoutes(internal, logService)

	if _, err := os.Stat("./dist/index.html"); err == nil {
		// Serve explicit mappings for known asset directories and files
		app.Static("/static", "./dist/static")
		app.Static("/assets", "./dist/assets")
		app.StaticFile("/favicon.ico", "./dist/favicon.ico")
		app.StaticFile("/robots.txt", "./dist/robots.txt")
		app.StaticFile("/manifest.json", "./dist/manifest.json")
		app.StaticFile("/asset-manifest.json", "./dist/asset-manifest.json")
		app.StaticFile("/logo192.png", "./dist/logo192.png")
		app.StaticFile("/logo512.png", "./dist/logo512.png")
		app.StaticFile("/service-worker.js", "./dist/service-worker.js")

		// Serve privacy policy
		app.StaticFile("/privacy-policy.html", "./dist/privacy-policy.html")
		app.StaticFile("/privacy", "./dist/privacy-policy.html")
		app.StaticFile("/gizlilik", "./dist/privacy-policy.html")

		// Serve the main index.html for root
		app.GET("/", func(c *gin.Context) {
			c.File("./dist/index.html")
		})

		// Fallback for unknown routes (SPA routing)
		app.NoRoute(func(c *gin.Context) {
			p := c.Request.URL.Path
			// Do not hijack API routes
			if strings.HasPrefix(p, "/api/") {
				c.Status(404)
				return
			}
			// If the request looks like a file (has an extension), only serve if it exists
			if ext := path.Ext(p); ext != "" {
				full := path.Join("./dist", p)
				if _, err := os.Stat(full); err == nil {
					c.File(full)
					return
				}
				c.Status(404)
				return
			}
			// Otherwise, serve index.html for SPA client-side routes
			c.File("./dist/index.html")
		})
	}

	fmt.Println("Admin server running on port ", appc.Port)
	app.Run(":" + appc.Port)
}
