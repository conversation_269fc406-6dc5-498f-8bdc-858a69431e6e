package config

import (
	"log"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"gopkg.in/yaml.v3"
)

type Config struct {
	App      App      `yaml:"app"`
	Database Database `yaml:"database"`
	Allows   Allows   `yaml:"allows"`
}

type App struct {
	Name                  string `yaml:"name"`
	Port                  string `yaml:"port"`
	Host                  string `yaml:"host"`
	BaseUrl               string `yaml:"base_url"`
	JwtIssuer             string `yaml:"jwt_issuer"`
	JwtSecret             string `yaml:"jwt_secret"`
	AdminUser             string `yaml:"admin_user"`
	AdminPass             string `yaml:"admin_pass"`
	InternalAPIKey        string `yaml:"internal_api_key"`
	AutomationWebhookURL  string `yaml:"automation_webhook_url"`
	AutomationHeaderName  string `yaml:"automation_header_name"`
	AutomationHeaderValue string `yaml:"automation_header_value"`
	AutomationTimeoutSec  int    `yaml:"automation_timeout_sec"`
}

type Database struct {
	Host string `yaml:"host"`
	Port string `yaml:"port"`
	User string `yaml:"user"`
	Pass string `yaml:"pass"`
	Name string `yaml:"name"`
}

type Allows struct {
	Methods []string `yaml:"methods"`
	Origins []string `yaml:"origins"`
	Headers []string `yaml:"headers"`
}

func InitConfig() *Config {
	var cfg Config

	// Try to load from config file first
	filename, _ := filepath.Abs("./config.yaml")
	data, err := os.ReadFile(filename)
	if err == nil {
		if err := yaml.Unmarshal(data, &cfg); err != nil {
			log.Fatalf("config: unmarshal error: %v", err)
		}
	}

	// Override with environment variables if they exist
	if val := os.Getenv("APP_NAME"); val != "" {
		cfg.App.Name = val
	}
	if val := os.Getenv("APP_PORT"); val != "" {
		cfg.App.Port = val
	}
	if val := os.Getenv("APP_HOST"); val != "" {
		cfg.App.Host = val
	}
	if val := os.Getenv("APP_BASE_URL"); val != "" {
		cfg.App.BaseUrl = val
	}
	if val := os.Getenv("APP_JWT_ISSUER"); val != "" {
		cfg.App.JwtIssuer = val
	}
	if val := os.Getenv("APP_JWT_SECRET"); val != "" {
		cfg.App.JwtSecret = val
	}
	if val := os.Getenv("APP_ADMIN_USER"); val != "" {
		cfg.App.AdminUser = val
	}
	if val := os.Getenv("APP_ADMIN_PASS"); val != "" {
		cfg.App.AdminPass = val
	}
	if val := os.Getenv("APP_INTERNAL_API_KEY"); val != "" {
		cfg.App.InternalAPIKey = val
	}
	if val := os.Getenv("X_API_KEY"); val != "" {
		cfg.App.InternalAPIKey = val
	}
	if val := os.Getenv("AUTOMATION_WEBHOOK_URL"); val != "" {
		cfg.App.AutomationWebhookURL = val
	}
	if val := os.Getenv("AUTOMATION_HEADER_NAME"); val != "" {
		cfg.App.AutomationHeaderName = val
	}
	if val := os.Getenv("AUTOMATION_HEADER_VALUE"); val != "" {
		cfg.App.AutomationHeaderValue = val
	}
	if val := os.Getenv("AUTOMATION_TIMEOUT_SEC"); val != "" {
		if parsed, err := strconv.Atoi(val); err == nil {
			cfg.App.AutomationTimeoutSec = parsed
		}
	}

	if val := os.Getenv("DB_HOST"); val != "" {
		cfg.Database.Host = val
	}
	if val := os.Getenv("DB_PORT"); val != "" {
		cfg.Database.Port = val
	}
	if val := os.Getenv("DB_USER"); val != "" {
		cfg.Database.User = val
	}
	if val := os.Getenv("DB_PASS"); val != "" {
		cfg.Database.Pass = val
	}
	if val := os.Getenv("DB_NAME"); val != "" {
		cfg.Database.Name = val
	}

	if val := os.Getenv("CORS_ORIGINS"); val != "" {
		cfg.Allows.Origins = strings.Split(val, ",")
	}

	if cfg.App.AutomationTimeoutSec <= 0 {
		cfg.App.AutomationTimeoutSec = 120
	}

	return &cfg
}
