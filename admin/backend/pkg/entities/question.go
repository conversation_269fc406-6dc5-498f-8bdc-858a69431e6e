package entities

import "github.com/google/uuid"

const (
	DifficultyEasy   = "easy"
	DifficultyMedium = "medium"
	DifficultyHard   = "hard"
)

const (
	QuestionTypeSingleChoice   = "single_choice"
	QuestionTypeMultipleChoice = "multiple_choice"
	QuestionTypeTrueFalse      = "true_false"
	QuestionTypeFillBlank      = "fill_blank"
)

type Question struct {
	Base
	LessonID      uuid.UUID  `json:"lesson_id" gorm:"type:uuid;index"`
	SubjectID     uuid.UUID  `json:"subject_id" gorm:"type:uuid;index"`
	ExplanationID *uuid.UUID `json:"explanation_id" gorm:"type:uuid;index"`

	Type        string `json:"type"`
	Difficulty  string `json:"difficulty"`
	Source      string `json:"source"`
	Stem        string `json:"stem"`
	IsPublished bool   `json:"is_published" gorm:"default:true"`
}
