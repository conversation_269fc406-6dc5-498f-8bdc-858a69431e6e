package dtos

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

// ExplanationImportResponse represents the payload returned after running the import workflow.
type ExplanationImportResponse struct {
	Subjects     []entities.Subject       `json:"subjects"`
	Explanations []entities.Explanation   `json:"explanations"`
	Payload      ExplanationImportPayload `json:"payload"`
}

// ExplanationImportPayload mirrors the automation output for client-side review.
type ExplanationImportPayload struct {
	DersNotlari []ExplanationImportTopic `json:"ders_notlari"`
}

// ExplanationImportTopic is a top-level subject with its associated sub headings.
type ExplanationImportTopic struct {
	UstKonu      string                        `json:"ust_konu"`
	AltBasliklar []ExplanationImportSubheading `json:"alt_basliklar"`
}

// ExplanationImportSubheading defines a single extracted explanation candidate.
type ExplanationImportSubheading struct {
	Baslik string `json:"baslik"`
	Ozet   string `json:"ozet"`
}
