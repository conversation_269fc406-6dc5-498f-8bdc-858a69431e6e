package dtos

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

type CreateLessonRequest struct {
	Title       string  `json:"title"`
	Description *string `json:"description"`
	Body        *string `json:"body"`
	IsPublished bool    `json:"is_published"`
}

type UpdateLessonRequest struct {
	Title       *string `json:"title"`
	Description *string `json:"description"`
	Body        *string `json:"body"`
	IsPublished *bool   `json:"is_published"`
}

type LessonListResponse struct {
	Data  []entities.Lesson `json:"data"`
	Total int64             `json:"total"`
}

type LessonResponse struct {
	Data entities.Lesson `json:"data"`
}
