package dtos

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

type CreateQuestionRequest struct {
	LessonID      string  `json:"lesson_id"`
	SubjectID     string  `json:"subject_id"`
	ExplanationID *string `json:"explanation_id"`
	Type          string  `json:"type"`
	Difficulty    string  `json:"difficulty"`
	Source        string  `json:"source"`
	Stem          string  `json:"stem"`
	IsPublished   bool    `json:"is_published"`
}

type UpdateQuestionRequest struct {
	LessonID      *string `json:"lesson_id"`
	SubjectID     *string `json:"subject_id"`
	ExplanationID *string `json:"explanation_id"`
	Type          *string `json:"type"`
	Difficulty    *string `json:"difficulty"`
	Source        *string `json:"source"`
	Stem          *string `json:"stem"`
	IsPublished   *bool   `json:"is_published"`
}

type QuestionListResponse struct {
	Data  []entities.Question `json:"data"`
	Total int64               `json:"total"`
}

type QuestionResponse struct {
	Data entities.Question `json:"data"`
}
