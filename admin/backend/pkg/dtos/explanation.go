package dtos

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

type CreateExplanationRequest struct {
	LessonID    string  `json:"lesson_id"`
	SubjectID   string  `json:"subject_id"`
	Title       string  `json:"title"`
	Summary     *string `json:"summary"`
	Body        *string `json:"body"`
	IsPublished bool    `json:"is_published"`
}

type UpdateExplanationRequest struct {
	LessonID    *string `json:"lesson_id"`
	SubjectID   *string `json:"subject_id"`
	Title       *string `json:"title"`
	Summary     *string `json:"summary"`
	Body        *string `json:"body"`
	IsPublished *bool   `json:"is_published"`
}

type ExplanationListResponse struct {
	Data  []entities.Explanation `json:"data"`
	Total int64                  `json:"total"`
}

type ExplanationResponse struct {
	Data entities.Explanation `json:"data"`
}
