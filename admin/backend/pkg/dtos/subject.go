package dtos

import "github.com/nocytech/kpss-plus-admin/pkg/entities"

type CreateSubjectRequest struct {
	LessonID      string  `json:"lesson_id"`
	MainSubjectID *string `json:"main_subject_id"`
	Title         string  `json:"title"`
	Description   *string `json:"description"`
	IsActive      bool    `json:"is_active"`
}

type UpdateSubjectRequest struct {
	LessonID      *string `json:"lesson_id"`
	MainSubjectID *string `json:"main_subject_id"`
	Title         *string `json:"title"`
	Description   *string `json:"description"`
	IsActive      *bool   `json:"is_active"`
}

type SubjectListResponse struct {
	Data  []entities.Subject `json:"data"`
	Total int64              `json:"total"`
}

type SubjectResponse struct {
	Data entities.Subject `json:"data"`
}
