package middleware

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

var internalAPIKey string

// SetInternalAP<PERSON><PERSON>ey configures the middleware to validate incoming X-API-Key headers.
func SetInternalAPIKey(key string) {
	internalAPIKey = key
}

// XApi<PERSON>ey enforces requests to supply a matching X-API-Key header.
// If no key is configured, the middleware allows the request to proceed.
func XApiKey() gin.HandlerFunc {
	return func(c *gin.Context) {
		if internalAPIKey == "" {
			c.Next()
			return
		}
		if c.GetHeader("X-API-Key") != internalAPIKey {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid api key"})
			return
		}
		c.Next()
	}
}
