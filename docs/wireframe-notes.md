# Wireframe Notları (Ekran Başına)

Bu notlar tasarımcıya grid, iç<PERSON>k b<PERSON>leri, durumlar ve CTA yerleşimleri için referans verir. Bileşenler için `components/ui/*` kullanılacak.

## Content
### ContentHomeScreen
- Başlık + arama <PERSON> (üst), kategori/subject filtre chips (yatay scroll).
- Bölümler: <PERSON><PERSON><PERSON><PERSON> (yatay carousel), <PERSON><PERSON><PERSON> (2 sütun grid kartlar), <PERSON> g<PERSON> (auth).
- Kart: görse<PERSON>/ikon, ba<PERSON><PERSON><PERSON><PERSON>, etiketler, kısa a<PERSON>ı<PERSON>, “+ Kütüphane” ikincil CTA.
- Empty: önerilen anahtar kelimeler; Error: retry butonu; Loading: skeleton grid.

### ContentSearchScreen
- Üstte arama input + filtre butonu; altında sonuç listesi (grid/list toggle ops.).
- <PERSON> (chip), önerilen etiketler.
- Empty: “<PERSON><PERSON><PERSON> bulunamadı” + öneriler; Error/Loading standard.

### ContentDetailScreen
- <PERSON> (thumb/placeholder), b<PERSON><PERSON><PERSON><PERSON><PERSON>, meta (süre, zor<PERSON>, subject), etiketler.
- Altında açıklama, istatistikler (görüntüleme/beğeni), benzer içerikler (yatay liste).
- Birincil CTA: Oynat/İncele; ikincil: Kütüphane, Paylaş.
- Guest banner (limit bar) üstünde sabit.

### ContentPlayerScreen
- Video/Audio kontroller ya da makale/PDF okuyucu; üst bar: geri, başlık, ilerleme.
- Alt sticky bar: ilerleme yüzdesi + “Tamamlandı”/“Not ekle”.
- Not modalı: kısa metin alanı + Kaydet.

### ContentLibraryScreen
- Sekmeler: Devam eden / Tamamlanan; liste kartları, ilerleme çubuğu.
- Empty: “Kütüphanen boş” + Keşfet’e git.

## Quiz
### QuizHomeScreen
- Filtreler (zorluk, subject) + arama; bölümler: Popüler, Önerilen, Son eklenen.
- Kart: başlık, soru sayısı, süre, ort. skor; “Başla” butonu.

### QuizDetailScreen
- Başlık, meta (soru sayısı, süre, zorluk), açıklama.
- İstatistik minik kartlar (deneme, ort. skor), liderlik mini görünüm.
- Benzer quizler (yatay); CTA: Başla, Liderlik, Meydan oku.
- Guest: canStart uyarısı ribbon.

### QuizPlayScreen
- Üstte sayaç ve ilerleme; ortada soru metni; altta 4 seçenek (dokunma alanı geniş).
- “Sonraki” birincil; “Çık” ikincil (confirm modal).
- Zaman biterse otomatik cevap kilitleme akışı.

### QuizResultScreen
- Skor yüzdesi (daire gauge), doğru/yanlış sayısı, süre.
- Sorular listesi (doğru/yanlış ikonları) + “Çözümleri incele”.
- Paylaş kartı ve Liderliğe git CTA.

### QuizLeaderboardScreen
- Sekmeler: Global / Arkadaş; liste satırı: sıra, avatar, ad, skor.
- Kullanıcının pozisyonu sabit üst veya highlight.

## Battle (Kapışma)
### BattleHomeScreen
- Özet kartlar: toplam kapışma, win rate, avg score, best rank.
- Grup listesi ve arkadaş meydan okuma kısa listeleri.
- “Grup oluştur” birincil CTA; “Davet kodu ile katıl” ikincil.

### GroupBattleScreen (Liste/Detay/Lobby)
- Liste: kartlarda isim, durum (waiting/active), katılımcı sayısı.
- Detay/Lobby: katılımcılar avatar grid, davet kodu alanı + “Kopyala/Paylaş”.
- Başlat (sadece creator), Katıl/Ayrıl; Lobby’de geri sayım (ops.).

### GroupBattleResults
- Tablo görünüm: sıra, ad, skor, süre; kupa ikonları ilk 3.
- “Tekrar oyna” CTA.

### FriendChallengeScreen
- Sekmeler: Gönderilen / Alınan; kart: durum rozetleri, mesaj, quiz başlığı.
- Detayda Accept/Decline, sonuçlar linki.

## Sosyal
### SocialHome/TimelineScreen
- Post kartı: avatar, ad, tarih, metin, medya; alt bar: beğen, yorum, paylaş.
- Yeni post FAB veya üst bar butonu.

### PostDetailScreen
- Büyük içerik + yorum listesi (sonsuz scroll); yorum kutusu alt sabit.

### CreatePostScreen
- Metin alanı, medya picker alanı, önizleme; “Paylaş” birincil.

## Menü
### MenuHomeScreen
- Grid veya liste kartları; her kart ikon + başlık + kısa açıklama.

### ProfileScreen
- Üstte kapak + avatar, ad/username, toplam skor; rozet kısayolu.
- Token bakiyesi küçük rozet; “Profili düzenle”.

### SettingsScreen
- Bölümler: Genel (tema/dil), Bildirimler, Güvenlik; switch ve select kontroller.

### BadgesScreen
- Grid rozet; kilitli olanlar gri; detay modalında açıklama ve kazanım kriteri.

### Progress/Analytics/Goals/Notifications
- Progress: grafik + liste; Analytics: kartlar + minigrafikler.
- Goals: hedef formu + streak görselleştirme.
- Notifications: filtre sekmeleri, swipe actions (okundu/sil).

## Modals
- LoginModal: basit form/CTA; GuestLimitModal: limit bar + faydalar listesi + upgrade CTA.
- ShareModal: platform butonları; Filter/Search: chips + apply/reset alt bar.
### AssistantQuizScreen
- Üst bar: geri, asistan avatarı/adı, sessize alma.
- Soru alanı: büyük, okunabilir metin + TTS durum göstergesi ("Okunuyor...").
- Seçenekler: büyük dokunma alanlı butonlar; alternatif olarak sesle cevap için mikrofon FAB.
- Alt bar: "Tekrar oku", "İpucu" (ops), "Atla" (ops), sayaç.
- İzin akışı: mikrofon izni iste (modal); izin yoksa yönlendirme metni.
- Durumlar: reading/listening/processing; hata ve yeniden dene akışları.

