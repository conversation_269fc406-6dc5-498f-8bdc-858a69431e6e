# KPSS Plus – <PERSON><PERSON> Checklist

B<PERSON> belge, mobil uygulama için backend domain’leri ve mevcut servislere (kpssplus/src/services/*, navigation/*, types/*) göre ekran bazlı tasarım brieftir. Her ekran için veri al<PERSON>, API uçları, CTA’lar, boş/hata/loader durumları, analytics ve guest/auth farklılıkları listelenmiştir.

Kaynaklar:
- Kod: `kpssplus/src/services/*`, `kpssplus/src/navigation/*`, `kpssplus/src/types/index.ts`
- Tema/Bileşenler: `kpssplus/src/theme/*`, `kpssplus/src/components/ui/*`

## İlgili Belgeler
- Figma Sayfa Hiyerarşisi: `docs/figma-pages.md`
- Wireframe Notları: `docs/wireframe-notes.md`
- Component Mapping: `docs/component-mapping.md`
- Analytics Sözlüğü: `docs/analytics-dictionary.md`

## Konvansiyonlar
- CTAs: Birincil aksiyon tek ve belirgin olmalı; ikincil aksiyon metinsel.
- Durumlar: Loading skeleton, Empty state (CTA ile), Error (retry butonlu) standart.
- Guest İşaretleme: Kilit ikonu + açıklama + “Giriş yap” butonu pattern’i.
- Analytics isimlendirme: kebab-case events, snake_case parametreler.

## Auth & Onboarding
### LoginScreen (`/login` ve varyantları)
- Veri Alanları: identifier (email/kullanıcı/telefon), password, sosyal butonlar, “misafir devam et”.
- API: `POST /login`, alternatifler: `/auth/login/username`, `/login/email`, `/login/phone`; guest: `POST /auth/guest`.
- CTA’lar: Giriş yap, Google ile, Apple ile, Kayıt ol, Şifremi unuttum, Misafir devam.
- Durumlar: invalid_credentials, rate_limit, network_error, loading.
- Analytics: login_click, login_success, login_fail, social_login_click, guest_continue_click.
- Guest/Auth: Guest başarıda limit banner; token saklama: AsyncStorage.

### RegisterScreen (`/auth/register`)
- Veri Alanları: username, email, password, firstName, lastName, phone (opsiyonel).
- API: `POST /auth/register`.
- CTA’lar: Kayıt ol, Girişe dön.
- Durumlar: duplicate_email/username, weak_password, loading.
- Analytics: register_click, register_success, register_fail.

### ForgotPasswordScreen (`/auth/forgot-password`)
- Veri Alanları: email.
- API: `POST /auth/forgot-password`.
- CTA’lar: Mail gönder, Girişe dön.
- Durumlar: success_info, rate_limit, not_found, loading.
- Analytics: forgot_submit, forgot_success, forgot_fail.

### ResetPasswordScreen (`/auth/reset-password`)
- Veri Alanları: token, newPassword.
- API: `POST /auth/reset-password`.
- CTA’lar: Şifreyi değiştir.
- Durumlar: token_expired/invalid, weak_password, loading.
- Analytics: reset_submit, reset_success, reset_fail.

### VerifyOTPScreen (opsiyonel) (`/auth/verify-otp`)
- Veri Alanları: phone, otp, sayaç.
- API: `POST /auth/verify-otp`.
- CTA’lar: Doğrula, Kodu tekrar gönder.
- Durumlar: wrong_otp, expired, loading.
- Analytics: otp_submit, otp_resend.

## Ana Tablar (`kpssplus/src/navigation/MainTabNavigator.tsx`)
### Content (Dersler)
- Ekranlar: ContentHomeScreen, ContentSearchScreen, ContentDetailScreen, ContentPlayerScreen, ContentLibraryScreen, TopicListScreen, TopicDetailScreen, StudyTopicsScreen.
- Genel API: `GET /content`, `/content/popular`, `/content/recommended`(auth), `/content/search`, `/content/{id}`, `/content/{id}/stats`, `/content/{id}/library` (POST/DELETE), `/content/progress` (GET/PUT).

### ContentHomeScreen
- Veri: konu/subject filtreleri, önerilen ve popüler listeler, arama çubuğu.
- API: `GET /content`, `/content/popular`, `/content/recommended`(auth).
- CTA: Detay, Kütüphaneye ekle/çıkar, Filtrele, Ara.
- Durum: empty (öneri keywordleri), loading skeleton, offline, error+retry.
- Analytics: content_impression, content_card_click, filter_open, filter_apply.
- Guest/Auth: `contentService.isGuestLimitReached()` gösterimi.

### ContentSearchScreen
- Veri: arama input, sonuç listesi, filtre chips.
- API: `GET /content/search?query&page&limit`.
- CTA: Filtreleri aç, Temizle.
- Durum: no_results (önerilerle), loading, error.
- Analytics: content_search, content_search_result_click.

### ContentDetailScreen
- Veri: title, description, type, subject, duration, difficulty, tags, view/like counts.
- API: `GET /content/{id}`, `/content/{id}/stats`, `POST/DELETE /content/{id}/library`.
- CTA: Oynat/İncele, Kütüphaneye ekle/çıkar, Paylaş.
- Durum: not_found, stats_error, loading.
- Analytics: content_detail_view, add_to_library, share_open.
- Guest/Auth: View limit uyarısı, upgrade CTA.

### ContentPlayerScreen
- Veri: player/reader, ilerleme barı, not alanı.
- API: `GET /content/{id}/progress`, `PUT /content/progress`.
- CTA: Not kaydet, Tamamlandı işaretle.
- Durum: progress_write_error, resume_prompt, loading.
- Analytics: content_start, progress_update, content_complete.
- Guest/Auth: Guest’te progress yazma kapalı; login CTA.

### ContentLibraryScreen
- Veri: devam eden/tamamlanan sekmeleri, liste kartları.
- API: `GET /content/library`.
- CTA: Detay, Kaldır.
- Durum: empty_library → Keşfet’e git; loading; error.
- Analytics: library_open, library_remove.
- Guest/Auth: Guest’te kütüphane yerine login CTA.

### TopicList/TopicDetail/StudyTopicsScreen
- Veri: konu listeleri, konu içi içerikler/quizler.
- API: Panel topic/subject (mobilde servis eklenecek).
- CTA: Konuya gir, İçerik aç, Quiz başlat.
- Durum: konu_yok, loading, error.
- Guest/Auth: Guest konu sayısı sınırlı.

## Quiz
- Ekranlar: QuizHomeScreen, QuizSearchScreen, QuizDetailScreen, QuizPlayScreen, QuizResultScreen, QuizLeaderboardScreen.
- Genel API: `GET /quiz`, `/quiz/popular`, `/quiz/recommended`(auth), `/quiz/search`, `/quiz/{id}`, `/quiz/{id}/statistics`, `/quiz/{id}/leaderboard`, `/quiz/{id}/similar`, `/quiz/{id}/start` (POST), `/quiz/sessions/{sid}/answer` (POST), `/quiz/sessions/{sid}/finish` (POST), `/quiz/results`, `/quiz/results/{rid}`.

### QuizHomeScreen
- Veri: popüler/önerilen/son eklenen sekmeleri, filtreler.
- API: `GET /quiz`, `/quiz/popular`, `/quiz/recommended`(auth).
- CTA: Detay, Başla, Ara, Filtrele.
- Durum: empty, loading, error.
- Analytics: quiz_impression, quiz_card_click, filter_apply.
- Guest/Auth: `quizService.isGuestQuizLimitReached()` gösterimi.

### QuizSearchScreen
- Veri: arama input, sonuç grid/list.
- API: `GET /quiz/search`.
- CTA: Filtrele, Temizle.
- Durum: no_results, loading, error.
- Analytics: quiz_search, quiz_search_result_click.

### QuizDetailScreen
- Veri: açıklama, subject, zorluk, soru sayısı, süre, ortalama skor, deneme, benzerler.
- API: `GET /quiz/{id}`, `/quiz/{id}/statistics`, `/quiz/{id}/leaderboard?type=global|friends`, `/quiz/{id}/similar`.
- CTA: Başla, Liderlik, Arkadaşa meydan oku, Paylaş.
- Durum: stats_error, leaderboard_error, loading.
- Analytics: quiz_detail_view, start_click, challenge_open.
- Guest/Auth: `quizService.canStartQuiz()` uyarısı ve login CTA.

### QuizPlayScreen
- Veri: soru, 4 seçenek, ilerleme, kalan süre.
- API: `POST /quiz/{id}/start`, `GET /quiz/{id}/questions`, `POST /quiz/sessions/{sid}/answer`.
- CTA: Cevapla, Sonraki, Çık uyarısı.
- Durum: network_retry, resume_session, loading.
- Analytics: question_view, answer_submit, quiz_abandon.
- Guest/Auth: Limit aşımı → GuestLimitModal.

### AssistantQuizScreen (Sesli Soru Asistanı)
- Amaç: Soruları asistanın sorduğu, sesli/etkileşimli quiz deneyimi.
- Veri: aktif quiz veya önerilen quiz; akış QuizPlay ile aynı temelde.
- API: `POST /quiz/{id}/start`, `GET /quiz/{id}/questions`, `POST /quiz/sessions/{sid}/answer`, `POST /quiz/sessions/{sid}/finish`. TTS/STT cihaz tarafı (platform servisleri) veya 3P (tasarımsal not).
- CTA: "Sesle cevapla", "Tekrar oku", "İpucu" (ops), "Atla" (ops), "Durdur".
- Durum: mic_izin_verilmedi, mic_hatası, okuyorum (reading), bekliyor (listening), network_retry, loading.
- Analytics: assistant_open, assistant_question_read, assistant_voice_answer, assistant_hint_click, assistant_skip, assistant_end.
- Guest/Auth: `quizService.canStartQuiz()` ile limit kontrolü; guest limit mesajı.

### QuizResultScreen
- Veri: skor, doğru/yanlış, süre, puan, özet grafikleri.
- API: `POST /quiz/sessions/{sid}/finish`, `GET /quiz/results/{rid}`.
- CTA: Çözümleri incele, Yeniden dene, Paylaş, Liderlik.
- Durum: result_not_ready, loading, error.
- Analytics: quiz_finish, result_view, share_click.

### QuizLeaderboardScreen
- Veri: global/arkadaş sekmeleri, kullanıcının sırası.
- API: `GET /quiz/{id}/leaderboard?type=global|friends`.
- CTA: Arkadaş davet, Tekrar dene.
- Durum: empty_leaderboard, loading.
- Analytics: leaderboard_view, leaderboard_tab_switch.

## Battle (Kapışma)
- Ekranlar: BattleHomeScreen, GroupBattleScreen (liste/detay/lobby), GroupBattleResults, FriendChallengeScreen.
- Genel API: Grup: `/battles/group` (GET/POST), `/battles/group/{id}` (GET), `/battles/group/{id}/join` (POST), `/leave` (POST), `/start` (POST), `/invite-code` (POST), `/join-by-code` (POST), `/results` (GET). Arkadaş: `/challenges/friend` (GET/POST), `/challenges/friend/{id}` (GET), `/respond` (POST), `/result` (POST), `/results` (GET). İstatistik: `/battles/stats`.

### BattleHomeScreen
- Veri: istatistik kartları (winRate, avgScore, bestRank), özet listeler.
- API: `GET /battles/stats`, `GET /battles/group`, `GET /challenges/friend`.
- CTA: Grup oluştur, Davet kodu ile katıl, Arkadaşa meydan oku.
- Durum: no_battles_yet, loading, error.
- Analytics: battle_home_view, battle_create_open.

### GroupBattleScreen (Liste/Detay/Lobby)
- Veri: bekleyen/aktif listeler; detay: katılımcılar, süre, davet kodu.
- API: `GET /battles/group`, `GET /battles/group/{id}`, `POST /battles/group/{id}/join`, `POST /battles/group/{id}/leave`, `POST /battles/group/{id}/start`, `POST /battles/group/{id}/invite-code`, `POST /battles/group/join-by-code`.
- CTA: Katıl/Ayrıl, Başlat (yetkili), Davet kodu üret/paylaş.
- Durum: invite_invalid, full_lobby, loading.
- Analytics: group_join, group_start, invite_generate.

### GroupBattleResults
- Veri: sıralama, skorlar, tekrar oyna.
- API: `GET /battles/group/{id}/results`.
- CTA: Tekrar oyna, Paylaş.
- Durum: loading, error.
- Analytics: group_results_view.

### FriendChallengeScreen
- Veri: Gönderilen/Alınan sekmeleri, durum (pending/accepted/declined/completed/expired).
- API: `POST /challenges/friend`, `GET /challenges/friend?type=sent|received`, `GET /challenges/friend/{id}`, `POST /challenges/friend/{id}/respond`, `POST /challenges/friend/{id}/result`, `GET /challenges/friend/{id}/results`.
- CTA: Kabul/Red, Sonuçları gör, Yeniden gönder.
- Durum: challenge_expired, loading, error.
- Analytics: challenge_send, challenge_accept, challenge_decline.

## Social (Sosyal)
- Ekranlar: SocialHomeScreen/TimelineScreen, PostDetailScreen, CreatePostScreen, FriendsScreen, FriendRequestsScreen, UserSearchScreen, LeaderboardScreen.
- Genel API: timeline/social domain (eklencek): `GET/POST /timeline`, `GET /timeline/{id}`, `GET/POST /timeline/{id}/comments`, like/share uçları; genel liderlik: `GET /leaderboard` veya quiz’e özel `/quiz/{id}/leaderboard`.

### SocialHome/TimelineScreen
- Veri: feed kartları (metin/medya), like/comment sayaçları.
- API: `GET /timeline`, `POST /timeline`, `GET/POST /timeline/{id}/comments` (eklencek); like/share uçları.
- CTA: Gönderi oluştur, Beğen, Yorum yap, Paylaş.
- Durum: empty_feed, loading, error.
- Analytics: feed_view, post_like, comment_add, create_post_open.

### PostDetailScreen
- Veri: gönderi içeriği, yorum listesi, etkileşimler.
- API: `GET /timeline/{id}`, `GET /timeline/{id}/comments`, `POST /timeline/{id}/comments`.
- CTA: Yorum gönder, Paylaş, Takip et (opsiyon).
- Durum: loading, error.
- Analytics: post_view, comment_submit.

### CreatePostScreen
- Veri: metin, medya (Cloudinary entegrasyonu panelde hazır).
- API: `POST /timeline` + upload.
- CTA: Paylaş.
- Durum: upload_progress, error.
- Analytics: post_create_submit.

### Friends/FriendRequests/UserSearch/Leaderboard
- API: social/friendship domain (eklencek), `GET /leaderboard` genel.
- CTA: Arkadaş ekle/kabul/ret, Ara.
- Analytics: friends_view, friend_add, friend_accept.

## Menu (Menü)
- Ekranlar: MenuHomeScreen, ProfileScreen, SettingsScreen, BadgesScreen, ProgressScreen, AnalyticsScreen, GoalsScreen, NotificationsScreen, HelpScreen, AboutScreen, TokenStore (alt sayfalar).

### MenuHomeScreen
- Veri: menü kartları (Profil, Ayarlar, Rozetler, İlerleme, Analitik, Hedefler, Bildirimler, Token Mağazası, Yardım, Hakkında).
- Analytics: menu_open, menu_item_click.

### ProfileScreen
- Veri: avatar, kullanıcı bilgileri, toplam skor, rozet kısayolu, token bakiyesi.
- API: `GET /user`, `GET /user/tokens`.
- CTA: Profili düzenle, Token mağazası.
- Durum: loading, error.
- Analytics: profile_view, profile_edit_open.

### SettingsScreen
- Veri: bildirim tercihleri, dil/tema, güvenlik, cache.
- API: preferences domain: `GET /preferences`, `PUT /preferences`.
- CTA: Kaydet, Çıkış yap.
- Durum: loading, error.
- Analytics: settings_save, logout_click.

### BadgesScreen
- Veri: rozet grid (kazanılmış/açık/kapalı), detay modal.
- API: `/badge` domain.
- CTA: Rozet detayı, Paylaş.
- Durum: loading, error.
- Analytics: badge_view, badge_detail_open.

### ProgressScreen
- Veri: içerik ilerleme, quiz sonuç özetleri; grafikler.
- API: `GET /content/progress`, `GET /quiz/results`.
- Analytics: progress_view.

### AnalyticsScreen
- Veri: `UserAnalytics` (toplam süre, görüntüleme, deneme, ort skor, streak, rozet, arkadaş, post, lastActive).
- API: `GET /analytics`.
- Analytics: analytics_view.

### GoalsScreen
- Veri: hedef (günlük süre, soru sayısı), streak.
- API: preferences/progress tabanlı (eklencek).
- CTA: Hedef oluştur/güncelle.
- Analytics: goal_set, goal_update.

### NotificationsScreen
- Veri: tip filtreleri, okundu durumu.
- API: `GET /notifications`, `PUT /notifications/{id}` (read), toplu okundu.
- CTA: Hepsini okundu yap, Sil (opsiyonel).
- Durum: empty_notifications, loading, error.
- Analytics: notifications_view, notification_click.

### HelpScreen / AboutScreen
- Statik içerik, sürüm, iletişim, gizlilik/kullanım linkleri.

### TokenStore (Menü içinde alt sayfalar)
- Token Balance/History: `GET /user/tokens`, `GET /user/tokens/history?page&limit`.
- Packages & Purchase: `GET /tokens/packages`, `POST /tokens/purchase`.
- Earn/Spend/Daily Bonus: `POST /user/tokens/earn`, `POST /user/tokens/spend`, `POST /user/tokens/daily-bonus`, `GET /user/tokens/daily-bonus/status`.
- UI: paket kartları (popüler/bonus), bakiye kartı, günlük bonus kartı (countdown).
- Analytics: token_purchase_click, token_purchase_success, daily_bonus_claim.

## Modallar (`kpssplus/src/navigation/ModalStackNavigator.tsx`)
### LoginModalScreen
- Kullanım: Auth gerektiren aksiyonda.
- CTA: Giriş yap, Misafir devam.
- Analytics: auth_modal_open.

### GuestLimitModalScreen
- Veri: `guestService.getGuestLimitations()`, `quizService.isGuestQuizLimitReached()`, `contentService.isGuestLimitReached()`, `guestService.getUpgradeBenefits()`.
- CTA: Giriş yap/Kayıt ol.
- Analytics: guest_limit_shown, upgrade_click.

### ShareModalScreen
- Veri: native paylaşım; opsiyonel `POST /quiz/{id}/share`.
- Analytics: share_modal_open.

### FilterModalScreen / SearchModalScreen
- İçerik/quiz filtreleri, kaydet/temizle.
- Analytics: filter_apply, filter_reset.

## Common
### LoadingScreen (`kpssplus/src/screens/common/LoadingScreen`)
- Kullanım: App init ve token doğrulama (opsiyonel `POST /auth/validate-token`), global isLoading.

## Guest vs Auth
- Guest Limitleri: İçerik görüntüleme ve quiz denemeleri sınırlı. Limit barı + uyarı.
- Progress/Library: Guest’te yazma ve kütüphane kapalı; login çağrısı.
- Sosyal/Battle: Guest’te kısıtlı; aksiyon yanında kilit ikonu.

## Analytics Rehberi
- Event şeması: `area_action[_detail]` (ör. `quiz_start`, `content_detail_view`).
- Parametreler: `quiz_id`, `content_id`, `subject`, `difficulty`, `result_score`, `source` (home/search/detail), `guest` (bool).

## MVP Önceliklendirme
1) Auth (Login, Register, Forgot/Reset, LoginModal)
2) Content (Home, Search, Detail, Player, Library)
3) Quiz (Home, Detail, Play, Result, Leaderboard)
4) Battle (Home, Group List/Detail/Lobby, Friend Challenge)
5) Menu (Profile, Settings, TokenStore temel akışları)
6) Sosyal (Timeline temel akışları)

Not: Figma sayfalarında [MVP] etiketiyle işaretlendi. Bkz: `docs/figma-pages.md`.

## Örnek UI Durum Metinleri (TR)

ContentHomeScreen
- Empty: "Henüz içerik bulunamadı." / "İlgi alanlarını seçerek içeriği keşfetmeye başla."
- Error: "Bir şeyler ters gitti. Lütfen tekrar deneyin."
- Offline: "Bağlantı yok. İnternete bağlandıktan sonra yeniden deneyin."

ContentSearchScreen
- No results: "Aramanıza uygun sonuç bulunamadı." / "Farklı anahtar kelimeler deneyin."

ContentDetailScreen
- Not found: "İçerik bulunamadı veya kaldırılmış."
- Stats error: "İstatistikler yüklenemedi."

ContentPlayerScreen
- Progress write error: "İlerlemeniz kaydedilemedi. Tekrar deneyin."
- Resume prompt: "Kaldığınız yerden devam etmek ister misiniz?"

ContentLibraryScreen
- Empty: "Kütüphanen boş. Beğendiğin içerikleri buraya ekleyebilirsin."

QuizHomeScreen
- Empty: "Gösterilecek quiz bulunamadı. Filtreleri değiştirin veya arama yapın."

QuizDetailScreen
- Guest limit: "Misafir limiti doldu. Devam etmek için giriş yapın."
- Leaderboard error: "Liderlik tablosu yüklenemedi."

QuizPlayScreen
- Network retry: "Bağlantı sorunu oluştu. Yeniden dene."
- Exit confirm: "Çıkarsanız ilerlemeniz kaybolabilir. Devam etmek istiyor musunuz?"

QuizResultScreen
- Not ready: "Sonuçlarınız hazırlanıyor. Lütfen birkaç saniye sonra tekrar deneyin."

QuizLeaderboardScreen
- Empty: "Bu quiz için henüz liderlik verisi yok. İlk sen ol!"

BattleHomeScreen
- Empty: "Henüz bir kapışmaya katılmadınız. Hemen bir grup oluşturun veya katılın."

GroupBattleScreen
- Invite invalid: "Davet kodu geçersiz veya süresi dolmuş."
- Full lobby: "Oda dolu. Başka bir kapışma deneyin."

SocialHome/TimelineScreen
- Empty: "Akışın şu an boş. Takip etmeye başlayın veya ilk gönderinizi paylaşın."

NotificationsScreen
- Empty: "Yeni bildiriminiz yok."

## UI State ve CTA Metinleri + Hata Kodları (Detaylı)

Bu bölüm MVP ekranları için net CTA metinleri ve arayüz durumlarını, ayrıca beklenen backend hata kodu eşlemelerini içerir. Kodlar tipiktir; gerçek API’ye göre uyarlanabilir.

### Auth
LoginScreen
- CTA’lar: "Giriş yap", "Google ile giriş", "Apple ile giriş", "Kayıt ol", "Şifremi unuttum", "Misafir olarak devam et"
- Hata Kodları → Mesajlar:
  - 401 invalid_credentials → "E-posta/Kullanıcı adı veya şifre hatalı."
  - 403 account_disabled → "Hesabınız devre dışı. Destek ile iletişime geçin."
  - 404 user_not_found → "Bu bilgilerle eşleşen hesap bulunamadı."
  - 429 too_many_attempts → "Çok fazla deneme. Lütfen birkaç dakika sonra tekrar deneyin."
  - 0 network_error → "Ağ hatası. Bağlantınızı kontrol edin."
  - -1 unexpected_error → "Beklenmeyen bir hata oluştu."

RegisterScreen
- CTA’lar: "Kayıt ol", "Giriş yap"
- Hata Kodları → Mesajlar:
  - 409 duplicate_email/username → "Bu e-posta veya kullanıcı adı kullanımda."
  - 422 weak_password → "Daha güçlü bir şifre belirleyin."
  - 0/500 → "Kayıt sırasında sorun oluştu."

ForgotPasswordScreen
- CTA’lar: "Sıfırlama maili gönder", "Girişe dön"
- Hata Kodları → Mesajlar:
  - 404 email_not_found → "Bu e-posta ile kayıt bulunamadı."
  - 429 rate_limited → "Çok sık istek. Bir süre sonra tekrar deneyin."
  - 0/500 → "İşlem başarısız. Lütfen tekrar deneyin."

ResetPasswordScreen
- CTA’lar: "Şifreyi değiştir"
- Hata Kodları → Mesajlar:
  - 400/410 token_invalid/expired → "Bağlantı geçersiz veya süresi dolmuş."
  - 422 weak_password → "Daha güçlü bir şifre belirleyin."
  - 0/500 → "İşlem başarısız."

### Content
ContentHomeScreen
- CTA’lar: "Detay", "Kütüphaneye ekle", "Filtrele", "Ara"
- Hata Kodları → Mesajlar:
  - 0 network_error → "Bağlantı yok. Daha sonra deneyin."
  - 5xx server_error → "Sunucu hatası. Lütfen tekrar deneyin."

ContentSearchScreen
- CTA’lar: "Filtreleri aç", "Temizle"
- Hata Kodları → Mesajlar: 0/5xx genel hata mesajı

ContentDetailScreen
- CTA’lar: "Oynat/İncele", "Kütüphaneye ekle/çıkar", "Paylaş"
- Hata Kodları → Mesajlar:
  - 404 not_found → "İçerik bulunamadı veya kaldırılmış."
  - 5xx stats_error → "İstatistikler yüklenemedi."

ContentPlayerScreen
- CTA’lar: "Not kaydet", "Tamamlandı"
- Hata Kodları → Mesajlar:
  - 400/500 progress_save_failed → "İlerlemeniz kaydedilemedi."
  - 0 network_error → "Bağlantı sorunu."

ContentLibraryScreen
- CTA’lar: "Detay", "Kaldır"
- Hata Kodları → Mesajlar:
  - 0/5xx library_load_failed → "Kütüphane yüklenemedi."
  - 400/404 remove_failed → "Kaldırma işlemi başarısız."

### Quiz
QuizHomeScreen
- CTA’lar: "Detay", "Başla", "Ara", "Filtrele"
- Hata Kodları → Mesajlar: 0/5xx genel hata mesajı

QuizDetailScreen
- CTA’lar: "Başla", "Liderlik", "Arkadaşa meydan oku", "Paylaş"
- Hata Kodları → Mesajlar:
  - 404 not_found → "Quiz bulunamadı."
  - 5xx stats_error → "İstatistikler yüklenemedi."
  - 5xx leaderboard_error → "Liderlik tablosu yüklenemedi."

QuizPlayScreen
- CTA’lar: "Cevapla", "Sonraki", "Çık"
- Hata Kodları → Mesajlar:
  - 400/403 session_start_failed → "Oturum başlatılamadı."
  - 400/500 answer_submit_failed → "Cevap gönderilemedi."
  - 0 network_error → "Bağlantı sorunu."

QuizResultScreen
- CTA’lar: "Çözümleri incele", "Yeniden dene", "Paylaş", "Liderlik"
- Hata Kodları → Mesajlar:
  - 202 not_ready → "Sonuçlar hazırlanıyor."
  - 404 result_not_found → "Sonuç bulunamadı."
  - 0/5xx → "Sonuçlar yüklenemedi."

QuizLeaderboardScreen
- CTA’lar: "Arkadaşları davet et", "Tekrar dene"
- Hata Kodları → Mesajlar:
  - 0/5xx leaderboard_load_failed → "Liderlik tablosu yüklenemedi."

### Modals
LoginModalScreen
- CTA’lar: "Giriş yap", "Misafir olarak devam et"
- Hata Kodları → Mesajlar: Auth ile aynı

GuestLimitModalScreen
- CTA’lar: "Giriş yap", "Kayıt ol"
- Durum: limit_reached → "Misafir limiti doldu. Devam etmek için giriş yapın."
