# Component Mapping (Ekran -> UI Bileşenleri)

Mevcut UI katmanı: `kpssplus/src/components/ui/*` ve tema `kpssplus/src/theme/*`.

## Temel Bileşenler
- Button: `components/ui/Button.tsx` (primary/secondary/ghost, full-width varyantları)
- Card: `components/ui/Card.tsx` (liste/grid öğeleri, header/body/footer slot)
- Input: `components/ui/Input.tsx` (text/password/search)
- Icon: `components/ui/Icon.tsx` (Ionicons wrapper)
- LoadingSpinner: `components/ui/LoadingSpinner.tsx` (global/inline)
- SocialButton: `components/ui/SocialButton.tsx` (Google/Apple)

Notlar:
- Chip/Tag: Şimdilik küçük `Button` pill stili ile gösterilebilir.
- Toast/Notification: Panel FE’de var; mobilde `Toast` benzeri hafif bir component eklenebilir (bilgi amaçlı).

## Ekran Bazında
- Auth (Login/Register/Forgot/Reset/OTP)
  - Input, <PERSON><PERSON> (primary), SocialButton, LoadingSpinner, Icon (geri)
- ContentHomeScreen
  - Input (search), Button (filter), Card (content), Icon (tags), LoadingSpinner
- ContentSearchScreen
  - Input, Button (filter/reset), Card, LoadingSpinner
- ContentDetailScreen
  - Card (hero/info), Button (primary: Oynat, secondary: Kütüphane/Paylaş), Icon (meta), LoadingSpinner
- ContentPlayerScreen
  - Button (Tamamlandı/Not kaydet), LoadingSpinner
- ContentLibraryScreen
  - Button (tab switch opsiyonel), Card (progress), Icon, LoadingSpinner
- TopicList/TopicDetail/StudyTopics
  - Card (topic), Button (giriş/başlat), Icon
- QuizHomeScreen
  - Input (search), Button (filter), Card (quiz), LoadingSpinner
- QuizDetailScreen
  - Card (stats), Button (Başla/Liderlik/MeydanOku), Icon
- QuizPlayScreen
  - Button (Cevapla/Sonraki), LoadingSpinner, Icon (timer)
- QuizResultScreen
  - Card (score/summary), Button (İncele/Yeniden/Paylaş), Icon
- QuizLeaderboardScreen
  - Card/List item (row), Icon (rank), LoadingSpinner
 - AssistantQuizScreen
   - Button (Cevapla/Atla/İpucu/Tekrar oku), Icon (mic/volume), LoadingSpinner, Card (soru)
- BattleHomeScreen
  - Card (istatistik), Card (list özetleri), Button (Grup oluştur), Icon
- GroupBattleScreen (Liste/Detay/Lobby)
  - Card (battle), Button (Katıl/Ayrıl/Başlat/Kod üret), Icon (status)
- GroupBattleResults
  - Card/Table rows, Icon (trophy), Button (Tekrar oyna)
- FriendChallengeScreen
  - Card (challenge), Button (Kabul/Red), Icon (status)
- SocialHome/TimelineScreen
  - Card (post), Button (Beğen/Yorum/Paylaş), Icon (etkileşim), LoadingSpinner
- PostDetailScreen
  - Card (post), Input (yorum), Button (Gönder)
- CreatePostScreen
  - Input (metin), Button (Medya ekle/Paylaş)
- MenuHomeScreen
  - Card (menu), Icon
- ProfileScreen
  - Card (profile summary), Button (Düzenle/Token mağazası), Icon (rozet)
- SettingsScreen
  - Input (select/switch mock), Button (Kaydet/Çıkış), Icon
- BadgesScreen
  - Card (badge), Icon (kilit)
- Progress/Analytics/Goals
  - Card (grafik/istatistik), Button (Hedef kaydet)
- NotificationsScreen
  - Card (notification), Button (Hepsi okundu), Icon
- Modals (Login, GuestLimit, Share, Filter/Search)
  - Input + Button + Card kombinasyonları, LoadingSpinner
