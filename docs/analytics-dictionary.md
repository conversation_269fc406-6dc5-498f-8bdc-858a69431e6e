# Analytics Etkinlik Sözlüğü

Standart: event adları kebab-case, parametre anahtarları snake_case.

## Ortak Parametreler
- user_id: string (auth ise)
- guest: boolean
- source: 'home' | 'search' | 'detail' | 'library' | 'result' | 'leaderboard' | 'modal'
- error_code: string (hata durumlarında)
- time_spent: number (saniye)
- subject: string
- difficulty: 'easy' | 'medium' | 'hard' | 'beginner' | 'intermediate' | 'advanced'

## Auth
- login_click {method: 'password'|'google'|'apple'|'guest'}
- login_success {method}
- login_fail {method, error_code}
- register_click {}
- register_success {}
- register_fail {error_code}
- forgot_submit {email_domain}
- forgot_success {}
- forgot_fail {error_code}
- reset_submit {}
- reset_success {}
- reset_fail {error_code}

## Content
- content_impression {content_id, position}
- content_card_click {content_id, position, source}
- content_detail_view {content_id}
- content_start {content_id}
- progress_update {content_id, progress}
- content_complete {content_id}
- add_to_library {content_id}
- remove_from_library {content_id}
- filter_open {area: 'content'}
- filter_apply {area: 'content', filters}
- content_search {query_len}
- content_search_result_click {content_id, position}

## Quiz
- quiz_impression {quiz_id, position}
- quiz_card_click {quiz_id, position, source}
- quiz_detail_view {quiz_id}
- quiz_start {quiz_id}
- question_view {quiz_id, question_index}
- answer_submit {quiz_id, question_index, correct}
- quiz_finish {quiz_id, score}
- result_view {quiz_id, result_id}
- leaderboard_view {quiz_id, type: 'global'|'friends'}
- filter_apply {area: 'quiz', filters}
- quiz_search {query_len}
- quiz_search_result_click {quiz_id, position}

### Assistant (Quiz)
- assistant_open {quiz_id}
- assistant_question_read {quiz_id, question_index}
- assistant_voice_answer {quiz_id, question_index, correct}
- assistant_hint_click {quiz_id, question_index}
- assistant_skip {quiz_id, question_index}
- assistant_end {quiz_id, score}

## Battle
- battle_home_view {}
- group_join {battle_id}
- group_start {battle_id}
- invite_generate {battle_id}
- group_results_view {battle_id}
- challenge_send {challenge_id, quiz_id}
- challenge_accept {challenge_id}
- challenge_decline {challenge_id}

## Sosyal
- feed_view {}
- post_like {post_id}
- comment_add {post_id}
- post_create_submit {has_media}
- post_view {post_id}
- comment_submit {post_id}

## Menü / Profil / Ayarlar
- menu_open {}
- menu_item_click {item}
- profile_view {profile_id}
- profile_edit_open {}
- settings_save {changed_count}
- logout_click {}

## Token / Bildirimler
- token_purchase_click {package_id}
- token_purchase_success {package_id}
- daily_bonus_claim {tokens_earned}
- notifications_view {unread_count}
- notification_click {notification_id}
