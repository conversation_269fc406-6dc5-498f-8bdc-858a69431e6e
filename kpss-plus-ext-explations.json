{"name": "kpss-plus-ext-explations", "nodes": [{"parameters": {"httpMethod": "POST", "path": "kpss-not-extract", "authentication": "headerAuth", "responseMode": "responseNode", "options": {"allowedOrigins": "*", "binaryPropertyName": "data"}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [-784, 32], "name": "Webhook", "id": "db0d2b33-8381-4f9a-9c81-2f8edbc36813", "webhookId": "f6c39d8e-afad-4971-905b-aeb7bffdd939", "credentials": {"httpHeaderAuth": {"id": "dhOYahHCnfBvBV6G", "name": "kpss-client-id"}}}, {"parameters": {"operation": "pdf", "binaryPropertyName": "data0", "options": {}}, "id": "6b6541a1-5eb8-4d0c-84a5-830c96d8af43", "name": "Extract data from PDF", "type": "n8n-nodes-base.extractFromFile", "position": [-352, -80], "typeVersion": 1}, {"parameters": {"method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpBearerAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"openrouter/sonoma-dusk-alpha\",\n  \"messages\": [\n    {\n      \"role\": \"user\",\n      \"content\": \"You are given a KPSS course notes PDF text.\\n\\nTEXT:\\n{{encodeURIComponent($json.text)}}\\n\\nTASK:\\nExtract a clean topic hierarchy and brief summaries. Return ONLY JSON (no markdown, no extra text) in EXACTLY this schema:\\n{\\n  \\\"ders_notlari\\\": [\\n    {\\n      \\\"ust_konu\\\": string,\\n      \\\"alt_basliklar\\\": [\\n        { \\\"baslik\\\": string, \\\"ozet\\\": string }\\n      ]\\n    }\\n  ]\\n}\\n\\nRULES:\\n- Detect each top-level topic heading as \\\"ust_konu\\\".\\n- Under each \\\"ust_konu\\\", list subheadings as objects with \\\"baslik\\\" and a 1-3 sentence \\\"ozet\\\" (concise, no bullet dumps).\\n- Remove page numbers, headers/footers, and noise.\\n- Turkish output.\\n- Strictly output valid JSON matching the schema above.\\n\"\n    }\n  ]\n}", "options": {}}, "name": "Send data to A.I.", "type": "n8n-nodes-base.httpRequest", "position": [-112, -80], "typeVersion": 4.2, "alwaysOutputData": false, "id": "aa858688-0d88-413b-8fe8-60d505d06ca1", "credentials": {"httpBearerAuth": {"id": "e5AwOP7g0u0tDVL9", "name": "Bearer Auth account"}}}, {"parameters": {"functionCode": "const content = $json?.choices?.[0]?.message?.content || \"{\\\"ders_notlari\\\":[]}\";\nlet parsed;\ntry { parsed = JSON.parse(content); } catch (_) { parsed = { ders_notlari: [] }; }\n// Güvenlik: Şema yoksa sar.\nif (!parsed || typeof parsed !== 'object' || !Array.isArray(parsed.ders_notlari)) {\n  parsed = { ders_notlari: [] };\n}\nreturn [{ json: parsed }];"}, "name": "Parse AI JSON", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [128, -80], "id": "8b4851ef-8525-4e49-97c4-db2000ce4de7"}, {"parameters": {"options": {}}, "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [368, -80], "id": "fa136670-8747-40b8-92c2-d68bce16ccce"}], "pinData": {}, "connections": {"Webhook": {"main": [[{"node": "Extract data from PDF", "type": "main", "index": 0}]]}, "Extract data from PDF": {"main": [[{"node": "Send data to A.I.", "type": "main", "index": 0}]]}, "Send data to A.I.": {"main": [[{"node": "Parse AI JSON", "type": "main", "index": 0}]]}, "Parse AI JSON": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "af62cacd-443d-4abd-8099-ea62b91e3714", "meta": {"templateCredsSetupCompleted": true, "instanceId": "01181dd32c1d600f681273a62447cf69148de8786915b328566271e214f82f6f"}, "id": "jHdXwS9XNvj7LgLj", "tags": []}